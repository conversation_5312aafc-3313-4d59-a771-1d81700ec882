##------------------------define.vm----------------------
##（Velocity宏定义）

##定义设置表名后缀的宏定义，调用方式：#setTableSuffix("Test")
#macro(setTableSuffix $suffix)
    #set($tableName = $!tool.append($tableInfo.name, $suffix))
#end

##定义设置包名后缀的宏定义，调用方式：#setPackageSuffix("Test")
#macro(setPackageSuffix $suffix)
#if($suffix!="")package #end#if($tableInfo.savePackageName!="")$!{tableInfo.savePackageName}.#{end}$!suffix;
#end

##定义直接保存路径与文件名简化的宏定义，调用方式：#save("/entity", ".java")
#macro(save $path $fileName)
    $!callback.setSavePath($tool.append($tableInfo.savePath, $path))
    $!callback.setFileName($tool.append($tableInfo.name, $fileName))
#end

##定义表注释的宏定义，调用方式：#tableComment("注释信息")
#macro(tableComment $desc)
/**
 * $!{tableInfo.comment}($!{tableInfo.name})$desc
 *
 * <AUTHOR>
 * @since $!time.currTime()
 */
#end

##定义GET，SET方法的宏定义，调用方式：#getSetMethod($column)
#macro(getSetMethod $column)

    public $!{tool.getClsNameByFullName($column.type)} get$!{tool.firstUpperCase($column.name)}() {
        return $!{column.name};
    }

    public void set$!{tool.firstUpperCase($column.name)}($!{tool.getClsNameByFullName($column.type)} $!{column.name}) {
        this.$!{column.name} = $!{column.name};
    }
#end

##------------------------mybatis.vm----------------------
##针对Mybatis 进行支持，主要用于生成xml文件
#foreach($column in $tableInfo.fullColumn)
    ##储存列类型
    $tool.call($column.ext.put("sqlType", $tool.getField($column.obj.dataType, "typeName")))
    #if($tool.newHashSet("java.lang.String").contains($column.type))
        #set($jdbcType="VARCHAR")
    #elseif($tool.newHashSet("java.lang.Boolean", "boolean").contains($column.type))
        #set($jdbcType="BOOLEAN")
    #elseif($tool.newHashSet("java.lang.Byte", "byte").contains($column.type))
        #set($jdbcType="BYTE")
    #elseif($tool.newHashSet("java.lang.Integer", "int", "java.lang.Short", "short").contains($column.type))
        #set($jdbcType="INTEGER")
    #elseif($tool.newHashSet("java.lang.Long", "long").contains($column.type))
        #set($jdbcType="INTEGER")
    #elseif($tool.newHashSet("java.lang.Float", "float", "java.lang.Double", "double").contains($column.type))
        #set($jdbcType="NUMERIC")
    #elseif($tool.newHashSet("java.util.Date", "java.sql.Timestamp", "java.time.Instant", "java.time.LocalDateTime", "java.time.OffsetDateTime", "	java.time.ZonedDateTime").contains($column.type))
        #set($jdbcType="TIMESTAMP")
    #elseif($tool.newHashSet("java.sql.Date", "java.time.LocalDate").contains($column.type))
        #set($jdbcType="TIMESTAMP")
    #else
        ##其他类型
        #set($jdbcType="VARCHAR")
    #end
    $tool.call($column.ext.put("jdbcType", $jdbcType))
#end

##定义宏，查询所有列
#macro(allSqlColumn)#foreach($column in $tableInfo.fullColumn)$column.obj.name#if($foreach.hasNext), #end#end#end

##------------------自定义-------------------

##entity类名
#set($entityName = $tableInfo.name.toUpperCase())
##entity 实例名
#set($entityInstanceName = $tableInfo.name.toLowerCase())

#if(!$tableInfo.pkColumn.isEmpty())
    ##表主键数据
    #set($pk = $tableInfo.pkColumn.get(0))
    
    ##主键第一个字母大写
    #set($pkFirstUpperCase =$tool.firstUpperCase($pk.obj.name.toLowerCase()))
#end

#set($baowuViewDir =$projectPath+"/src/main/resources/META-INF/resources/"+$entityName.substring(0,2)+"/"+$entityName.substring(2,4)+"/")
#set($baowuJavaDir =$projectPath+"/src/main/java/com/baosight/imc/"+$entityName.substring(0,2).toLowerCase()+"/"+$entityName.substring(2,4).toLowerCase()+"/")
#set($baowuTestDir =$projectPath+"/src/test/java/com/baosight/imc/"+$entityName.substring(0,2).toLowerCase()+"/"+$entityName.substring(2,4).toLowerCase()+"/")

#if($tableInfo.savePath=="")
    $!callback.setSavePath($baowuJavaDir)
#end
import requests
import json
import time
import sys
import base64
import os
import asyncio
from crawl4ai import AsyncWebCrawler
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

async def extract_with_multiple_strategies(url):
    """使用多种策略尝试提取内容"""
    print(f"正在爬取: {url}")

    async with AsyncWebCrawler(verbose=True) as crawler:

        # 策略1: 先获取基本页面内容
        print("\n=== 策略1: 获取基本页面内容 ===")
        basic_result = await crawler.arun(url=url)

        print(f"页面状态码: {basic_result.status_code}")
        print(f"页面标题: {basic_result.metadata.get('title', 'N/A')}")
        print(f"HTML长度: {len(basic_result.html) if basic_result.html else 0}")
        print(f"清理后内容长度: {len(basic_result.cleaned_html) if basic_result.cleaned_html else 0}")

        if basic_result.cleaned_html:
            print(f"页面文本预览: {basic_result.cleaned_html[:300]}...")

        # 策略2: 尝试原始选择器
        print("\n=== 策略2: 原始选择器 ===")
        try:
            original_schema = {
                "name": "original",
                "baseSelector": ".articleDetailContent",
                "fields": [
                    {"name": "title", "selector": "h2", "type": "text"},
                    {"name": "content", "selector": "p", "type": "text"}
                ],
            }
            strategy = JsonCssExtractionStrategy(original_schema)
            result = await crawler.arun(url=url, extraction_strategy=strategy)

            if result.extracted_content:
                print("✓ 原始选择器成功提取内容:")
                print(result.extracted_content)
                return result.extracted_content
            else:
                print("✗ 原始选择器未提取到内容")
        except Exception as e:
            print(f"✗ 原始选择器出错: {e}")

        # 策略3: 更宽泛的选择器
        print("\n=== 策略3: 宽泛选择器 ===")
        broad_schemas = [
            {
                "name": "article_tag",
                "baseSelector": "article",
                "fields": [
                    {"name": "title", "selector": "h1, h2, h3", "type": "text"},
                    {"name": "content", "selector": "p", "type": "text"}
                ]
            },
            {
                "name": "content_class",
                "baseSelector": ".content, .main-content, .article-content",
                "fields": [
                    {"name": "title", "selector": "h1, h2, h3", "type": "text"},
                    {"name": "content", "selector": "p", "type": "text"}
                ]
            },
            {
                "name": "body_all",
                "baseSelector": "body",
                "fields": [
                    {"name": "headings", "selector": "h1, h2, h3, h4", "type": "text"},
                    {"name": "paragraphs", "selector": "p", "type": "text"}
                ]
            }
        ]

        for schema in broad_schemas:
            try:
                strategy = JsonCssExtractionStrategy(schema)
                result = await crawler.arun(url=url, extraction_strategy=strategy)

                if result.extracted_content:
                    print(f"✓ {schema['name']} 成功提取:")
                    content = result.extracted_content
                    print(content)
                    return content
                else:
                    print(f"✗ {schema['name']} 未提取到内容")
            except Exception as e:
                print(f"✗ {schema['name']} 出错: {e}")

        # 策略4: 如果所有CSS策略都失败，返回清理后的文本
        print("\n=== 策略4: 返回清理后的文本 ===")
        if basic_result.cleaned_html:
            print("返回页面的清理后文本内容")
            # 简单提取标题和段落
            import re

            # 提取可能的标题
            title_match = re.search(r'<title[^>]*>([^<]+)</title>', basic_result.html, re.IGNORECASE)
            title = title_match.group(1) if title_match else "未找到标题"

            # 返回结构化数据
            fallback_content = {
                "title": title,
                "content": basic_result.cleaned_html[:2000],  # 前2000字符
                "full_text": basic_result.cleaned_html
            }

            print(f"标题: {title}")
            print(f"内容预览: {basic_result.cleaned_html[:500]}...")

            return json.dumps(fallback_content, ensure_ascii=False, indent=2)

        print("❌ 所有策略都失败，无法提取内容")
        return None

async def save_results(content, filename="extraction_results.json"):
    """保存提取结果"""
    if content:
        with open(filename, "w", encoding="utf-8") as f:
            if isinstance(content, str):
                f.write(content)
            else:
                json.dump(content, f, ensure_ascii=False, indent=2)
        print(f"结果已保存到: {filename}")

async def main():
    url = "https://36kr.com/p/3311645093895936"

    # 提取内容
    extracted_content = await extract_with_multiple_strategies(url)

    if extracted_content:
        print("\n=== 最终提取结果 ===")
        print(extracted_content)

        # 保存结果
        await save_results(extracted_content)
    else:
        print("\n❌ 未能提取到任何内容")

        # 提供一些建议
        print("\n建议:")
        print("1. 检查网站是否有反爬虫保护")
        print("2. 尝试使用不同的URL")
        print("3. 检查网站是否需要JavaScript渲染")
        print("4. 运行 debug_crawler.py 来分析页面结构")

if __name__ == "__main__":
    asyncio.run(main())
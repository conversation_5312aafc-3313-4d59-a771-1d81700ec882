<?xml version="1.0" ?>
<testsuite name="FlameAPITester-20250611102611" tests="2" time="102.018" failures="0" errors="2">
	<testcase classname="FlameAPITester" name="test_connection" time="1.002">
		<error type="AssertionError" message="unexpectedly None"><![CDATA[Traceback (most recent call last):
  File "c:\vs_project\test\智能开户\test\test2.py", line 110, in test_connection
    self.assertIsNotNone(ws.sock)
AssertionError: unexpectedly None
]]></error>
	</testcase>
	<testcase classname="FlameAPITester" name="test_planning_agent" time="101.016">
		<error type="AssertionError" message="False is not true"><![CDATA[Traceback (most recent call last):
  File "c:\vs_project\test\智能开户\test\test2.py", line 130, in test_planning_agent
    self.assertTrue(valid_response)
AssertionError: False is not true
]]></error>
	</testcase>
	<system-out><![CDATA[### 连接已关闭 ###
### 连接已关闭 ###
]]></system-out>
	<system-err><![CDATA[]]></system-err>
</testsuite>

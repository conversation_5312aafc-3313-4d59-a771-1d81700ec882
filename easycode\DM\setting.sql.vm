## 引入宏定义
$!{baowu.vm}

## 使用宏定义设置回调（保存位置与文件）
$!callback.setSavePath("C:\\bwwp\\bwsql")
$!callback.setFileName($entityName+".sql")

INSERT INTO IPLAT4J.TEDFA00 (REC_CREATOR,REC_CREATE_TIME,REC_REVISOR,REC_REVISE_TIME,FORM_ENAME,FORM_CNAME,FORM_LOAD_PATH,FORM_TYPE,MODULE_ENAME_1,MODULE_ENAME_2,INIT_LOAD_SERVICE_ENAME,IS_AUTH,FORM_PARAM,SUBAPP_CODE,ICON_INFO,BUSINESS_CATEGORY,OPERATE_TYPE,TENANT_ID,ARCHIVE_FLAG) VALUES ('KF2269','${time.currTime('yyyyMMddHHmmss')}',' ',' ','${entityName}','${tableInfo.comment}',' ','0','${entityName.substring(0,2)}','${entityName.substring(2,4)}',' ','0',' ',' ',' ',' ','query',' ',' ');
INSERT INTO IPLAT4J.TEDFA01 (REC_CREATOR,REC_CREATE_TIME,REC_REVISOR,REC_REVISE_TIME,ARCHIVE_FLAG,FORM_ENAME,REGION_ID,BUTTON_ENAME,BUTTON_CNAME,BUTTON_DESC,NODE_SORT_ID,IS_AUTH,URI,LAYOUT,"POSITION",TENANT_ID,BUSINESS_CATEGORY,OPERATE_TYPE) VALUES ('KF2269','${time.currTime('yyyyMMddHHmmss')}','KF2269','${time.currTime('yyyyMMddHHmmss')}',' ','${entityName}','GRID:EF_GRID_RESULT','DELETE','删除',' ','3','0',' ','0','0',' ',' ','delete');
INSERT INTO IPLAT4J.TEDFA01 (REC_CREATOR,REC_CREATE_TIME,REC_REVISOR,REC_REVISE_TIME,ARCHIVE_FLAG,FORM_ENAME,REGION_ID,BUTTON_ENAME,BUTTON_CNAME,BUTTON_DESC,NODE_SORT_ID,IS_AUTH,URI,LAYOUT,"POSITION",TENANT_ID,BUSINESS_CATEGORY,OPERATE_TYPE) VALUES ('KF2269','${time.currTime('yyyyMMddHHmmss')}','KF2269','${time.currTime('yyyyMMddHHmmss')}',' ','${entityName}','GRID:EF_GRID_RESULT','INSERT','新增',' ','1','0','css:k-add','2','0',' ',' ','add');
INSERT INTO IPLAT4J.TEDFA01 (REC_CREATOR,REC_CREATE_TIME,REC_REVISOR,REC_REVISE_TIME,ARCHIVE_FLAG,FORM_ENAME,REGION_ID,BUTTON_ENAME,BUTTON_CNAME,BUTTON_DESC,NODE_SORT_ID,IS_AUTH,URI,LAYOUT,"POSITION",TENANT_ID,BUSINESS_CATEGORY,OPERATE_TYPE) VALUES ('KF2269','${time.currTime('yyyyMMddHHmmss')}','KF2269','${time.currTime('yyyyMMddHHmmss')}',' ','${entityName}','GRID:EF_GRID_RESULT','INSERTSAVE','新增',' ','1','0','','0','0',' ',' ','insert');
INSERT INTO IPLAT4J.TEDFA01 (REC_CREATOR,REC_CREATE_TIME,REC_REVISOR,REC_REVISE_TIME,ARCHIVE_FLAG,FORM_ENAME,REGION_ID,BUTTON_ENAME,BUTTON_CNAME,BUTTON_DESC,NODE_SORT_ID,IS_AUTH,URI,LAYOUT,"POSITION",TENANT_ID,BUSINESS_CATEGORY,OPERATE_TYPE) VALUES ('KF2269','${time.currTime('yyyyMMddHHmmss')}','KF2269','${time.currTime('yyyyMMddHHmmss')}',' ','${entityName}','INQU','QUERY','查询',' ','1','0',' ','0','0',' ',' ','query');
INSERT INTO IPLAT4J.TEDFA01 (REC_CREATOR,REC_CREATE_TIME,REC_REVISOR,REC_REVISE_TIME,ARCHIVE_FLAG,FORM_ENAME,REGION_ID,BUTTON_ENAME,BUTTON_CNAME,BUTTON_DESC,NODE_SORT_ID,IS_AUTH,URI,LAYOUT,"POSITION",TENANT_ID,BUSINESS_CATEGORY,OPERATE_TYPE) VALUES ('KF2269','${time.currTime('yyyyMMddHHmmss')}','KF2269','${time.currTime('yyyyMMddHHmmss')}',' ','${entityName}','INQU','REST','重置',' ','2','0',' ','0','0',' ',' ','query');
INSERT INTO IPLAT4J.TEDFA01 (REC_CREATOR,REC_CREATE_TIME,REC_REVISOR,REC_REVISE_TIME,ARCHIVE_FLAG,FORM_ENAME,REGION_ID,BUTTON_ENAME,BUTTON_CNAME,BUTTON_DESC,NODE_SORT_ID,IS_AUTH,URI,LAYOUT,"POSITION",TENANT_ID,BUSINESS_CATEGORY,OPERATE_TYPE) VALUES ('KF2269','${time.currTime('yyyyMMddHHmmss')}','KF2269','${time.currTime('yyyyMMddHHmmss')}',' ','${entityName}','GRID:EF_GRID_RESULT','UPDATESAVE','修改',' ','2','0',' ','0','0',' ',' ','update');

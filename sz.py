import pandas as pd
import json

# 文件路径
file_path = r"C:\Users\<USER>\Documents\数智.xlsx"

# 读取 Excel 文件
df = pd.read_excel(file_path)

if '答' in df.columns:
    # 提取 "content" 内容到新列 "答内容"
    def extract_content(x):
        if isinstance(x, str):
            try:
                data = json.loads(x)
                contents = data.get('contents', [])
                if contents and isinstance(contents, list) and len(contents) > 0:
                    content_dict = contents[0].get('content', {})
                    return content_dict.get('content', None)
            except json.JSONDecodeError:
                
                pass
            except Exception as e:
                print(x)
                # exit()
        return None

    df['答内容'] = df['答'].apply(extract_content)
else:
    print("列 '答' 不存在于文件中")

# 显示前几行数据
# print(df.head())

# 保存到新文件
output_file_path = r"C:\Users\<USER>\Documents\数智_处理后.xlsx"
df.to_excel(output_file_path, index=False)
<?xml version="1.0" ?>
<testsuite name="FlameAPITester-20250611163632" tests="1" time="2.003" failures="0" errors="0">
	<testcase classname="FlameAPITester" name="test_planning_agent" time="2.003"/>
	<system-out><![CDATA[开始测试规划型智能体...
构建的payload: {
  "header": {
    "traceId": "TRACE-1749630992-7fbf0052",
    "bodyId": "xzrbgent6ue6b0btaorwxi5phhbyld3d",
    "appId": "2741701DAF894C02BA78",
    "mode": 0
  },
  "parameter": {},
  "payload": {
    "sessionId": "SESSION_001",
    "text": [
      {
        "content": "开户申请",
        "content_type": "text",
        "role": "user"
      }
    ]
  }
}
signed_str=host: **********
date: Wed, 11 Jun 2025 08:36:32 GMT
GET /openapi/flames/api/v2/chat HTTP/1.1
############hmac api_key="2741701DAF894C02BA78",algorithm="hmac-sha256",headers="host date request-line",signature="e9oiOMkg3Z4SlIp/+bANsUOJ+X7spdqeGgUsZm4SGkA="
############aG1hYyBhcGlfa2V5PSIyNzQxNzAxREFGODk0QzAyQkE3OCIsYWxnb3JpdGhtPSJobWFjLXNoYTI1NiIsaGVhZGVycz0iaG9zdCBkYXRlIHJlcXVlc3QtbGluZSIsc2lnbmF0dXJlPSJlOW9pT01rZzNaNFNsSXAvK2JBTnNVT0orWDdzcGRxZUdnVXNabTRTR2tBPSI=
------------ws.url---ws://**********:30009/openapi/flames/api/v2/chat?host=**********&date=Wed%2C+11+Jun+2025+08%3A36%3A32+GMT&authorization=aG1hYyBhcGlfa2V5PSIyNzQxNzAxREFGODk0QzAyQkE3OCIsYWxnb3JpdGhtPSJobWFjLXNoYTI1NiIsaGVhZGVycz0iaG9zdCBkYXRlIHJlcXVlc3QtbGluZSIsc2lnbmF0dXJlPSJlOW9pT01rZzNaNFNsSXAvK2JBTnNVT0orWDdzcGRxZUdnVXNabTRTR2tBPSI%3D&bodyId=xzrbgent6ue6b0btaorwxi5phhbyld3d
------------ws.head---[]
发送消息: {"header": {"traceId": "TRACE-1749630992-7fbf0052", "bodyId": "xzrbgent6ue6b0btaorwxi5phhbyld3d", "appId": "2741701DAF894C02BA78", "mode": 0}, "parameter": {}, "payload": {"sessionId": "SESSION_001", "text": [{"content": "开户申请", "content_type": "text", "role": "user"}]}}
收到原始消息: {"header":{"code":-1,"message":"Data Not Found","status":2,"sid":"TALKOEYYNKQRQJCOP","traceId":"TRACE-1749630992-7fbf0052"},"payload":{}}
解析后的JSON数据: {'header': {'code': -1, 'message': 'Data Not Found', 'status': 2, 'sid': 'TALKOEYYNKQRQJCOP', 'traceId': 'TRACE-1749630992-7fbf0052'}, 'payload': {}}
### 连接已关闭 ### 状态码: 1000, 消息: 
等待服务器响应...
收到响应: {'header': {'code': -1, 'message': 'Data Not Found', 'status': 2, 'sid': 'TALKOEYYNKQRQJCOP', 'traceId': 'TRACE-1749630992-7fbf0052'}, 'payload': {}}
响应类型: dict, 键: ['header', 'payload']
找到有效响应内容

测试结果:
- 总响应数: 1
- 是否有错误: False
- 是否有有效响应: True
- 测试用时: 0.00秒
- 所有响应: [{'header': {'code': -1, 'message': 'Data Not Found', 'status': 2, 'sid': 'TALKOEYYNKQRQJCOP', 'traceId': 'TRACE-1749630992-7fbf0052'}, 'payload': {}}]
]]></system-out>
	<system-err><![CDATA[]]></system-err>
</testsuite>

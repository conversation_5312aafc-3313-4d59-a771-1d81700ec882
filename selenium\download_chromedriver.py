import os
import sys
import zipfile
import requests
import platform
import subprocess
from pathlib import Path

def get_chrome_version():
    """Get the installed Chrome version."""
    system = platform.system()

    if system == "Windows":
        try:
            # Try to get Chrome version from registry
            import winreg
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Google\Chrome\BLBeacon")
            version, _ = winreg.QueryValueEx(key, "version")
            return version
        except:
            # Fallback to checking the Chrome executable
            chrome_path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"
            if os.path.exists(chrome_path):
                try:
                    from win32com.client import Dispatch
                    parser = Dispatch("Scripting.FileSystemObject")
                    version = parser.GetFileVersion(chrome_path)
                    return version
                except:
                    # If win32com is not available, try using PowerShell
                    try:
                        result = subprocess.check_output(
                            ['powershell', '-command', f'(Get-Item "{chrome_path}").VersionInfo.ProductVersion'],
                            stderr=subprocess.STDOUT
                        )
                        return result.decode('utf-8').strip()
                    except:
                        pass

    elif system == "Linux":
        try:
            result = subprocess.check_output(['google-chrome', '--version'])
            return result.decode('utf-8').strip().split()[-1]
        except:
            pass

    elif system == "Darwin":  # macOS
        try:
            result = subprocess.check_output(['/Applications/Google Chrome.app/Contents/MacOS/Google Chrome', '--version'])
            return result.decode('utf-8').strip().split()[-1]
        except:
            pass

    return None

def download_chromedriver(version=None):
    """Download the appropriate ChromeDriver for the installed Chrome version."""
    system = platform.system()
    drivers_dir = Path("drivers")
    drivers_dir.mkdir(exist_ok=True)

    if not version:
        version = get_chrome_version()

    if not version:
        print("Could not determine Chrome version. Please specify it manually.")
        return None

    # Get major version
    major_version = version.split('.')[0]

    # Determine platform
    if system == "Windows":
        platform_name = "win32"
    elif system == "Linux":
        platform_name = "linux64"
    elif system == "Darwin":
        platform_name = "mac64" if platform.machine() != "arm64" else "mac_arm64"
    else:
        print(f"Unsupported platform: {system}")
        return None

    # Try to download from Chrome for Testing
    try:
        # First, try to get the latest driver for this major version
        print(f"Trying to download ChromeDriver for Chrome version {major_version}...")

        # For very recent Chrome versions, try the Chrome for Testing API
        try:
            print("Trying Chrome for Testing API...")
            cft_url = "https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json"
            response = requests.get(cft_url)

            if response.status_code == 200:
                data = response.json()
                # Find the latest version for this major version
                matching_versions = []

                for version_info in data.get("versions", []):
                    version_str = version_info.get("version", "")
                    if version_str.startswith(f"{major_version}."):
                        matching_versions.append(version_info)

                if matching_versions:
                    # Sort by version and get the latest
                    latest_version = sorted(matching_versions, key=lambda x: x.get("version", ""))[-1]
                    version_str = latest_version.get("version")

                    # Find the chromedriver download
                    for download in latest_version.get("downloads", {}).get("chromedriver", []):
                        if download.get("platform") == platform_name:
                            download_url = download.get("url")

                            if download_url:
                                # Download path
                                zip_path = drivers_dir / f"chromedriver_{platform_name}.zip"

                                # Download the file
                                print(f"Downloading ChromeDriver {version_str} from {download_url}...")
                                response = requests.get(download_url)

                                if response.status_code == 200:
                                    with open(zip_path, 'wb') as f:
                                        f.write(response.content)

                                    # Extract the zip file
                                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                                        zip_ref.extractall(drivers_dir)

                                    # The Chrome for Testing zip has a different structure
                                    chromedriver_dir = drivers_dir / f"chromedriver-{platform_name}"
                                    if os.path.exists(chromedriver_dir):
                                        if system == "Windows":
                                            driver_path = chromedriver_dir / "chromedriver.exe"
                                        else:
                                            driver_path = chromedriver_dir / "chromedriver"
                                            os.chmod(driver_path, 0o755)

                                        print(f"ChromeDriver downloaded and extracted to {driver_path}")
                                        return driver_path
        except Exception as e:
            print(f"Chrome for Testing API failed: {e}")

        # Fallback to the old method
        # URL for the latest driver version
        url = f"https://chromedriver.storage.googleapis.com/LATEST_RELEASE_{major_version}"
        response = requests.get(url)

        if response.status_code == 200:
            driver_version = response.text.strip()

            # Download URL
            download_url = f"https://chromedriver.storage.googleapis.com/{driver_version}/chromedriver_{platform_name}.zip"

            # Download path
            zip_path = drivers_dir / f"chromedriver_{platform_name}.zip"

            # Download the file
            print(f"Downloading ChromeDriver {driver_version} from {download_url}...")
            response = requests.get(download_url)

            if response.status_code == 200:
                with open(zip_path, 'wb') as f:
                    f.write(response.content)

                # Extract the zip file
                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                    zip_ref.extractall(drivers_dir)

                # Make the driver executable on Unix systems
                if system != "Windows":
                    driver_path = drivers_dir / "chromedriver"
                    os.chmod(driver_path, 0o755)
                else:
                    driver_path = drivers_dir / "chromedriver.exe"

                print(f"ChromeDriver downloaded and extracted to {driver_path}")
                return driver_path
    except Exception as e:
        print(f"Error downloading ChromeDriver: {e}")

    # Last resort: try to download the latest stable version
    try:
        print("Trying to download the latest stable ChromeDriver...")
        url = "https://chromedriver.storage.googleapis.com/LATEST_RELEASE"
        response = requests.get(url)

        if response.status_code == 200:
            driver_version = response.text.strip()

            # Download URL
            download_url = f"https://chromedriver.storage.googleapis.com/{driver_version}/chromedriver_{platform_name}.zip"

            # Download path
            zip_path = drivers_dir / f"chromedriver_{platform_name}.zip"

            # Download the file
            print(f"Downloading latest stable ChromeDriver {driver_version} from {download_url}...")
            response = requests.get(download_url)

            if response.status_code == 200:
                with open(zip_path, 'wb') as f:
                    f.write(response.content)

                # Extract the zip file
                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                    zip_ref.extractall(drivers_dir)

                # Make the driver executable on Unix systems
                if system != "Windows":
                    driver_path = drivers_dir / "chromedriver"
                    os.chmod(driver_path, 0o755)
                else:
                    driver_path = drivers_dir / "chromedriver.exe"

                print(f"Latest stable ChromeDriver downloaded and extracted to {driver_path}")
                print("WARNING: This may not be compatible with your Chrome version.")
                return driver_path
    except Exception as e:
        print(f"Error downloading latest stable ChromeDriver: {e}")

    print("All download methods failed. Please download ChromeDriver manually.")
    return None

if __name__ == "__main__":
    chrome_version = get_chrome_version()
    if chrome_version:
        print(f"Detected Chrome version: {chrome_version}")
        driver_path = download_chromedriver(chrome_version)
        if driver_path:
            print(f"ChromeDriver path: {driver_path}")
        else:
            print("Failed to download ChromeDriver")
    else:
        print("Could not detect Chrome version")

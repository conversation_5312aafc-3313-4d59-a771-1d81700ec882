## 引入宏定义
$!{baowu.vm}

## 使用宏定义设置回调（保存位置与文件）
$!callback.setSavePath($tableInfo.savePath+"/service")
$!callback.setFileName("Service"+$entityName+".java")

## 使用宏定义设置包后缀,打印包路径
#setPackageSuffix("service")

## 使用全局变量实现默认包导入
$!{autoImport.vm}
import com.baosight.elim.common.utils.GlobalUtils;
import com.baosight.imc.common.utils.BeanUtil;
import com.baosight.imc.common.utils.FormUtils;
import com.baosight.imc.interfaces.vz.bm.domain.VZBM1300;
import com.baosight.imc.interfaces.vz.bm.service.ServiceVZBM1300;
import com.baosight.imc.xb.ai.domain.XBAI13;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceEPBase;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * ${tableInfo.comment} 服务类
 * @DateTime ${time.currTime('yyyy-MM-dd HH:mm:ss')} 
 * <AUTHOR>
 */
public class Service${entityName} extends ServiceEPBase {

    private static final Logger logger = LoggerFactory.getLogger(Service${entityName}.class);

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return super.initLoad(inInfo, new ${entityName}());
    }

    /**
     * 查询功能
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = super.query(inInfo, "${entityName}.query", new ${entityName}());
        return outInfo;
    }

    /**
     * 新增功能
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo insert(EiInfo inInfo) {

        EiInfo outInfo = new EiInfo();
        List rows = new ArrayList();
        for (int i = 0; i < inInfo.getBlock(EiConstant.resultBlock).getRowCount(); i++) {
            ${entityName} ${entityInstanceName} = new ${entityName}();
            ${entityInstanceName}.fromMap(inInfo.getRow(EiConstant.resultBlock, i));
            BeanUtil.beanAttributeValueTrim(${entityInstanceName});
            FormUtils.setCreatorProertyFromXservice(${entityInstanceName});
            boolean isCheckPass = check${entityName}(${entityInstanceName}, outInfo);
            if (!isCheckPass) {
                if (StringUtils.isNotBlank(outInfo.getMsg())) {
                    outInfo.setMsg(errorString("VZBM0810001", "第" + (i + 1) + "行" + outInfo.getMsg()));
                }
                return outInfo;
            }
            int countDuplication = dao.count("${entityName}.duplication", new HashMap<String, Object>() {{
                #foreach($column in $tableInfo.fullColumn)
                        put("${column.name}", ${entityInstanceName}.get${tool.firstUpperCase($column.name)}());
                #end
            }});
            if (countDuplication > 0) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg(errorString("VZBM0810001", "第" + (i + 1) + "行记录重复！"));
                return outInfo;
            }
            rows.add(${entityInstanceName}.toMap());
        }
        inInfo.getBlock(EiConstant.resultBlock).setRows(rows);

        return insert(inInfo, "${entityName}.insert");
    }

    @Nullable
    private boolean check${entityName}(${entityName} ${entityInstanceName}, EiInfo outInfo) {
        try {
            #foreach($column in $tableInfo.otherColumn)
                #if($column.obj.isNotNull())
                    ${column.shortType} ${column.name} = ${entityInstanceName}.get${tool.firstUpperCase($column.name)}();
                    #if($column.shortType=="String")
                    if (StringUtils.isEmpty(${column.name})) {
                    #else
                    if (Objects.isNull(${column.name})) {
                    #end
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("${column.comment}不能为空！");
                        return false;
                    }
                #end
                    #if($column.shortType=="String")
                    if (${column.name}.getBytes("gbk").length > ${column.obj.getDataType().getLength()}) {
                        outInfo.setStatus(EiConstant.STATUS_FAILURE);
                        outInfo.setMsg("${column.comment}最大为${column.obj.getDataType().getLength()}个文字！");
                        return false;
                    }
                    #end
            #end

        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return true;
    }

    /**
     * @param code
     * @param msg
     * @return
     */
    private String errorString(String code, String msg) {
        VZBM1300 queryMsg = new VZBM1300();
        queryMsg.setErrorNum(code);
        queryMsg.setFormEname("${entityName}");
        GlobalUtils.setCreatorProerty(queryMsg);
        return ServiceVZBM1300.getMessageTextByErrorNumAndRecord(new String[]{msg}, queryMsg);
    }

    /**
     * 修改功能
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo update(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();

        for (int i = 0; i < inInfo.getBlock(EiConstant.resultBlock).getRowCount(); i++) {
            ${entityName} ${entityInstanceName} = new ${entityName}();
            ${entityInstanceName}.fromMap(inInfo.getRow(EiConstant.resultBlock, i));
            BeanUtil.beanAttributeValueTrim(${entityInstanceName});
            FormUtils.setCreatorProertyFromXservice(${entityInstanceName});
            boolean isCheckPass = check${entityName}(${entityInstanceName}, outInfo);
            if (!isCheckPass) {
                if (StringUtils.isNotBlank(outInfo.getMsg())) {
                    outInfo.setMsg(errorString("VZBM0810001", "第" + (i + 1) + "行" + outInfo.getMsg()));
                }
                return outInfo;
            }
            if (StringUtils.isEmpty(${entityInstanceName}.get${pkFirstUpperCase}())) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("记录$!pk.obj.name为空！");
                return outInfo;
            }
            int countDuplication = dao.count("${entityName}.duplication", new HashMap<String, Object>() {{
                #foreach($column in $tableInfo.fullColumn)
                put("${column.name}", ${entityInstanceName}.get${tool.firstUpperCase($column.name)}());
                #end
                put("notContain$!pkFirstUpperCase", ${entityInstanceName}.get$!pkFirstUpperCase());
            }});
            if (countDuplication > 0) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg(errorString("VZBM0810001", "第" + (i + 1) + "行记录重复！"));
                return outInfo;
            }
        }

        return super.update(inInfo, "${entityName}.update");
    }

    /**
     * 删除功能
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        EiInfo outInfo = super.delete(inInfo, "${entityName}.delete");
        return outInfo;
    }

}
var wns;
var wns2;

$(function () {

    $('#QUERY').on('click', function (e) {
        resultGrid.dataSource.page(1);
    });
    $('#QUERY_DIRECT_USER').on('click', function (e) {
        resultDirectUserGrid.dataSource.page(1);
    });

    var customType =1;

    IPLATUI.EFGrid = {
        "result": {
            pageable: {
                pageSize: 10,
                pageSizes: [10, 20, 50, 100, 500]
            },
            //列是否可以拖动切换位置
            reorderable: true,
            columns: [],
            beforeEdit: function (e) {
                var row = e.row;
                //获取当前修改行
                $("#editRow").val(row);
                if(e.field === "finUserId") {
                    customType = 2;
                }else if(e.field === "directUserNum") {
                    customType = 1;
                }
            },
            afterEdit: function (e) {
                // 执行自定义逻辑代码，假设根据逻辑要求不关闭单元格编辑状态
                if (e.model["finUserId"] === "") {
                    e.model.set("finUserName", "");
                }
                if (e.model["directUserNum"] === "") {
                    e.model.set("directUserName", "");
                }
            }

        },

        "resultDirectUser": {
            /**
             * 双击数据行时触发的事件，注意编辑状态时不会触发
             * @param e
             * e.sender     kendoGrid对象，resultGrid
             * e.model      双击的行数据，kendo.data.Model
             * e.row        当前行的行号
             * e.tr         行的tr,包括固定列和数据列 jquery对象
             */
            onRowDblClick: function (e) {
                var chineseUserName = e.model.chineseUserName;
                var userNum = e.model.userNum;

                var row = $("#editRow").val();
                let dataItems = resultGrid.getDataItems();

                var model = dataItems[row];
                if(customType===1){
                    model.set("directUserNum", userNum);
                    model.set("directUserName", chineseUserName);
                }else if(customType===2){
                    model.set("finUserId", userNum);
                    model.set("finUserName", chineseUserName);
                }

                var popupGridWindow = $("#queryDirectUserNum").data("kendoWindow");
                // 关闭window
                popupGridWindow.close();
            }
        },
    }



})

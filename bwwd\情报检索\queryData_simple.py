import dmPython
import csv
import json
from datetime import datetime, timedelta
import os

# 添加DaMeng bin目录到PATH环境变量
dm_bin_path = r"C:\dmdbms\server\bin"
if os.path.exists(dm_bin_path):
    os.environ["PATH"] = f"{dm_bin_path};{os.environ['PATH']}"

# 数据库配置 - 尝试不同的字符集
DB_CONFIGS = [
    {
        "user": "iplat4j",
        "password": "dameng123",
        "server": "**********",
        "port": 52025,
        "charset": "UTF-8"
    },
    {
        "user": "iplat4j",
        "password": "dameng123",
        "server": "**********",
        "port": 52025,
        "charset": "GB18030"
    },
    {
        "user": "iplat4j",
        "password": "dameng123",
        "server": "**********",
        "port": 52025
        # 不指定charset
    }
]

# 计算时间范围
now = datetime.now()
start_date = (now - timedelta(days=now.weekday())).replace(hour=0, minute=0, second=0, microsecond=0)
end_date = start_date + timedelta(days=7)

def try_connection(config):
    """尝试连接数据库"""
    try:
        conn = dmPython.connect(**config)
        cursor = conn.cursor()
        cursor.execute("SELECT 1 FROM DUAL")
        cursor.fetchone()
        print(f"连接成功，配置: {config}")
        return conn, cursor
    except Exception as e:
        print(f"连接失败，配置: {config}, 错误: {e}")
        return None, None

def safe_decode_text(text):
    """安全解码文本"""
    if text is None:
        return None
    
    if isinstance(text, bytes):
        # 尝试不同的编码
        encodings = ['utf-8', 'gb18030', 'gbk', 'latin1']
        for encoding in encodings:
            try:
                return text.decode(encoding)
            except UnicodeDecodeError:
                continue
        # 如果都失败，使用错误处理
        return text.decode('utf-8', errors='replace')
    
    return str(text)

def export_to_csv(data, filename):
    """导出数据到CSV文件"""
    if not data:
        print("没有数据可导出")
        return
    
    with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.writer(csvfile)
        # 写入标题
        writer.writerow(['UUID', 'SESSION_ID', 'SESSION_TITLE', 'CONTENT'])
        # 写入数据
        for row in data:
            cleaned_row = [safe_decode_text(field) for field in row]
            writer.writerow(cleaned_row)
    
    print(f"数据已导出到: {filename}")

def export_to_json(data, filename):
    """导出数据到JSON文件"""
    if not data:
        print("没有数据可导出")
        return
    
    json_data = []
    for row in data:
        json_data.append({
            'UUID': safe_decode_text(row[0]),
            'SESSION_ID': safe_decode_text(row[1]),
            'SESSION_TITLE': safe_decode_text(row[2]),
            'CONTENT': safe_decode_text(row[3])
        })
    
    with open(filename, 'w', encoding='utf-8') as jsonfile:
        json.dump(json_data, jsonfile, ensure_ascii=False, indent=2)
    
    print(f"数据已导出到: {filename}")

def main():
    conn = None
    cursor = None
    
    # 尝试不同的配置连接数据库
    for config in DB_CONFIGS:
        conn, cursor = try_connection(config)
        if conn:
            break
    
    if not conn:
        print("所有连接配置都失败")
        return
    
    try:
        # 构建查询
        query = f'''
        SELECT t1.UUID, t1.SESSION_ID, t1.SESSION_TITLE, t2.CONTENT  
        FROM IPLAT4J.TXBAI02 AS t1 
        LEFT JOIN TXBAI0201 t2 ON t1.SESSION_ID = t2.SESSION_ID 
        WHERE t1.INTENTION='1' 
        AND t1.REC_CREATE_TIME > {start_date.strftime('%Y%m%d%H%M%S000')} 
        AND t1.REC_CREATE_TIME < {end_date.strftime('%Y%m%d%H%M%S000')}
        '''
        
        print("执行查询:")
        print(query)
        
        cursor.execute(query)
        
        # 尝试获取数据
        all_rows = []
        try:
            # 方法1: 一次性获取所有数据
            rows = cursor.fetchall()
            all_rows = rows
            print(f"成功获取 {len(all_rows)} 行数据")
        except UnicodeDecodeError as e:
            print(f"一次性获取失败: {e}")
            print("尝试逐行获取...")
            
            # 方法2: 逐行获取
            while True:
                try:
                    row = cursor.fetchone()
                    if row is None:
                        break
                    all_rows.append(row)
                except UnicodeDecodeError as row_error:
                    print(f"跳过有问题的行: {row_error}")
                    continue
            
            print(f"逐行获取完成，共 {len(all_rows)} 行数据")
        
        # 显示前几行数据
        print("\n前5行数据预览:")
        for i, row in enumerate(all_rows[:5]):
            print(f"\n--- 第 {i+1} 行 ---")
            try:
                fields = ["UUID", "SESSION_ID", "SESSION_TITLE", "CONTENT"]
                for j, (field_name, value) in enumerate(zip(fields, row)):
                    decoded_value = safe_decode_text(value)
                    if decoded_value and field_name == "CONTENT" and len(decoded_value) > 100:
                        print(f"{field_name}: {decoded_value[:100]}...")
                    else:
                        print(f"{field_name}: {decoded_value}")
            except Exception as e:
                print(f"显示第 {i+1} 行时出错: {e}")
        
        # 导出数据
        if all_rows:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            csv_filename = f"query_results_{timestamp}.csv"
            json_filename = f"query_results_{timestamp}.json"
            
            export_to_csv(all_rows, csv_filename)
            export_to_json(all_rows, json_filename)
        
    except Exception as e:
        print(f"查询执行失败: {e}")
    
    finally:
        # 关闭连接
        if cursor:
            cursor.close()
        if conn:
            conn.close()
        print("数据库连接已关闭")

if __name__ == "__main__":
    main()

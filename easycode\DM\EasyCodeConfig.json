{"author": "<PERSON><PERSON><PERSON>feng", "version": "1.2.9", "userSecure": "", "currTypeMapperGroupName": "baowu", "currTemplateGroupName": "baowu", "currColumnConfigGroupName": "baowu", "currGlobalConfigGroupName": "baowu", "typeMapper": {"baowu": {"name": "baowu", "elementList": [{"matchType": "REGEX", "columnType": "varchar(\\(\\d+\\))?", "javaType": "java.lang.String"}, {"matchType": "REGEX", "columnType": "char(\\(\\d+\\))?", "javaType": "java.lang.String"}, {"matchType": "REGEX", "columnType": "(tiny|medium|long)*text", "javaType": "java.lang.String"}, {"matchType": "REGEX", "columnType": "decimal(\\(\\d+,\\d+\\))?", "javaType": "java.math.BigDecimal"}, {"matchType": "ORDINARY", "columnType": "integer", "javaType": "java.lang.Integer"}, {"matchType": "REGEX", "columnType": "(tiny|small|medium)*int(\\(\\d+\\))?", "javaType": "java.lang.Integer"}, {"matchType": "ORDINARY", "columnType": "int4", "javaType": "java.lang.Integer"}, {"matchType": "ORDINARY", "columnType": "int8", "javaType": "java.lang.Long"}, {"matchType": "REGEX", "columnType": "bigint(\\(\\d+\\))?", "javaType": "java.lang.Long"}, {"matchType": "ORDINARY", "columnType": "date", "javaType": "java.lang.String"}, {"matchType": "ORDINARY", "columnType": "datetime", "javaType": "java.lang.String"}, {"matchType": "ORDINARY", "columnType": "timestamp", "javaType": "java.lang.String"}, {"matchType": "ORDINARY", "columnType": "time", "javaType": "java.lang.String"}, {"matchType": "ORDINARY", "columnType": "boolean", "javaType": "java.lang.String"}, {"matchType": "ORDINARY", "columnType": "bigint unsigned", "javaType": "java.lang.Long"}, {"matchType": "ORDINARY", "columnType": "NUMBER(19)", "javaType": "java.lang.Integer"}, {"matchType": "ORDINARY", "columnType": "NVARCHAR2(256)", "javaType": "java.lang.String"}]}, "Default": {"name": "<PERSON><PERSON><PERSON>", "elementList": [{"matchType": "REGEX", "columnType": "varchar(\\(\\d+\\))?", "javaType": "java.lang.String"}, {"matchType": "REGEX", "columnType": "char(\\(\\d+\\))?", "javaType": "java.lang.String"}, {"matchType": "REGEX", "columnType": "(tiny|medium|long)*text", "javaType": "java.lang.String"}, {"matchType": "REGEX", "columnType": "decimal(\\(\\d+,\\d+\\))?", "javaType": "java.lang.Double"}, {"matchType": "ORDINARY", "columnType": "integer", "javaType": "java.lang.Integer"}, {"matchType": "REGEX", "columnType": "(tiny|small|medium)*int(\\(\\d+\\))?", "javaType": "java.lang.Integer"}, {"matchType": "ORDINARY", "columnType": "int4", "javaType": "java.lang.Integer"}, {"matchType": "ORDINARY", "columnType": "int8", "javaType": "java.lang.Long"}, {"matchType": "REGEX", "columnType": "bigint(\\(\\d+\\))?", "javaType": "java.lang.Long"}, {"matchType": "ORDINARY", "columnType": "date", "javaType": "java.util.Date"}, {"matchType": "ORDINARY", "columnType": "datetime", "javaType": "java.util.Date"}, {"matchType": "ORDINARY", "columnType": "timestamp", "javaType": "java.util.Date"}, {"matchType": "ORDINARY", "columnType": "time", "javaType": "java.time.LocalTime"}, {"matchType": "ORDINARY", "columnType": "boolean", "javaType": "java.lang.Bo<PERSON>an"}, {"matchType": "ORDINARY", "columnType": "bigint unsigned", "javaType": "java.lang.Long"}, {"matchType": "ORDINARY", "columnType": "NUMBER(19)", "javaType": "java.lang.Integer"}, {"matchType": "ORDINARY", "columnType": "NVARCHAR2(256)", "javaType": "java.lang.String"}]}}, "template": {"MybatisPlus-Mixed": {"name": "MybatisPlus-Mixed", "elementList": [{"name": "controller.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##设置表后缀（宏定义）\n#setTableSuffix(\"Controller\")\n\n##保存文件（宏定义）\n#save(\"/controller\", \"Controller.java\")\n\n##包路径（宏定义）\n#setPackageSuffix(\"controller\")\n\n##定义服务名\n#set($serviceName = $!tool.append($!tool.firstLowerCase($!tableInfo.name), \"Service\"))\n\n##定义实体对象名\n#set($entityName = $!tool.firstLowerCase($!tableInfo.name))\n\nimport com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;\nimport com.baomidou.mybatisplus.extension.api.ApiController;\nimport com.baomidou.mybatisplus.extension.api.R;\nimport com.baomidou.mybatisplus.extension.plugins.pagination.Page;\nimport $!{tableInfo.savePackageName}.entity.$!tableInfo.name;\nimport $!{tableInfo.savePackageName}.service.$!{tableInfo.name}Service;\nimport org.springframework.web.bind.annotation.*;\n\nimport javax.annotation.Resource;\nimport java.io.Serializable;\nimport java.util.List;\n\n##表注释（宏定义）\n#tableComment(\"表控制层\")\n@RestController\n@RequestMapping(\"$!tool.firstLowerCase($!tableInfo.name)\")\npublic class $!{tableName} extends ApiController {\n    /**\n     * 服务对象\n     */\n    @Resource\n    private $!{tableInfo.name}Service $!{serviceName};\n\n    /**\n     * 分页查询所有数据\n     *\n     * @param page 分页对象\n     * @param $!entityName 查询实体\n     * @return 所有数据\n     */\n    @GetMapping\n    public R selectAll(Page<$!tableInfo.name> page, $!tableInfo.name $!entityName) {\n        return success(this.$!{serviceName}.page(page, new QueryWrapper<>($!entityName)));\n    }\n\n    /**\n     * 通过主键查询单条数据\n     *\n     * @param id 主键\n     * @return 单条数据\n     */\n    @GetMapping(\"{id}\")\n    public R selectOne(@PathVariable Serializable id) {\n        return success(this.$!{serviceName}.getById(id));\n    }\n\n    /**\n     * 新增数据\n     *\n     * @param $!entityName 实体对象\n     * @return 新增结果\n     */\n    @PostMapping\n    public R insert(@RequestBody $!tableInfo.name $!entityName) {\n        return success(this.$!{serviceName}.save($!entityName));\n    }\n\n    /**\n     * 修改数据\n     *\n     * @param $!entityName 实体对象\n     * @return 修改结果\n     */\n    @PutMapping\n    public R update(@RequestBody $!tableInfo.name $!entityName) {\n        return success(this.$!{serviceName}.updateById($!entityName));\n    }\n\n    /**\n     * 删除数据\n     *\n     * @param idList 主键结合\n     * @return 删除结果\n     */\n    @DeleteMapping\n    public R delete(@RequestParam(\"idList\") List<Long> idList) {\n        return success(this.$!{serviceName}.removeByIds(idList));\n    }\n}\n"}, {"name": "dao.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##设置表后缀（宏定义）\n#setTableSuffix(\"Dao\")\n\n##保存文件（宏定义）\n#save(\"/dao\", \"Dao.java\")\n\n##包路径（宏定义）\n#setPackageSuffix(\"dao\")\n\nimport java.util.List;\n\nimport com.baomidou.mybatisplus.core.mapper.BaseMapper;\nimport org.apache.ibatis.annotations.Param;\nimport $!{tableInfo.savePackageName}.entity.$!tableInfo.name;\n\n##表注释（宏定义）\n#tableComment(\"表数据库访问层\")\npublic interface $!{tableName} extends BaseMapper<$!tableInfo.name> {\n\n/**\n* 批量新增数据（MyBatis原生foreach方法）\n*\n* @param entities List<$!{tableInfo.name}> 实例对象列表\n* @return 影响行数\n*/\nint insertBatch(@Param(\"entities\") List<$!{tableInfo.name}> entities);\n\n/**\n* 批量新增或按主键更新数据（MyBatis原生foreach方法）\n*\n* @param entities List<$!{tableInfo.name}> 实例对象列表\n* @return 影响行数\n* @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参\n*/\nint insertOrUpdateBatch(@Param(\"entities\") List<$!{tableInfo.name}> entities);\n\n}\n"}, {"name": "entity.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##保存文件（宏定义）\n#save(\"/entity\", \".java\")\n\n##包路径（宏定义）\n#setPackageSuffix(\"entity\")\n\n##自动导入包（全局变量）\n$!autoImport\nimport com.baomidou.mybatisplus.extension.activerecord.Model;\nimport java.io.Serializable;\n\n##表注释（宏定义）\n#tableComment(\"表实体类\")\n@SuppressWarnings(\"serial\")\npublic class $!{tableInfo.name} extends Model<$!{tableInfo.name}> {\n#foreach($column in $tableInfo.fullColumn)\n    #if(${column.comment})//${column.comment}#end\n\n    private $!{tool.getClsNameByFullName($column.type)} $!{column.name};\n#end\n\n#foreach($column in $tableInfo.fullColumn)\n#getSetMethod($column)\n#end\n\n#foreach($column in $tableInfo.pkColumn)\n    /**\n     * 获取主键值\n     *\n     * @return 主键值\n     */\n    @Override\n    protected Serializable pkVal() {\n        return this.$!column.name;\n    }\n    #break\n#end\n}\n"}, {"name": "mapper.xml.vm", "code": "##引入mybatis支持\n$!{mybatisSupport.vm}\n\n##设置保存名称与保存位置\n$!callback.setFileName($tool.append($!{tableInfo.name}, \"Dao.xml\"))\n$!callback.setSavePath($tool.append($modulePath, \"/src/main/resources/mapper\"))\n\n##拿到主键\n#if(!$tableInfo.pkColumn.isEmpty())\n    #set($pk = $tableInfo.pkColumn.get(0))\n#end\n\n<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">\n<mapper namespace=\"$!{tableInfo.savePackageName}.dao.$!{tableInfo.name}Dao\">\n\n    <resultMap type=\"$!{tableInfo.savePackageName}.entity.$!{tableInfo.name}\" id=\"$!{tableInfo.name}Map\">\n#foreach($column in $tableInfo.fullColumn)\n        <result property=\"$!column.name\" column=\"$!column.obj.name\" jdbcType=\"$!column.ext.jdbcType\"/>\n#end\n    </resultMap>\n\n    <!-- 批量插入 -->\n    <insert id=\"insertBatch\" keyProperty=\"$!pk.name\" useGeneratedKeys=\"true\">\n        insert into $!{tableInfo.obj.parent.name}.$!{tableInfo.obj.name}(#foreach($column in $tableInfo.otherColumn)$!column.obj.name#if($foreach.hasNext), #end#end)\n        values\n        <foreach collection=\"entities\" item=\"entity\" separator=\",\">\n        (#foreach($column in $tableInfo.otherColumn)#{entity.$!{column.name}}#if($foreach.hasNext), #end#end)\n        </foreach>\n    </insert>\n    <!-- 批量插入或按主键更新 -->\n    <insert id=\"insertOrUpdateBatch\" keyProperty=\"$!pk.name\" useGeneratedKeys=\"true\">\n        insert into $!{tableInfo.obj.parent.name}.$!{tableInfo.obj.name}(#foreach($column in $tableInfo.otherColumn)$!column.obj.name#if($foreach.hasNext), #end#end)\n        values\n        <foreach collection=\"entities\" item=\"entity\" separator=\",\">\n            (#foreach($column in $tableInfo.otherColumn)#{entity.$!{column.name}}#if($foreach.hasNext), #end#end)\n        </foreach>\n        on duplicate key update\n         #foreach($column in $tableInfo.otherColumn)$!column.obj.name = values($!column.obj.name) #if($foreach.hasNext), #end#end\n    </insert>\n\n</mapper>\n"}, {"name": "service.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##设置表后缀（宏定义）\n#setTableSuffix(\"Service\")\n\n##保存文件（宏定义）\n#save(\"/service\", \"Service.java\")\n\n##包路径（宏定义）\n#setPackageSuffix(\"service\")\n\nimport com.baomidou.mybatisplus.extension.service.IService;\nimport $!{tableInfo.savePackageName}.entity.$!tableInfo.name;\n\n##表注释（宏定义）\n#tableComment(\"表服务接口\")\npublic interface $!{tableName} extends IService<$!tableInfo.name> {\n\n}\n"}, {"name": "serviceImpl.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##设置表后缀（宏定义）\n#setTableSuffix(\"ServiceImpl\")\n\n##保存文件（宏定义）\n#save(\"/service/impl\", \"ServiceImpl.java\")\n\n##包路径（宏定义）\n#setPackageSuffix(\"service.impl\")\n\nimport com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;\nimport $!{tableInfo.savePackageName}.dao.$!{tableInfo.name}Dao;\nimport $!{tableInfo.savePackageName}.entity.$!{tableInfo.name};\nimport $!{tableInfo.savePackageName}.service.$!{tableInfo.name}Service;\nimport org.springframework.stereotype.Service;\n\n##表注释（宏定义）\n#tableComment(\"表服务实现类\")\n@Service(\"$!tool.firstLowerCase($tableInfo.name)Service\")\npublic class $!{tableName} extends ServiceImpl<$!{tableInfo.name}Dao, $!{tableInfo.name}> implements $!{tableInfo.name}Service {\n\n}\n"}]}, "MybatisPlus": {"name": "MybatisPlus", "elementList": [{"name": "controller.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##设置表后缀（宏定义）\n#setTableSuffix(\"Controller\")\n\n##保存文件（宏定义）\n#save(\"/controller\", \"Controller.java\")\n\n##包路径（宏定义）\n#setPackageSuffix(\"controller\")\n\n##定义服务名\n#set($serviceName = $!tool.append($!tool.firstLowerCase($!tableInfo.name), \"Service\"))\n\n##定义实体对象名\n#set($entityName = $!tool.firstLowerCase($!tableInfo.name))\n\nimport com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;\nimport com.baomidou.mybatisplus.extension.api.ApiController;\nimport com.baomidou.mybatisplus.extension.api.R;\nimport com.baomidou.mybatisplus.extension.plugins.pagination.Page;\nimport $!{tableInfo.savePackageName}.entity.$!tableInfo.name;\nimport $!{tableInfo.savePackageName}.service.$!{tableInfo.name}Service;\nimport org.springframework.web.bind.annotation.*;\n\nimport javax.annotation.Resource;\nimport java.io.Serializable;\nimport java.util.List;\n\n##表注释（宏定义）\n#tableComment(\"表控制层\")\n@RestController\n@RequestMapping(\"$!tool.firstLowerCase($!tableInfo.name)\")\npublic class $!{tableName} extends ApiController {\n    /**\n     * 服务对象\n     */\n    @Resource\n    private $!{tableInfo.name}Service $!{serviceName};\n\n    /**\n     * 分页查询所有数据\n     *\n     * @param page 分页对象\n     * @param $!entityName 查询实体\n     * @return 所有数据\n     */\n    @GetMapping\n    public R selectAll(Page<$!tableInfo.name> page, $!tableInfo.name $!entityName) {\n        return success(this.$!{serviceName}.page(page, new QueryWrapper<>($!entityName)));\n    }\n\n    /**\n     * 通过主键查询单条数据\n     *\n     * @param id 主键\n     * @return 单条数据\n     */\n    @GetMapping(\"{id}\")\n    public R selectOne(@PathVariable Serializable id) {\n        return success(this.$!{serviceName}.getById(id));\n    }\n\n    /**\n     * 新增数据\n     *\n     * @param $!entityName 实体对象\n     * @return 新增结果\n     */\n    @PostMapping\n    public R insert(@RequestBody $!tableInfo.name $!entityName) {\n        return success(this.$!{serviceName}.save($!entityName));\n    }\n\n    /**\n     * 修改数据\n     *\n     * @param $!entityName 实体对象\n     * @return 修改结果\n     */\n    @PutMapping\n    public R update(@RequestBody $!tableInfo.name $!entityName) {\n        return success(this.$!{serviceName}.updateById($!entityName));\n    }\n\n    /**\n     * 删除数据\n     *\n     * @param idList 主键结合\n     * @return 删除结果\n     */\n    @DeleteMapping\n    public R delete(@RequestParam(\"idList\") List<Long> idList) {\n        return success(this.$!{serviceName}.removeByIds(idList));\n    }\n}\n"}, {"name": "dao.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##设置表后缀（宏定义）\n#setTableSuffix(\"Dao\")\n\n##保存文件（宏定义）\n#save(\"/dao\", \"Dao.java\")\n\n##包路径（宏定义）\n#setPackageSuffix(\"dao\")\n\nimport com.baomidou.mybatisplus.core.mapper.BaseMapper;\nimport $!{tableInfo.savePackageName}.entity.$!tableInfo.name;\n\n##表注释（宏定义）\n#tableComment(\"表数据库访问层\")\npublic interface $!{tableName} extends BaseMapper<$!tableInfo.name> {\n\n}\n"}, {"name": "entity.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##保存文件（宏定义）\n#save(\"/entity\", \".java\")\n\n##包路径（宏定义）\n#setPackageSuffix(\"entity\")\n\n##自动导入包（全局变量）\n$!{autoImport.vm}\nimport com.baomidou.mybatisplus.extension.activerecord.Model;\nimport java.io.Serializable;\n\n##表注释（宏定义）\n#tableComment(\"表实体类\")\n@SuppressWarnings(\"serial\")\npublic class $!{tableInfo.name} extends Model<$!{tableInfo.name}> {\n#foreach($column in $tableInfo.fullColumn)\n    #if(${column.comment})//${column.comment}#end\n\n    private $!{tool.getClsNameByFullName($column.type)} $!{column.name};\n#end\n\n#foreach($column in $tableInfo.fullColumn)\n#getSetMethod($column)\n#end\n\n#foreach($column in $tableInfo.pkColumn)\n    /**\n     * 获取主键值\n     *\n     * @return 主键值\n     */\n    @Override\n    protected Serializable pkVal() {\n        return this.$!column.name;\n    }\n    #break\n#end\n}\n"}, {"name": "service.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##设置表后缀（宏定义）\n#setTableSuffix(\"Service\")\n\n##保存文件（宏定义）\n#save(\"/service\", \"Service.java\")\n\n##包路径（宏定义）\n#setPackageSuffix(\"service\")\n\nimport com.baomidou.mybatisplus.extension.service.IService;\nimport $!{tableInfo.savePackageName}.entity.$!tableInfo.name;\n\n##表注释（宏定义）\n#tableComment(\"表服务接口\")\npublic interface $!{tableName} extends IService<$!tableInfo.name> {\n\n}\n"}, {"name": "serviceImpl.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##设置表后缀（宏定义）\n#setTableSuffix(\"ServiceImpl\")\n\n##保存文件（宏定义）\n#save(\"/service/impl\", \"ServiceImpl.java\")\n\n##包路径（宏定义）\n#setPackageSuffix(\"service.impl\")\n\nimport com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;\nimport $!{tableInfo.savePackageName}.dao.$!{tableInfo.name}Dao;\nimport $!{tableInfo.savePackageName}.entity.$!{tableInfo.name};\nimport $!{tableInfo.savePackageName}.service.$!{tableInfo.name}Service;\nimport org.springframework.stereotype.Service;\n\n##表注释（宏定义）\n#tableComment(\"表服务实现类\")\n@Service(\"$!tool.firstLowerCase($tableInfo.name)Service\")\npublic class $!{tableName} extends ServiceImpl<$!{tableInfo.name}Dao, $!{tableInfo.name}> implements $!{tableInfo.name}Service {\n\n}\n"}]}, "spring-data-mongodb": {"name": "spring-data-mongodb", "elementList": [{"name": "controller.java.vm", "code": "##导入宏定义、设置包名、类名、文件名##导入宏定义\n$!{define.vm}\n#setPackageSuffix(\"controller\")\n#setTableSuffix(\"Controller\")\n#save(\"/controller\", \"Controller.java\")\n\n##拿到主键\n#if(!$tableInfo.pkColumn.isEmpty())\n    #set($pk = $tableInfo.pkColumn.get(0))\n#end\n##定义服务名\n#set($serviceSortType = $!tool.append($!tableInfo.name, \"Service\"))\n#set($serviceType = $!tool.append($!tableInfo.savePackageName, \".service.\", $serviceSortType))\n#set($serviceVarName = $!tool.firstLowerCase($serviceSortType))\n\n#set($entityShortType = $!tableInfo.name)\n#set($entityType = $!tableInfo.psiClassObj.getQualifiedName())\n#set($entityVarName = $!tool.firstLowerCase($!tableInfo.name))\n#set($pkType = $!pk.type)\n\nimport $pkType;\nimport $entityType;\nimport $serviceType;\nimport lombok.AllArgsConstructor;\nimport org.springframework.data.domain.Page;\nimport org.springframework.data.domain.Pageable;\nimport org.springframework.web.bind.annotation.GetMapping;\nimport org.springframework.web.bind.annotation.PostMapping;\nimport org.springframework.web.bind.annotation.RequestBody;\nimport org.springframework.web.bind.annotation.RequestMapping;\nimport org.springframework.web.bind.annotation.RestController;\n\n\n/**\n * $!{tableInfo.comment}控制层\n *\n * <AUTHOR>\n * @since $!time.currTime()\n */\n@RestController\n@RequestMapping(\"/$!tool.firstLowerCase($!tableInfo.name)\")\n@AllArgsConstructor\npublic class $!{tableName} {\n\n\tprivate $serviceSortType $serviceVarName;\n\n\t/**\n\t * 获取$!{tableInfo.comment}列表(分页)\n\t */\n\t@GetMapping(\"/list\")\n\tpublic Page<$entityShortType> list(Pageable page) {\n\t\treturn null;\n\t}\n\n\t/**\n\t * 获取$!{tableInfo.comment}\n\t */\n\t@GetMapping(\"/get\")\n\tpublic $entityShortType get($!pk.shortType id) {\n\t\treturn ${serviceVarName}.findById(id);\n\t}\n\n\t/**\n\t * 添加$!{tableInfo.comment}\n\t */\n\t@PostMapping(\"/add\")\n\tpublic void add(@RequestBody $entityShortType $entityVarName) {\n\t\t${serviceVarName}.save($entityVarName);\n\t}\n\n\n\t/**\n\t * 修改$!{tableInfo.comment}\n\t */\n\t@PostMapping(\"/update\")\n\tpublic void update(@RequestBody $entityShortType $entityVarName) {\n\t\t${serviceVarName}.save($entityVarName);\n\t}\n\n\t/**\n\t * 删除$!{tableInfo.comment}\n\t */\n\t@PostMapping(\"/delete\")\n\tpublic void delete($!pk.shortType id) {\n\t\t${serviceVarName}.deleteById(id);\n\t}\n\n}\n"}, {"name": "entity.java.vm", "code": "##引入宏定义\n$!{define.vm}\n\n##使用宏定义设置回调（保存位置与文件后缀）\n#save(\"/entity\", \".java\")\n\n##使用宏定义设置包后缀\n#setPackageSuffix(\"entity\")\n\n##使用全局变量实现默认包导入\n$!{autoImport.vm}\nimport java.io.Serializable;\n\n##使用宏定义实现类注释信息\n#tableComment(\"实体类\")\npublic class $!{tableInfo.name} implements Serializable {\n    private static final long serialVersionUID = $!tool.serial();\n#foreach($column in $tableInfo.fullColumn)\n    #if(${column.comment})/**\n     * ${column.comment}\n     */#end\n\n    private $!{tool.getClsNameByFullName($column.type)} $!{column.name};\n#end\n\n#foreach($column in $tableInfo.fullColumn)\n##使用宏定义实现get,set方法\n#getSetMethod($column)\n#end\n\n}\n"}, {"name": "repository.java.vm", "code": "##导入宏定义、设置包名、类名、文件名##导入宏定义\n$!{define.vm}\n#setPackageSuffix(\"repository\")\n#setTableSuffix(\"Repository\")\n#save(\"/repository\", \"Repository.java\")\n\n##拿到主键\n#if(!$tableInfo.pkColumn.isEmpty())\n    #set($pk = $tableInfo.pkColumn.get(0))\n#end\n##实体类名、主键类名\n#set($entityShortType = $!tableInfo.name)\n#set($entityType = $!tableInfo.psiClassObj.getQualifiedName())\n#set($pkShortType = $!pk.shortType)\n#set($pkType = $!pk.type)\n\nimport $pkType;\nimport $entityType;\nimport org.springframework.data.mongodb.repository.MongoRepository;\n\n\n/**\n * $!{tableInfo.comment}持久层\n *\n * <AUTHOR>\n * @since $!time.currTime()\n */\npublic interface $!{tableName} extends MongoRepository<$entityShortType, $pkShortType> {\n\n}\n"}, {"name": "service.java.vm", "code": "##导入宏定义、设置包名、类名、文件名##导入宏定义\n$!{define.vm}\n#setPackageSuffix(\"service\")\n#setTableSuffix(\"Service\")\n#save(\"/service\", \"Service.java\")\n\n##拿到主键\n#if(!$tableInfo.pkColumn.isEmpty())\n    #set($pk = $tableInfo.pkColumn.get(0))\n#end\n##实体类名、主键类名\n#set($entityShortType = $!tableInfo.name)\n#set($entityType = $!tableInfo.psiClassObj.getQualifiedName())\n#set($entityVarName = $!tool.firstLowerCase($!tableInfo.name))\n#set($pkShortType = $!pk.shortType)\n#set($pkType = $!pk.type)\n\nimport $pkType;\nimport $entityType;\nimport org.springframework.data.domain.Page;\nimport org.springframework.data.domain.Pageable;\nimport java.util.Collection;\nimport java.util.List;\n\n\n/**\n * $!{tableInfo.comment}业务层\n *\n * <AUTHOR>\n * @since $!time.currTime()\n */\npublic interface $!{tableName} {\n\n    void save($entityShortType $entityVarName);\n\n    void deleteById($pkShortType id);\n\n    $entityShortType findById($pkShortType id);\n\n    List<$entityShortType> findById(Collection<$pkShortType> ids);\n\n    Page<$entityShortType> list(Pageable page);\n\n}\n"}, {"name": "serviceImpl.java.vm", "code": "##导入宏定义、设置包名、类名、文件名\n$!{define.vm}\n#setPackageSuffix(\"service.impl\")\n#setTableSuffix(\"ServiceImpl\")\n#save(\"/service/impl\", \"ServiceImpl.java\")\n\n##拿到主键\n#if(!$tableInfo.pkColumn.isEmpty())\n    #set($pk = $tableInfo.pkColumn.get(0))\n#end\n##业务层类名、持久层类名、实体名\n#set($serviceSortType = $!tool.append($!tableInfo.name, \"Service\"))\n#set($serviceType = $!tool.append($!tableInfo.savePackageName, \".service.\", $serviceSortType))\n#set($repositorySortType = $!tool.append($!tableInfo.name, \"Repository\"))\n#set($repositoryType = $!tool.append($!tableInfo.savePackageName, \".repository.\", $repositorySortType))\n#set($repositoryVarName = $!tool.firstLowerCase($!repositorySortType))\n#set($entityShortType = $!tableInfo.name)\n#set($entityType = $!tableInfo.psiClassObj.getQualifiedName())\n#set($entityVarName = $!tool.firstLowerCase($!tableInfo.name))\n#set($pkShortType = $!pk.shortType)\n#set($pkType = $!pk.type)\n\nimport $pkType;\nimport $entityType;\nimport $serviceType;\nimport $repositoryType;\nimport org.springframework.stereotype.Service;\nimport javax.annotation.Resource;\nimport org.springframework.data.domain.Page;\nimport org.springframework.data.domain.Pageable;\nimport java.util.Collection;\nimport java.util.List;\nimport java.util.stream.Collectors;\nimport java.util.stream.StreamSupport;\n\n\n/**\n * $!{tableInfo.comment}业务层\n *\n * <AUTHOR>\n * @since $!time.currTime()\n */\n@Service\npublic class $!{tableName} implements $!serviceSortType {\n\n\t@Resource\n    private $repositorySortType $repositoryVarName;\n\n    @Override\n    public void save($entityShortType $entityVarName) {\n        $!{repositoryVarName}.save($entityVarName);\n    }\n\n    @Override\n    public void deleteById($pkShortType id) {\n        $!{repositoryVarName}.delete(id);\n    }\n\n    @Override\n    public $entityShortType findById($pkShortType id) {\n        return $!{repositoryVarName}.findOne(id);\n    }\n\n    @Override\n    public List<$entityShortType> findById(Collection<$pkShortType> ids) {\n        Iterable<$entityShortType> iterable = $!{repositoryVarName}.findAll(ids);\n        return StreamSupport.stream(iterable.spliterator(), false)\n                .collect(Collectors.toList());\n    }\n\n    @Override\n    public Page<$entityShortType> list(Pageable page) {\n        return $!{repositoryVarName}.findAll(page);\n    }\n\n}\n"}]}, "baowu": {"name": "baowu", "elementList": [{"name": "entity.java.vm", "code": "## 引入宏定义\n$!{baowu.vm}\n\n## 使用宏定义设置回调（保存位置与文件）\n$!callback.setSavePath($tableInfo.savePath+\"/domain\")\n$!callback.setFileName($entityName+\".java\")\n\n## 使用宏定义设置包后缀,打印包路径\n#setPackageSuffix(\"domain\")\n\n## 使用全局变量实现默认包导入\n$!{autoImport.vm}\nimport com.baosight.iplat4j.core.data.DaoEPBase;\nimport com.baosight.iplat4j.core.ei.EiColumn;\nimport com.baosight.iplat4j.core.util.NumberUtils;\nimport com.baosight.iplat4j.core.util.StringUtils;\nimport java.util.HashMap;\nimport java.util.Map;\n\n## 使用宏定义实现类注释信息\n/**\n * Title: ${entityName}.java <br>\n * Description: ${tableInfo.name} ${tableInfo.comment} <br>\n *\n * Copyright: Baosight Software LTD.co Copyright (c) 2019. <br>\n *\n * @version 1.0\n * @CreateTime ${time.currTime('yyyy-MM-dd HH:mm:ss')} \n * <AUTHOR>\n */\npublic class ${entityName} extends DaoEPBase {\n    private static final long serialVersionUID = 1L;\n\n#foreach($column in $tableInfo.fullColumn)\n    public static final String FIELD_${tool.hump2Underline($column.name).toUpperCase()} = \"$!{column.name}\";    \t\t/* $!{column.comment} */\n#end\n\n#foreach($column in $tableInfo.fullColumn)\n    public static final String COL_${tool.hump2Underline($column.name).toUpperCase()} = \"$!{tool.hump2Underline($column.name).toUpperCase()}\";    \t\t/* $!{column.comment} */\n#end\n\n    public static final String QUERY = \"${entityName}.query\";\n    public static final String COUNT = \"${entityName}.count\";\n    public static final String INSERT = \"${entityName}.insert\";\n    public static final String UPDATE = \"${entityName}.update\";\n    public static final String DELETE = \"${entityName}.delete\";\n\n#foreach($column in $tableInfo.fullColumn)\n    private ${column.shortType} $!{column.name} =#if($column.shortType==\"String\") \"\"#else new ${column.shortType}(0)#end;\t\t/* $!{column.comment} */\n#end\n\n    /**\n     * initialize the metadata.\n     */\n    public void initMetaData() {\n        EiColumn eiColumn;\n\n#foreach($column in $tableInfo.fullColumn)\n        eiColumn = new EiColumn(FIELD_${tool.hump2Underline($column.name).toUpperCase()});\n        eiColumn.setFieldLength(${column.obj.getDataType().getLength()});\n        eiColumn.setDescName(\"$!{column.comment}\");\n        eiMetadata.addMeta(eiColumn);\n#end\n    }\n\n    /**\n     * the constructor.\n     */\n    public ${entityName}() {\n        initMetaData();\n    }\n\n#foreach($column in $tableInfo.fullColumn)\n    /**\n     * get the $!{column.name} - $!{column.comment}.\n     * @return the $!{column.name}\n     */\n    public ${column.shortType} get${tool.firstUpperCase($column.name)}() {\n        return this.$!{column.name};\n    }\n\n    /**\n     * set the $!{column.name} - $!{column.comment}.\n     *\n     * @param $!{column.name} - $!{column.comment}\n     */\n    public void set${tool.firstUpperCase($column.name)}(${column.shortType} $!{column.name}) {\n        this.$!{column.name} = $!{column.name};\n    }\n#end\n\n    /**\n     * get the value from Map.\n     *\n     * @param map - source data map\n     */\n    @Override\n    public void fromMap(Map map) {\n#foreach($column in $tableInfo.fullColumn)\n        #if($column.shortType==\"String\")\n            set${tool.firstUpperCase($column.name)}(StringUtils.defaultIfEmpty(StringUtils.toString(map.get(FIELD_${tool.hump2Underline($column.name).toUpperCase()})), $!{column.name}));\n        #else\n            set${tool.firstUpperCase($column.name)}(NumberUtils.to${column.shortType}(StringUtils.toString(map.get(FIELD_${tool.hump2Underline($column.name).toUpperCase()})), $!{column.name}));\n        #end\n#end\n    }\n\n    /**\n     * set the value to Map.\n     */\n    @Override\n    public Map toMap() {\n        Map map = new HashMap();\n#foreach($column in $tableInfo.fullColumn)\n        map.put(FIELD_${tool.hump2Underline($column.name).toUpperCase()}, StringUtils.toString($!{column.name}, eiMetadata.getMeta(FIELD_${tool.hump2Underline($column.name).toUpperCase()})));\n#end\n        return map;\n    }\n}"}, {"name": "mapper.xml.vm", "code": "## 引入宏定义\n$!{baowu.vm}\n\n## 使用宏定义设置回调（保存位置与文件）\n$!callback.setSavePath($tableInfo.savePath+\"/sql\")\n$!callback.setFileName($entityName+\".xml\")\n\n<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE sqlMap  PUBLIC \"-//ibatis.apache.org//DTD SQL Map 2.0//EN\" \"http://ibatis.apache.org/dtd/sql-map-2.dtd\">\n<!--      table information\n\t\tGenerate time : ${time.currTime('yyyy-MM-dd HH:mm:ss')}\n   \t\tVersion :  1.0\n\t\ttable comment : ${tableInfo.comment}\n\t\tschema : ${dasUtil.getSchema($tableInfo.obj)}\n\t\ttableName : ${tableInfo.obj.getName()}\n#foreach($column in $tableInfo.fullColumn)\n\t\t$!column.obj.name  ${column.obj.getDataType()}  default ${column.obj.getDefault()} #if($column.obj.isNotNull()) NOT #end NULL, \n#end\n\t-->\n<sqlMap namespace=\"${entityName}\">\n\n\t<sql id=\"condition\">\n#foreach($column in $tableInfo.fullColumn)\n\t\t<isNotEmpty prepend=\" AND \" property=\"${column.name}\">\n\t\t\t$!column.obj.name = #${column.name}#\n\t\t</isNotEmpty>\n#end\n\t</sql>\n\n\t<select id=\"duplication\" parameterClass=\"java.util.HashMap\"\n\t\t\tresultClass=\"${tableInfo.savePackageName}.${entityName}\">\n\t\tSELECT\n#foreach($column in $tableInfo.fullColumn)\n\t\t\t$!column.obj.name\tas \"${column.name}\",  <!-- ${column.comment} -->\n#end\n\t\tFROM ${dasUtil.getSchema($tableInfo.obj)}.${tableInfo.obj.getName()} WHERE 1=1 \n#foreach($column in $tableInfo.fullColumn)\n\t\t\tAND $!column.obj.name = #${column.name}#\n#end\n\n\t\t<isNotEmpty prepend=\" AND \" property=\"notContain$!pkFirstUpperCase\">\n\t\t\t$!pk.obj.name  != #notContain$!pkFirstUpperCase#\n\t\t</isNotEmpty>\n\n\t\t<dynamic prepend=\"ORDER BY\">\n\t\t\t<isNotEmpty property=\"orderBy\">\n    \t\t  $orderBy$\n\t\t\t</isNotEmpty>\n\t\t</dynamic>\n\n\t</select>\n\n\t<select id=\"query\" parameterClass=\"java.util.HashMap\"\n\t\t\tresultClass=\"${tableInfo.savePackageName}.${tableInfo.name.toUpperCase()}\">\n\t\tSELECT\n#foreach($column in $tableInfo.fullColumn)\n\t\t$!column.obj.name\tas \"${column.name}\",  <!-- ${column.comment} -->\n#end\n\t\tFROM ${dasUtil.getSchema($tableInfo.obj)}.${tableInfo.obj.getName()} WHERE 1=1\n\t\t<include refid=\"condition\" />\n\t\t<dynamic prepend=\"ORDER BY\">\n\t\t\t<isNotEmpty property=\"orderBy\">\n\t\t\t\t$orderBy$\n\t\t\t</isNotEmpty>\n\t\t</dynamic>\n\n\t</select>\n\n\t<select id=\"count\" resultClass=\"int\">\n\t\tSELECT COUNT(*) FROM ${dasUtil.getSchema($tableInfo.obj)}.${tableInfo.obj.getName()} WHERE 1=1\n    <include refid=\"condition\" />\n\t</select>\n\n\t<insert id=\"insert\">\n\t\tINSERT INTO ${dasUtil.getSchema($tableInfo.obj)}.${tableInfo.obj.getName()} (\n#foreach($column in $tableInfo.fullColumn)\n\t\t\t$!column.obj.name #if($foreach.hasNext),#end <!-- ${column.comment} -->\n#end\n\t\t\t)\n\t    VALUES (\n#foreach($column in $tableInfo.fullColumn)\n\t\t\t#${column.name}:$!column.ext.jdbcType# #if($foreach.hasNext),#end\n#end\n\t\t\t)\n\t</insert>\n\n\t<delete id=\"delete\">\n\t\tDELETE FROM ${dasUtil.getSchema($tableInfo.obj)}.${tableInfo.obj.getName()} WHERE $!pk.obj.name = #$!pk.name#\n\t</delete>\n\n\t<update id=\"update\">\n\t\tUPDATE ${dasUtil.getSchema($tableInfo.obj)}.${tableInfo.obj.getName()}\n\t\tSET\n#foreach($column in $tableInfo.otherColumn)\n\t\t\t$!column.obj.name\t= #${column.name}# #if($foreach.hasNext),#end   <!-- ${column.comment} -->\n#end\n\t\tWHERE\n            $!pk.obj.name = #$!pk.name#\n            \n\t</update>\n\n</sqlMap>\n"}, {"name": "entity.js.vm", "code": "## 引入宏定义\n$!{baowu.vm}\n\n## 使用宏定义设置回调（保存位置与文件）\n$!callback.setSavePath($baowuViewDir)\n$!callback.setFileName($entityName+\".js\")\n\n$(function () {\n\n    $('#QUERY').on('click', function (e) {\n        resultGrid.dataSource.page(1);\n    });\n    //当前修改行\n    var editRow =0;\n\n    IPLATUI.EFGrid = {\n        \"result\": {\n            pageable: {\n                pageSize: 10,\n                pageSizes: [10, 20, 50, 100, 500]\n            },\n            // 列是否可以拖动切换位置\n            reorderable: true,\n            columns: [],\n            beforeEdit: function (e) {\n                editRow = e.row;\n                // 获取当前修改行\n            #foreach($column in $tableInfo.fullColumn)\n                // if(e.field === \"${column.name}\") { ${foreach.index};}\n            #end\n            },\n            afterEdit: function (e) {\n                // 执行自定义逻辑代码，假设根据逻辑要求不关闭单元格编辑状态\n            #foreach($column in $tableInfo.fullColumn)\n                //if (e.model[\"${column.name}\"] === \"\") {\n                //    e.model.set(\"${column.name}\", \"\");\n                //}\n            #end\n            },\n            /**\n             * 双击数据行时触发的事件，注意编辑状态时不会触发\n             * @param e\n             * e.sender     kendoGrid对象，resultGrid\n             * e.model      双击的行数据，kendo.data.Model\n             * e.row        当前行的行号\n             * e.tr         行的tr,包括固定列和数据列 jquery对象\n             */\n            onRowDblClick: function (e) {\n                let dataItems = resultGrid.getDataItems();\n                var model = dataItems[e.row];\n            #foreach($column in $tableInfo.fullColumn)\n                //model.set(\"${column.name}\", \"\");\n            #end\n\n                //var popupGridWindow = $(\"#querynum\").data(\"kendoWindow\");\n                //popupGridWindow.close();\n            }\n        },\n    }\n});"}, {"name": "entity.jsp.vm", "code": "## 引入宏定义\n$!{baowu.vm}\n\n## 使用宏定义设置回调（保存位置与文件）\n$!callback.setSavePath($baowuViewDir)\n$!callback.setFileName($entityName+\".jsp\")\n\n<!DOCTYPE html>\n<%@ page contentType=\"text/html; charset=UTF-8\" %>\n<%@ taglib uri=\"http://java.sun.com/jsp/jstl/core\" prefix=\"c\" %>\n<%@ taglib prefix=\"EF\" tagdir=\"/WEB-INF/tags/EF\" %>\n<%@ page import=\"com.baosight.iplat4j.core.ei.EiInfo\" %>\n<%@ page import=\"com.baosight.iplat4j.core.resource.I18nMessages\" %>\n\n<c:set var=\"ctx\" value=\"${pageContext.request.contextPath}\"/>\n<EF:EFPage prefix=\"imc\">\n    <EF:EFRegion id=\"inqu\" title=\"查询条件\">\n        <div class=\"row\">\n#foreach($column in $tableInfo.otherColumn)\n            <EF:EFInput ename=\"inqu_status-0-${column.name}\" cname=\"${column.comment}\" colWidth=\"3\" ratio=\"4:8\"/>\n#end\n        </div>\n    </EF:EFRegion>\n\n    <EF:EFRegion id=\"result\" title=\"记录集\">\n        <EF:EFGrid blockId=\"result\" isFloat=\"true\" autoDraw=\"no\" toolbarConfig=\"true\" needAuth=\"true\">\n            <EF:EFColumn ename=\"${pk.obj.name.toLowerCase()}\" cname=\"\" hidden=\"true\"/>\n#foreach($column in $tableInfo.otherColumn)\n            <EF:EFColumn ename=\"${column.name}\" cname=\"${column.comment}\" sort=\"true\" required=\"true\" align=\"center\"/>\n#end\n        </EF:EFGrid>\n    </EF:EFRegion>\n</EF:EFPage>"}, {"name": "service.java.vm", "code": "## 引入宏定义\n$!{baowu.vm}\n\n## 使用宏定义设置回调（保存位置与文件）\n$!callback.setSavePath($tableInfo.savePath+\"/service\")\n$!callback.setFileName(\"Service\"+$entityName+\".java\")\n\n## 使用宏定义设置包后缀,打印包路径\n#setPackageSuffix(\"service\")\n\n## 使用全局变量实现默认包导入\n$!{autoImport.vm}\nimport com.baosight.elim.common.utils.GlobalUtils;\nimport com.baosight.imc.common.utils.BeanUtil;\nimport com.baosight.imc.common.utils.FormUtils;\nimport com.baosight.imc.interfaces.vz.bm.domain.VZBM1300;\nimport com.baosight.imc.interfaces.vz.bm.service.ServiceVZBM1300;\nimport com.baosight.imc.xb.ai.domain.XBAI13;\nimport com.baosight.iplat4j.core.ei.EiConstant;\nimport com.baosight.iplat4j.core.ei.EiInfo;\nimport com.baosight.iplat4j.core.service.impl.ServiceEPBase;\nimport org.apache.commons.lang.StringUtils;\nimport org.jetbrains.annotations.Nullable;\nimport org.slf4j.Logger;\nimport org.slf4j.LoggerFactory;\n\nimport java.io.UnsupportedEncodingException;\nimport java.math.BigDecimal;\nimport java.util.ArrayList;\nimport java.util.HashMap;\nimport java.util.List;\nimport java.util.Objects;\n\n/**\n * ${tableInfo.comment} 服务类\n * @DateTime ${time.currTime('yyyy-MM-dd HH:mm:ss')} \n * <AUTHOR>\n */\npublic class Service${entityName} extends ServiceEPBase {\n\n    private static final Logger logger = LoggerFactory.getLogger(Service${entityName}.class);\n\n    @Override\n    public EiInfo initLoad(EiInfo inInfo) {\n        return super.initLoad(inInfo, new ${entityName}());\n    }\n\n    /**\n     * 查询功能\n     *\n     * @param inInfo\n     * @return\n     */\n    @Override\n    public EiInfo query(EiInfo inInfo) {\n        EiInfo outInfo = super.query(inInfo, \"${entityName}.query\", new ${entityName}());\n        return outInfo;\n    }\n\n    /**\n     * 新增功能\n     *\n     * @param inInfo\n     * @return\n     */\n    @Override\n    public EiInfo insert(EiInfo inInfo) {\n\n        EiInfo outInfo = new EiInfo();\n        List rows = new ArrayList();\n        for (int i = 0; i < inInfo.getBlock(EiConstant.resultBlock).getRowCount(); i++) {\n            ${entityName} ${entityInstanceName} = new ${entityName}();\n            ${entityInstanceName}.fromMap(inInfo.getRow(EiConstant.resultBlock, i));\n            BeanUtil.beanAttributeValueTrim(${entityInstanceName});\n            FormUtils.setCreatorProertyFromXservice(${entityInstanceName});\n            boolean isCheckPass = check${entityName}(${entityInstanceName}, outInfo);\n            if (!isCheckPass) {\n                if (StringUtils.isNotBlank(outInfo.getMsg())) {\n                    outInfo.setMsg(errorString(\"VZBM0810001\", \"第\" + (i + 1) + \"行\" + outInfo.getMsg()));\n                }\n                return outInfo;\n            }\n            int countDuplication = dao.count(\"${entityName}.duplication\", new HashMap<String, Object>() {{\n                #foreach($column in $tableInfo.fullColumn)\n                        put(\"${column.name}\", ${entityInstanceName}.get${tool.firstUpperCase($column.name)}());\n                #end\n            }});\n            if (countDuplication > 0) {\n                outInfo.setStatus(EiConstant.STATUS_FAILURE);\n                outInfo.setMsg(errorString(\"VZBM0810001\", \"第\" + (i + 1) + \"行记录重复！\"));\n                return outInfo;\n            }\n            rows.add(${entityInstanceName}.toMap());\n        }\n        inInfo.getBlock(EiConstant.resultBlock).setRows(rows);\n\n        return insert(inInfo, \"${entityName}.insert\");\n    }\n\n    @Nullable\n    private boolean check${entityName}(${entityName} ${entityInstanceName}, EiInfo outInfo) {\n        try {\n            #foreach($column in $tableInfo.otherColumn)\n                #if($column.obj.isNotNull())\n                    ${column.shortType} ${column.name} = ${entityInstanceName}.get${tool.firstUpperCase($column.name)}();\n                    #if($column.shortType==\"String\")\n                    if (StringUtils.isEmpty(${column.name})) {\n                    #else\n                    if (Objects.isNull(${column.name})) {\n                    #end\n                        outInfo.setStatus(EiConstant.STATUS_FAILURE);\n                        outInfo.setMsg(\"${column.comment}不能为空！\");\n                        return false;\n                    }\n                #end\n                    #if($column.shortType==\"String\")\n                    if (${column.name}.getBytes(\"gbk\").length > ${column.obj.getDataType().getLength()}) {\n                        outInfo.setStatus(EiConstant.STATUS_FAILURE);\n                        outInfo.setMsg(\"${column.comment}最大为${column.obj.getDataType().getLength()}个文字！\");\n                        return false;\n                    }\n                    #end\n            #end\n\n        } catch (UnsupportedEncodingException e) {\n            e.printStackTrace();\n            throw new RuntimeException(e);\n        }\n        return true;\n    }\n\n    /**\n     * @param code\n     * @param msg\n     * @return\n     */\n    private String errorString(String code, String msg) {\n        VZBM1300 queryMsg = new VZBM1300();\n        queryMsg.setErrorNum(code);\n        queryMsg.setFormEname(\"${entityName}\");\n        GlobalUtils.setCreatorProerty(queryMsg);\n        return ServiceVZBM1300.getMessageTextByErrorNumAndRecord(new String[]{msg}, queryMsg);\n    }\n\n    /**\n     * 修改功能\n     *\n     * @param inInfo\n     * @return\n     */\n    @Override\n    public EiInfo update(EiInfo inInfo) {\n        EiInfo outInfo = new EiInfo();\n\n        for (int i = 0; i < inInfo.getBlock(EiConstant.resultBlock).getRowCount(); i++) {\n            ${entityName} ${entityInstanceName} = new ${entityName}();\n            ${entityInstanceName}.fromMap(inInfo.getRow(EiConstant.resultBlock, i));\n            BeanUtil.beanAttributeValueTrim(${entityInstanceName});\n            FormUtils.setCreatorProertyFromXservice(${entityInstanceName});\n            boolean isCheckPass = check${entityName}(${entityInstanceName}, outInfo);\n            if (!isCheckPass) {\n                if (StringUtils.isNotBlank(outInfo.getMsg())) {\n                    outInfo.setMsg(errorString(\"VZBM0810001\", \"第\" + (i + 1) + \"行\" + outInfo.getMsg()));\n                }\n                return outInfo;\n            }\n            if (StringUtils.isEmpty(${entityInstanceName}.get${pkFirstUpperCase}())) {\n                outInfo.setStatus(EiConstant.STATUS_FAILURE);\n                outInfo.setMsg(\"记录$!pk.obj.name为空！\");\n                return outInfo;\n            }\n            int countDuplication = dao.count(\"${entityName}.duplication\", new HashMap<String, Object>() {{\n                #foreach($column in $tableInfo.fullColumn)\n                put(\"${column.name}\", ${entityInstanceName}.get${tool.firstUpperCase($column.name)}());\n                #end\n                put(\"notContain$!pkFirstUpperCase\", ${entityInstanceName}.get$!pkFirstUpperCase());\n            }});\n            if (countDuplication > 0) {\n                outInfo.setStatus(EiConstant.STATUS_FAILURE);\n                outInfo.setMsg(errorString(\"VZBM0810001\", \"第\" + (i + 1) + \"行记录重复！\"));\n                return outInfo;\n            }\n        }\n\n        return super.update(inInfo, \"${entityName}.update\");\n    }\n\n    /**\n     * 删除功能\n     *\n     * @param inInfo\n     * @return\n     */\n    @Override\n    public EiInfo delete(EiInfo inInfo) {\n        EiInfo outInfo = super.delete(inInfo, \"${entityName}.delete\");\n        return outInfo;\n    }\n\n}"}, {"name": "test.java.vm", "code": "## 引入宏定义\n$!{baowu.vm}\n\n## 使用宏定义设置回调（保存位置与文件）\n$!callback.setSavePath($baowuTestDir+\"/service\")\n$!callback.setFileName(\"Service\"+$entityName+\"Test.java\")\n\n#setPackageSuffix(\"service\")\n$!{autoImport.vm}\nimport com.baosight.BaseTest;\nimport com.baosight.iplat4j.core.ei.EiInfo;\nimport org.junit.Before;\nimport org.junit.Test;\n\n/**\n * ${tableInfo.comment} 服务测试类\n * @DateTime ${time.currTime('yyyy-MM-dd HH:mm:ss')} \n * <AUTHOR>\n */\npublic class Service${entityName}Test extends BaseTest {\n    Service${entityName} service${entityName};\n    @Before\n    public void init() {\n        setSessionUser();\n        service${entityName} =(Service${entityName})getServiceBean(\"${entityName}\");\n    }\n\n    private ${entityName} get${entityName}() {\n        ${entityName} ${entityInstanceName} = new ${entityName}();\n        #foreach($column in $tableInfo.fullColumn)\n        ${entityInstanceName}.set${tool.firstUpperCase($column.name)}(${column.obj.getDefaultValue()});\n        #end\n        return ${entityInstanceName};\n    }\n\n    @Test\n    public void testQuery() {\n        EiInfo info =service${entityName}.query(new EiInfo(){{\n\n        }});\n        System.out.println(info.toJSONString());\n    }\n\n    @Test\n    public void testInsert() {\n                inInfo.getBlock(EiConstant.resultBlock).setRows(rows);\n\n        EiBlock eiBlock=new EiBlock(EiConstant.resultBlock)\n                .setRows(\n                    new ArrayList<${entityName}>(){{\n                        add(get${entityName}());\n                    }}\n                );\n        EiInfo info =service${entityName}.insert(new EiInfo(){{\n            setBlock(eiBlock);\n        }});\n            \n        System.out.println(info.toJSONString());\n    }\n\n    @Test\n    public void testUpdate() {\n        EiBlock eiBlock=new EiBlock(EiConstant.resultBlock)\n                .setRows(\n                    new ArrayList<${entityName}>(){{\n                        add(get${entityName}());\n                    }}\n                );\n        EiInfo info =service${entityName}.update(new EiInfo(){{\n            setBlock(eiBlock);\n        }});\n        System.out.println(info.toJSONString());\n    }\n\n    @Test\n    public void testDelete() {\n        EiBlock eiBlock=new EiBlock(EiConstant.resultBlock)\n                .setRows(\n                    new ArrayList<${entityName}>(){{\n                        add(get${entityName}());\n                    }}\n                );\n        EiInfo info =service${entityName}.delete(new EiInfo(){{\n            setBlock(eiBlock);\n        }});\n        System.out.println(info.toJSONString());\n    }\n\n}"}, {"name": "setting.sql.vm", "code": "## 引入宏定义\n$!{baowu.vm}\n\n## 使用宏定义设置回调（保存位置与文件）\n$!callback.setSavePath(\"C:\\\\bwwp\\\\bwsql\")\n$!callback.setFileName($entityName+\".sql\")\n\nINSERT INTO IPLAT4J.TEDFA00 (REC_CREATOR,REC_CREATE_TIME,REC_REVISOR,REC_REVISE_TIME,FORM_ENAME,FORM_CNAME,FORM_LOAD_PATH,FORM_TYPE,MODULE_ENAME_1,MODULE_ENAME_2,INIT_LOAD_SERVICE_ENAME,IS_AUTH,FORM_PARAM,SUBAPP_CODE,ICON_INFO,BUSINESS_CATEGORY,OPERATE_TYPE,TENANT_ID,ARCHIVE_FLAG) VALUES ('KF2269','${time.currTime('yyyyMMddHHmmss')}',' ',' ','${entityName}','${tableInfo.comment}',' ','0','${entityName.substring(0,2)}','${entityName.substring(2,4)}',' ','0',' ',' ',' ',' ','query',' ',' ');\nINSERT INTO IPLAT4J.TEDFA01 (REC_CREATOR,REC_CREATE_TIME,REC_REVISOR,REC_REVISE_TIME,ARCHIVE_FLAG,FORM_ENAME,REGION_ID,BUTTON_ENAME,BUTTON_CNAME,BUTTON_DESC,NODE_SORT_ID,IS_AUTH,URI,LAYOUT,\"POSITION\",TENANT_ID,BUSINESS_CATEGORY,OPERATE_TYPE) VALUES ('KF2269','${time.currTime('yyyyMMddHHmmss')}','KF2269','${time.currTime('yyyyMMddHHmmss')}',' ','${entityName}','GRID:EF_GRID_RESULT','DELETE','删除',' ','3','0',' ','0','0',' ',' ','delete');\nINSERT INTO IPLAT4J.TEDFA01 (REC_CREATOR,REC_CREATE_TIME,REC_REVISOR,REC_REVISE_TIME,ARCHIVE_FLAG,FORM_ENAME,REGION_ID,BUTTON_ENAME,BUTTON_CNAME,BUTTON_DESC,NODE_SORT_ID,IS_AUTH,URI,LAYOUT,\"POSITION\",TENANT_ID,BUSINESS_CATEGORY,OPERATE_TYPE) VALUES ('KF2269','${time.currTime('yyyyMMddHHmmss')}','KF2269','${time.currTime('yyyyMMddHHmmss')}',' ','${entityName}','GRID:EF_GRID_RESULT','INSERT','新增',' ','1','0','css:k-add','2','0',' ',' ','add');\nINSERT INTO IPLAT4J.TEDFA01 (REC_CREATOR,REC_CREATE_TIME,REC_REVISOR,REC_REVISE_TIME,ARCHIVE_FLAG,FORM_ENAME,REGION_ID,BUTTON_ENAME,BUTTON_CNAME,BUTTON_DESC,NODE_SORT_ID,IS_AUTH,URI,LAYOUT,\"POSITION\",TENANT_ID,BUSINESS_CATEGORY,OPERATE_TYPE) VALUES ('KF2269','${time.currTime('yyyyMMddHHmmss')}','KF2269','${time.currTime('yyyyMMddHHmmss')}',' ','${entityName}','GRID:EF_GRID_RESULT','INSERTSAVE','新增',' ','1','0','','0','0',' ',' ','insert');\nINSERT INTO IPLAT4J.TEDFA01 (REC_CREATOR,REC_CREATE_TIME,REC_REVISOR,REC_REVISE_TIME,ARCHIVE_FLAG,FORM_ENAME,REGION_ID,BUTTON_ENAME,BUTTON_CNAME,BUTTON_DESC,NODE_SORT_ID,IS_AUTH,URI,LAYOUT,\"POSITION\",TENANT_ID,BUSINESS_CATEGORY,OPERATE_TYPE) VALUES ('KF2269','${time.currTime('yyyyMMddHHmmss')}','KF2269','${time.currTime('yyyyMMddHHmmss')}',' ','${entityName}','INQU','QUERY','查询',' ','1','0',' ','0','0',' ',' ','query');\nINSERT INTO IPLAT4J.TEDFA01 (REC_CREATOR,REC_CREATE_TIME,REC_REVISOR,REC_REVISE_TIME,ARCHIVE_FLAG,FORM_ENAME,REGION_ID,BUTTON_ENAME,BUTTON_CNAME,BUTTON_DESC,NODE_SORT_ID,IS_AUTH,URI,LAYOUT,\"POSITION\",TENANT_ID,BUSINESS_CATEGORY,OPERATE_TYPE) VALUES ('KF2269','${time.currTime('yyyyMMddHHmmss')}','KF2269','${time.currTime('yyyyMMddHHmmss')}',' ','${entityName}','INQU','REST','重置',' ','2','0',' ','0','0',' ',' ','query');\nINSERT INTO IPLAT4J.TEDFA01 (REC_CREATOR,REC_CREATE_TIME,REC_REVISOR,REC_REVISE_TIME,ARCHIVE_FLAG,FORM_ENAME,REGION_ID,BUTTON_ENAME,BUTTON_CNAME,BUTTON_DESC,NODE_SORT_ID,IS_AUTH,URI,LAYOUT,\"POSITION\",TENANT_ID,BUSINESS_CATEGORY,OPERATE_TYPE) VALUES ('KF2269','${time.currTime('yyyyMMddHHmmss')}','KF2269','${time.currTime('yyyyMMddHHmmss')}',' ','${entityName}','GRID:EF_GRID_RESULT','UPDATESAVE','修改',' ','2','0',' ','0','0',' ',' ','update');\n"}]}, "Default": {"name": "<PERSON><PERSON><PERSON>", "elementList": [{"name": "controller.java.vm", "code": "##定义初始变量\n#set($tableName = $tool.append($tableInfo.name, \"Controller\"))\n##设置回调\n$!callback.setFileName($tool.append($tableName, \".java\"))\n$!callback.setSavePath($tool.append($tableInfo.savePath, \"/controller\"))\n##拿到主键\n#if(!$tableInfo.pkColumn.isEmpty())\n    #set($pk = $tableInfo.pkColumn.get(0))\n#end\n\n#if($tableInfo.savePackageName)package $!{tableInfo.savePackageName}.#{end}controller;\n\nimport $!{tableInfo.savePackageName}.entity.$!{tableInfo.name};\nimport $!{tableInfo.savePackageName}.service.$!{tableInfo.name}Service;\nimport org.springframework.data.domain.Page;\nimport org.springframework.data.domain.PageRequest;\nimport org.springframework.http.ResponseEntity;\nimport org.springframework.web.bind.annotation.*;\n\nimport javax.annotation.Resource;\n\n/**\n * $!{tableInfo.comment}($!{tableInfo.name})表控制层\n *\n * <AUTHOR>\n * @since $!time.currTime()\n */\n@RestController\n@RequestMapping(\"$!tool.firstLowerCase($tableInfo.name)\")\npublic class $!{tableName} {\n    /**\n     * 服务对象\n     */\n    @Resource\n    private $!{tableInfo.name}Service $!tool.firstLowerCase($tableInfo.name)Service;\n\n    /**\n     * 分页查询\n     *\n     * @param $!{tool.firstLowerCase($tableInfo.name)} 筛选条件\n     * @param pageRequest      分页对象\n     * @return 查询结果\n     */\n    @GetMapping\n    public ResponseEntity<Page<$!{tableInfo.name}>> queryByPage($!{tableInfo.name} $!{tool.firstLowerCase($tableInfo.name)}, PageRequest pageRequest) {\n        return ResponseEntity.ok(this.$!{tool.firstLowerCase($tableInfo.name)}Service.queryByPage($!{tool.firstLowerCase($tableInfo.name)}, pageRequest));\n    }\n\n    /**\n     * 通过主键查询单条数据\n     *\n     * @param id 主键\n     * @return 单条数据\n     */\n    @GetMapping(\"{id}\")\n    public ResponseEntity<$!{tableInfo.name}> queryById(@PathVariable(\"id\") $!pk.shortType id) {\n        return ResponseEntity.ok(this.$!{tool.firstLowerCase($tableInfo.name)}Service.queryById(id));\n    }\n\n    /**\n     * 新增数据\n     *\n     * @param $!{tool.firstLowerCase($tableInfo.name)} 实体\n     * @return 新增结果\n     */\n    @PostMapping\n    public ResponseEntity<$!{tableInfo.name}> add($!{tableInfo.name} $!{tool.firstLowerCase($tableInfo.name)}) {\n        return ResponseEntity.ok(this.$!{tool.firstLowerCase($tableInfo.name)}Service.insert($!{tool.firstLowerCase($tableInfo.name)}));\n    }\n\n    /**\n     * 编辑数据\n     *\n     * @param $!{tool.firstLowerCase($tableInfo.name)} 实体\n     * @return 编辑结果\n     */\n    @PutMapping\n    public ResponseEntity<$!{tableInfo.name}> edit($!{tableInfo.name} $!{tool.firstLowerCase($tableInfo.name)}) {\n        return ResponseEntity.ok(this.$!{tool.firstLowerCase($tableInfo.name)}Service.update($!{tool.firstLowerCase($tableInfo.name)}));\n    }\n\n    /**\n     * 删除数据\n     *\n     * @param id 主键\n     * @return 删除是否成功\n     */\n    @DeleteMapping\n    public ResponseEntity<Boolean> deleteById($!pk.shortType id) {\n        return ResponseEntity.ok(this.$!{tool.firstLowerCase($tableInfo.name)}Service.deleteById(id));\n    }\n\n}\n"}, {"name": "dao.java.vm", "code": "##定义初始变量\n#set($tableName = $tool.append($tableInfo.name, \"Dao\"))\n##设置回调\n$!callback.setFileName($tool.append($tableName, \".java\"))\n$!callback.setSavePath($tool.append($tableInfo.savePath, \"/dao\"))\n\n##拿到主键\n#if(!$tableInfo.pkColumn.isEmpty())\n    #set($pk = $tableInfo.pkColumn.get(0))\n#end\n\n#if($tableInfo.savePackageName)package $!{tableInfo.savePackageName}.#{end}dao;\n\nimport $!{tableInfo.savePackageName}.entity.$!{tableInfo.name};\nimport org.apache.ibatis.annotations.Param;\nimport org.springframework.data.domain.Pageable;\nimport java.util.List;\n\n/**\n * $!{tableInfo.comment}($!{tableInfo.name})表数据库访问层\n *\n * <AUTHOR>\n * @since $!time.currTime()\n */\npublic interface $!{tableName} {\n\n    /**\n     * 通过ID查询单条数据\n     *\n     * @param $!pk.name 主键\n     * @return 实例对象\n     */\n    $!{tableInfo.name} queryById($!pk.shortType $!pk.name);\n\n    /**\n     * 查询指定行数据\n     *\n     * @param $!tool.firstLowerCase($!{tableInfo.name}) 查询条件\n     * @param pageable         分页对象\n     * @return 对象列表\n     */\n    List<$!{tableInfo.name}> queryAllByLimit($!{tableInfo.name} $!tool.firstLowerCase($!{tableInfo.name}), @Param(\"pageable\") Pageable pageable);\n\n    /**\n     * 统计总行数\n     *\n     * @param $!tool.firstLowerCase($!{tableInfo.name}) 查询条件\n     * @return 总行数\n     */\n    long count($!{tableInfo.name} $!tool.firstLowerCase($!{tableInfo.name}));\n\n    /**\n     * 新增数据\n     *\n     * @param $!tool.firstLowerCase($!{tableInfo.name}) 实例对象\n     * @return 影响行数\n     */\n    int insert($!{tableInfo.name} $!tool.firstLowerCase($!{tableInfo.name}));\n\n    /**\n     * 批量新增数据（MyBatis原生foreach方法）\n     *\n     * @param entities List<$!{tableInfo.name}> 实例对象列表\n     * @return 影响行数\n     */\n    int insertBatch(@Param(\"entities\") List<$!{tableInfo.name}> entities);\n\n    /**\n     * 批量新增或按主键更新数据（MyBatis原生foreach方法）\n     *\n     * @param entities List<$!{tableInfo.name}> 实例对象列表\n     * @return 影响行数\n     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参\n     */\n    int insertOrUpdateBatch(@Param(\"entities\") List<$!{tableInfo.name}> entities);\n\n    /**\n     * 修改数据\n     *\n     * @param $!tool.firstLowerCase($!{tableInfo.name}) 实例对象\n     * @return 影响行数\n     */\n    int update($!{tableInfo.name} $!tool.firstLowerCase($!{tableInfo.name}));\n\n    /**\n     * 通过主键删除数据\n     *\n     * @param $!pk.name 主键\n     * @return 影响行数\n     */\n    int deleteById($!pk.shortType $!pk.name);\n\n}\n"}, {"name": "debug.json.vm", "code": "// 禁止将生成结果写入到文件\n$!callback.setWriteFile(false)\n\n//调试表原始对象\n$!tool.debug($tableInfo.obj)\n\n//调试列原始对象\n$!tool.debug($tableInfo.fullColumn.get(0).obj)\n\n//调试列原始列类型\n$!tool.debug($tableInfo.fullColumn.get(0).obj.dataType)\n\n//获取原始列类型中的字段\nsqlType = $!tool.getField($tableInfo.fullColumn.get(0).obj.dataType, \"typeName\")\n\n//执行原始列类型中的方法\nsqlTypeLen = $!tableInfo.fullColumn.get(0).obj.dataType.getLength()\n"}, {"name": "entity.java.vm", "code": "##引入宏定义\n$!{define.vm}\n\n##使用宏定义设置回调（保存位置与文件后缀）\n#save(\"/entity\", \".java\")\n\n##使用宏定义设置包后缀\n#setPackageSuffix(\"entity\")\n\n##使用全局变量实现默认包导入\n$!{autoImport.vm}\nimport java.io.Serializable;\n\n##使用宏定义实现类注释信息\n#tableComment(\"实体类\")\npublic class $!{tableInfo.name} implements Serializable {\n    private static final long serialVersionUID = $!tool.serial();\n#foreach($column in $tableInfo.fullColumn)\n    #if(${column.comment})/**\n     * ${column.comment}\n     */#end\n\n    private $!{tool.getClsNameByFullName($column.type)} $!{column.name};\n#end\n\n#foreach($column in $tableInfo.fullColumn)\n##使用宏定义实现get,set方法\n#getSetMethod($column)\n#end\n\n}\n"}, {"name": "mapper.xml.vm", "code": "##引入mybatis支持\n$!{mybatisSupport.vm}\n\n##设置保存名称与保存位置\n$!callback.setFileName($tool.append($!{tableInfo.name}, \"Dao.xml\"))\n$!callback.setSavePath($tool.append($modulePath, \"/src/main/resources/mapper\"))\n\n##拿到主键\n#if(!$tableInfo.pkColumn.isEmpty())\n    #set($pk = $tableInfo.pkColumn.get(0))\n#end\n\n<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">\n<mapper namespace=\"$!{tableInfo.savePackageName}.dao.$!{tableInfo.name}Dao\">\n\n    <resultMap type=\"$!{tableInfo.savePackageName}.entity.$!{tableInfo.name}\" id=\"$!{tableInfo.name}Map\">\n#foreach($column in $tableInfo.fullColumn)\n        <result property=\"$!column.name\" column=\"$!column.obj.name\" jdbcType=\"$!column.ext.jdbcType\"/>\n#end\n    </resultMap>\n\n    <!--查询单个-->\n    <select id=\"queryById\" resultMap=\"$!{tableInfo.name}Map\">\n        select\n          #allSqlColumn()\n\n        from $!tableInfo.obj.name\n        where $!pk.obj.name = #{$!pk.name}\n    </select>\n\n    <!--查询指定行数据-->\n    <select id=\"queryAllByLimit\" resultMap=\"$!{tableInfo.name}Map\">\n        select\n          #allSqlColumn()\n\n        from $!tableInfo.obj.name\n        <where>\n#foreach($column in $tableInfo.fullColumn)\n            <if test=\"$!column.name != null#if($column.type.equals(\"java.lang.String\")) and $!column.name != ''#end\">\n                and $!column.obj.name = #{$!column.name}\n            </if>\n#end\n        </where>\n        limit #{pageable.offset}, #{pageable.pageSize}\n    </select>\n\n    <!--统计总行数-->\n    <select id=\"count\" resultType=\"java.lang.Long\">\n        select count(1)\n        from $!tableInfo.obj.name\n        <where>\n#foreach($column in $tableInfo.fullColumn)\n            <if test=\"$!column.name != null#if($column.type.equals(\"java.lang.String\")) and $!column.name != ''#end\">\n                and $!column.obj.name = #{$!column.name}\n            </if>\n#end\n        </where>\n    </select>\n\n    <!--新增所有列-->\n    <insert id=\"insert\" keyProperty=\"$!pk.name\" useGeneratedKeys=\"true\">\n        insert into $!{tableInfo.obj.name}(#foreach($column in $tableInfo.otherColumn)$!column.obj.name#if($foreach.hasNext), #end#end)\n        values (#foreach($column in $tableInfo.otherColumn)#{$!{column.name}}#if($foreach.hasNext), #end#end)\n    </insert>\n\n    <insert id=\"insertBatch\" keyProperty=\"$!pk.name\" useGeneratedKeys=\"true\">\n        insert into $!{tableInfo.obj.name}(#foreach($column in $tableInfo.otherColumn)$!column.obj.name#if($foreach.hasNext), #end#end)\n        values\n        <foreach collection=\"entities\" item=\"entity\" separator=\",\">\n        (#foreach($column in $tableInfo.otherColumn)#{entity.$!{column.name}}#if($foreach.hasNext), #end#end)\n        </foreach>\n    </insert>\n\n    <insert id=\"insertOrUpdateBatch\" keyProperty=\"$!pk.name\" useGeneratedKeys=\"true\">\n        insert into $!{tableInfo.obj.name}(#foreach($column in $tableInfo.otherColumn)$!column.obj.name#if($foreach.hasNext), #end#end)\n        values\n        <foreach collection=\"entities\" item=\"entity\" separator=\",\">\n            (#foreach($column in $tableInfo.otherColumn)#{entity.$!{column.name}}#if($foreach.hasNext), #end#end)\n        </foreach>\n        on duplicate key update\n        #foreach($column in $tableInfo.otherColumn)$!column.obj.name = values($!column.obj.name)#if($foreach.hasNext),\n        #end#end\n\n    </insert>\n\n    <!--通过主键修改数据-->\n    <update id=\"update\">\n        update $!{tableInfo.obj.name}\n        <set>\n#foreach($column in $tableInfo.otherColumn)\n            <if test=\"$!column.name != null#if($column.type.equals(\"java.lang.String\")) and $!column.name != ''#end\">\n                $!column.obj.name = #{$!column.name},\n            </if>\n#end\n        </set>\n        where $!pk.obj.name = #{$!pk.name}\n    </update>\n\n    <!--通过主键删除-->\n    <delete id=\"deleteById\">\n        delete from $!{tableInfo.obj.name} where $!pk.obj.name = #{$!pk.name}\n    </delete>\n\n</mapper>\n"}, {"name": "service.java.vm", "code": "##定义初始变量\n#set($tableName = $tool.append($tableInfo.name, \"Service\"))\n##设置回调\n$!callback.setFileName($tool.append($tableName, \".java\"))\n$!callback.setSavePath($tool.append($tableInfo.savePath, \"/service\"))\n\n##拿到主键\n#if(!$tableInfo.pkColumn.isEmpty())\n    #set($pk = $tableInfo.pkColumn.get(0))\n#end\n\n#if($tableInfo.savePackageName)package $!{tableInfo.savePackageName}.#{end}service;\n\nimport $!{tableInfo.savePackageName}.entity.$!{tableInfo.name};\nimport org.springframework.data.domain.Page;\nimport org.springframework.data.domain.PageRequest;\n\n/**\n * $!{tableInfo.comment}($!{tableInfo.name})表服务接口\n *\n * <AUTHOR>\n * @since $!time.currTime()\n */\npublic interface $!{tableName} {\n\n    /**\n     * 通过ID查询单条数据\n     *\n     * @param $!pk.name 主键\n     * @return 实例对象\n     */\n    $!{tableInfo.name} queryById($!pk.shortType $!pk.name);\n\n    /**\n     * 分页查询\n     *\n     * @param $!tool.firstLowerCase($!{tableInfo.name}) 筛选条件\n     * @param pageRequest      分页对象\n     * @return 查询结果\n     */\n    Page<$!{tableInfo.name}> queryByPage($!{tableInfo.name} $!tool.firstLowerCase($!{tableInfo.name}), PageRequest pageRequest);\n\n    /**\n     * 新增数据\n     *\n     * @param $!tool.firstLowerCase($!{tableInfo.name}) 实例对象\n     * @return 实例对象\n     */\n    $!{tableInfo.name} insert($!{tableInfo.name} $!tool.firstLowerCase($!{tableInfo.name}));\n\n    /**\n     * 修改数据\n     *\n     * @param $!tool.firstLowerCase($!{tableInfo.name}) 实例对象\n     * @return 实例对象\n     */\n    $!{tableInfo.name} update($!{tableInfo.name} $!tool.firstLowerCase($!{tableInfo.name}));\n\n    /**\n     * 通过主键删除数据\n     *\n     * @param $!pk.name 主键\n     * @return 是否成功\n     */\n    boolean deleteById($!pk.shortType $!pk.name);\n\n}"}, {"name": "serviceImpl.java.vm", "code": "##定义初始变量\n#set($tableName = $tool.append($tableInfo.name, \"ServiceImpl\"))\n##设置回调\n$!callback.setFileName($tool.append($tableName, \".java\"))\n$!callback.setSavePath($tool.append($tableInfo.savePath, \"/service/impl\"))\n\n##拿到主键\n#if(!$tableInfo.pkColumn.isEmpty())\n    #set($pk = $tableInfo.pkColumn.get(0))\n#end\n\n#if($tableInfo.savePackageName)package $!{tableInfo.savePackageName}.#{end}service.impl;\n\nimport $!{tableInfo.savePackageName}.entity.$!{tableInfo.name};\nimport $!{tableInfo.savePackageName}.dao.$!{tableInfo.name}Dao;\nimport $!{tableInfo.savePackageName}.service.$!{tableInfo.name}Service;\nimport org.springframework.stereotype.Service;\nimport org.springframework.data.domain.Page;\nimport org.springframework.data.domain.PageImpl;\nimport org.springframework.data.domain.PageRequest;\n\nimport javax.annotation.Resource;\n\n/**\n * $!{tableInfo.comment}($!{tableInfo.name})表服务实现类\n *\n * <AUTHOR>\n * @since $!time.currTime()\n */\n@Service(\"$!tool.firstLowerCase($!{tableInfo.name})Service\")\npublic class $!{tableName} implements $!{tableInfo.name}Service {\n    @Resource\n    private $!{tableInfo.name}Dao $!tool.firstLowerCase($!{tableInfo.name})Dao;\n\n    /**\n     * 通过ID查询单条数据\n     *\n     * @param $!pk.name 主键\n     * @return 实例对象\n     */\n    @Override\n    public $!{tableInfo.name} queryById($!pk.shortType $!pk.name) {\n        return this.$!{tool.firstLowerCase($!{tableInfo.name})}Dao.queryById($!pk.name);\n    }\n\n    /**\n     * 分页查询\n     *\n     * @param $!{tool.firstLowerCase($tableInfo.name)} 筛选条件\n     * @param pageRequest      分页对象\n     * @return 查询结果\n     */\n    @Override\n    public Page<$!{tableInfo.name}> queryByPage($!{tableInfo.name} $!{tool.firstLowerCase($tableInfo.name)}, PageRequest pageRequest) {\n        long total = this.$!{tool.firstLowerCase($tableInfo.name)}Dao.count($!{tool.firstLowerCase($tableInfo.name)});\n        return new PageImpl<>(this.$!{tool.firstLowerCase($tableInfo.name)}Dao.queryAllByLimit($!{tool.firstLowerCase($tableInfo.name)}, pageRequest), pageRequest, total);\n    }\n\n    /**\n     * 新增数据\n     *\n     * @param $!tool.firstLowerCase($!{tableInfo.name}) 实例对象\n     * @return 实例对象\n     */\n    @Override\n    public $!{tableInfo.name} insert($!{tableInfo.name} $!tool.firstLowerCase($!{tableInfo.name})) {\n        this.$!{tool.firstLowerCase($!{tableInfo.name})}Dao.insert($!tool.firstLowerCase($!{tableInfo.name}));\n        return $!tool.firstLowerCase($!{tableInfo.name});\n    }\n\n    /**\n     * 修改数据\n     *\n     * @param $!tool.firstLowerCase($!{tableInfo.name}) 实例对象\n     * @return 实例对象\n     */\n    @Override\n    public $!{tableInfo.name} update($!{tableInfo.name} $!tool.firstLowerCase($!{tableInfo.name})) {\n        this.$!{tool.firstLowerCase($!{tableInfo.name})}Dao.update($!tool.firstLowerCase($!{tableInfo.name}));\n        return this.queryById($!{tool.firstLowerCase($!{tableInfo.name})}.get$!tool.firstUpperCase($pk.name)());\n    }\n\n    /**\n     * 通过主键删除数据\n     *\n     * @param $!pk.name 主键\n     * @return 是否成功\n     */\n    @Override\n    public boolean deleteById($!pk.shortType $!pk.name) {\n        return this.$!{tool.firstLowerCase($!{tableInfo.name})}Dao.deleteById($!pk.name) > 0;\n    }\n}"}]}}, "columnConfig": {"baowu": {"name": "baowu", "elementList": [{"title": "disable", "type": "BOOLEAN", "selectValue": ""}, {"title": "support", "type": "SELECT", "selectValue": "add,edit,query,del,ui"}]}, "Default": {"name": "<PERSON><PERSON><PERSON>", "elementList": [{"title": "disable", "type": "BOOLEAN", "selectValue": ""}, {"title": "support", "type": "SELECT", "selectValue": "add,edit,query,del,ui"}]}}, "globalConfig": {"baowu": {"name": "baowu", "elementList": [{"name": "autoImport.vm", "value": "##自动导入包（仅导入实体属性需要的包，通常用于实体类）\n#foreach($import in $importList)\nimport $!import;\n#end"}, {"name": "baowu.vm", "value": "##------------------------define.vm----------------------\n##（Velocity宏定义）\n\n##定义设置表名后缀的宏定义，调用方式：#setTableSuffix(\"Test\")\n#macro(setTableSuffix $suffix)\n    #set($tableName = $!tool.append($tableInfo.name, $suffix))\n#end\n\n##定义设置包名后缀的宏定义，调用方式：#setPackageSuffix(\"Test\")\n#macro(setPackageSuffix $suffix)\n#if($suffix!=\"\")package #end#if($tableInfo.savePackageName!=\"\")$!{tableInfo.savePackageName}.#{end}$!suffix;\n#end\n\n##定义直接保存路径与文件名简化的宏定义，调用方式：#save(\"/entity\", \".java\")\n#macro(save $path $fileName)\n    $!callback.setSavePath($tool.append($tableInfo.savePath, $path))\n    $!callback.setFileName($tool.append($tableInfo.name, $fileName))\n#end\n\n##定义表注释的宏定义，调用方式：#tableComment(\"注释信息\")\n#macro(tableComment $desc)\n/**\n * $!{tableInfo.comment}($!{tableInfo.name})$desc\n *\n * <AUTHOR>\n * @since $!time.currTime()\n */\n#end\n\n##定义GET，SET方法的宏定义，调用方式：#getSetMethod($column)\n#macro(getSetMethod $column)\n\n    public $!{tool.getClsNameByFullName($column.type)} get$!{tool.firstUpperCase($column.name)}() {\n        return $!{column.name};\n    }\n\n    public void set$!{tool.firstUpperCase($column.name)}($!{tool.getClsNameByFullName($column.type)} $!{column.name}) {\n        this.$!{column.name} = $!{column.name};\n    }\n#end\n\n##------------------------mybatis.vm----------------------\n##针对Mybatis 进行支持，主要用于生成xml文件\n#foreach($column in $tableInfo.fullColumn)\n    ##储存列类型\n    $tool.call($column.ext.put(\"sqlType\", $tool.getField($column.obj.dataType, \"typeName\")))\n    #if($tool.newHashSet(\"java.lang.String\").contains($column.type))\n        #set($jdbcType=\"VARCHAR\")\n    #elseif($tool.newHashSet(\"java.lang.Boolean\", \"boolean\").contains($column.type))\n        #set($jdbcType=\"BOOLEAN\")\n    #elseif($tool.newHashSet(\"java.lang.Byte\", \"byte\").contains($column.type))\n        #set($jdbcType=\"BYTE\")\n    #elseif($tool.newHashSet(\"java.lang.Integer\", \"int\", \"java.lang.Short\", \"short\").contains($column.type))\n        #set($jdbcType=\"INTEGER\")\n    #elseif($tool.newHashSet(\"java.lang.Long\", \"long\").contains($column.type))\n        #set($jdbcType=\"INTEGER\")\n    #elseif($tool.newHashSet(\"java.lang.Float\", \"float\", \"java.lang.Double\", \"double\").contains($column.type))\n        #set($jdbcType=\"NUMERIC\")\n    #elseif($tool.newHashSet(\"java.util.Date\", \"java.sql.Timestamp\", \"java.time.Instant\", \"java.time.LocalDateTime\", \"java.time.OffsetDateTime\", \"\tjava.time.ZonedDateTime\").contains($column.type))\n        #set($jdbcType=\"TIMESTAMP\")\n    #elseif($tool.newHashSet(\"java.sql.Date\", \"java.time.LocalDate\").contains($column.type))\n        #set($jdbcType=\"TIMESTAMP\")\n    #else\n        ##其他类型\n        #set($jdbcType=\"VARCHAR\")\n    #end\n    $tool.call($column.ext.put(\"jdbcType\", $jdbcType))\n#end\n\n##定义宏，查询所有列\n#macro(allSqlColumn)#foreach($column in $tableInfo.fullColumn)$column.obj.name#if($foreach.hasNext), #end#end#end\n\n##------------------自定义-------------------\n\n##entity类名\n#set($entityName = $tableInfo.name.toUpperCase())\n##entity 实例名\n#set($entityInstanceName = $tableInfo.name.toLowerCase())\n\n#if(!$tableInfo.pkColumn.isEmpty())\n    ##表主键数据\n    #set($pk = $tableInfo.pkColumn.get(0))\n    \n    ##主键第一个字母大写\n    #set($pkFirstUpperCase =$tool.firstUpperCase($pk.obj.name.toLowerCase()))\n#end\n\n#set($baowuViewDir =$projectPath+\"/src/main/resources/META-INF/resources/\"+$entityName.substring(0,2)+\"/\"+$entityName.substring(2,4)+\"/\")\n#set($baowuJavaDir =$projectPath+\"/src/main/java/com/baosight/imc/\"+$entityName.substring(0,2).toLowerCase()+\"/\"+$entityName.substring(2,4).toLowerCase()+\"/\")\n#set($baowuTestDir =$projectPath+\"/src/test/java/com/baosight/imc/\"+$entityName.substring(0,2).toLowerCase()+\"/\"+$entityName.substring(2,4).toLowerCase()+\"/\")\n\n#if($tableInfo.savePath==\"\")\n    $!callback.setSavePath($baowuJavaDir)\n#end"}]}, "Default": {"name": "<PERSON><PERSON><PERSON>", "elementList": [{"name": "autoImport.vm", "value": "##自动导入包（仅导入实体属性需要的包，通常用于实体类）\n#foreach($import in $importList)\nimport $!import;\n#end"}, {"name": "define.vm", "value": "##（Velocity宏定义）\n\n##定义设置表名后缀的宏定义，调用方式：#setTableSuffix(\"Test\")\n#macro(setTableSuffix $suffix)\n    #set($tableName = $!tool.append($tableInfo.name, $suffix))\n#end\n\n##定义设置包名后缀的宏定义，调用方式：#setPackageSuffix(\"Test\")\n#macro(setPackageSuffix $suffix)\n#if($suffix!=\"\")package #end#if($tableInfo.savePackageName!=\"\")$!{tableInfo.savePackageName}.#{end}$!suffix;\n#end\n\n##定义直接保存路径与文件名简化的宏定义，调用方式：#save(\"/entity\", \".java\")\n#macro(save $path $fileName)\n    $!callback.setSavePath($tool.append($tableInfo.savePath, $path))\n    $!callback.setFileName($tool.append($tableInfo.name, $fileName))\n#end\n\n##定义表注释的宏定义，调用方式：#tableComment(\"注释信息\")\n#macro(tableComment $desc)\n/**\n * $!{tableInfo.comment}($!{tableInfo.name})$desc\n *\n * <AUTHOR>\n * @since $!time.currTime()\n */\n#end\n\n##定义GET，SET方法的宏定义，调用方式：#getSetMethod($column)\n#macro(getSetMethod $column)\n\n    public $!{tool.getClsNameByFullName($column.type)} get$!{tool.firstUpperCase($column.name)}() {\n        return $!{column.name};\n    }\n\n    public void set$!{tool.firstUpperCase($column.name)}($!{tool.getClsNameByFullName($column.type)} $!{column.name}) {\n        this.$!{column.name} = $!{column.name};\n    }\n#end"}, {"name": "init.vm", "value": "##初始化区域\n\n##去掉表的t_前缀\n$!tableInfo.setName($tool.getClassName($tableInfo.obj.name.replaceFirst(\"book_\",\"\")))\n\n##参考阿里巴巴开发手册，POJO 类中布尔类型的变量，都不要加 is 前缀，否则部分框架解析会引起序列化错误\n#foreach($column in $tableInfo.fullColumn)\n#if($column.name.startsWith(\"is\") && $column.type.equals(\"java.lang.Boolean\"))\n    $!column.setName($tool.firstLowerCase($column.name.substring(2)))\n#end\n#end\n\n##实现动态排除列\n#set($temp = $tool.newHashSet(\"testCreateTime\", \"otherColumn\"))\n#foreach($item in $temp)\n    #set($newList = $tool.newArrayList())\n    #foreach($column in $tableInfo.fullColumn)\n        #if($column.name!=$item)\n            ##带有反回值的方法调用时使用$tool.call来消除返回值\n            $tool.call($newList.add($column))\n        #end\n    #end\n    ##重新保存\n    $tableInfo.setFullColumn($newList)\n#end\n\n##对importList进行篡改\n#set($temp = $tool.newHashSet())\n#foreach($column in $tableInfo.fullColumn)\n    #if(!$column.type.startsWith(\"java.lang.\"))\n        ##带有反回值的方法调用时使用$tool.call来消除返回值\n        $tool.call($temp.add($column.type))\n    #end\n#end\n##覆盖\n#set($importList = $temp)"}, {"name": "mybatisSupport.vm", "value": "##针对Mybatis 进行支持，主要用于生成xml文件\n#foreach($column in $tableInfo.fullColumn)\n    ##储存列类型\n    $tool.call($column.ext.put(\"sqlType\", $tool.getField($column.obj.dataType, \"typeName\")))\n    #if($tool.newHashSet(\"java.lang.String\").contains($column.type))\n        #set($jdbcType=\"VARCHAR\")\n    #elseif($tool.newHashSet(\"java.lang.Boolean\", \"boolean\").contains($column.type))\n        #set($jdbcType=\"BOOLEAN\")\n    #elseif($tool.newHashSet(\"java.lang.Byte\", \"byte\").contains($column.type))\n        #set($jdbcType=\"BYTE\")\n    #elseif($tool.newHashSet(\"java.lang.Integer\", \"int\", \"java.lang.Short\", \"short\").contains($column.type))\n        #set($jdbcType=\"INTEGER\")\n    #elseif($tool.newHashSet(\"java.lang.Long\", \"long\").contains($column.type))\n        #set($jdbcType=\"INTEGER\")\n    #elseif($tool.newHashSet(\"java.lang.Float\", \"float\", \"java.lang.Double\", \"double\").contains($column.type))\n        #set($jdbcType=\"NUMERIC\")\n    #elseif($tool.newHashSet(\"java.util.Date\", \"java.sql.Timestamp\", \"java.time.Instant\", \"java.time.LocalDateTime\", \"java.time.OffsetDateTime\", \"\tjava.time.ZonedDateTime\").contains($column.type))\n        #set($jdbcType=\"TIMESTAMP\")\n    #elseif($tool.newHashSet(\"java.sql.Date\", \"java.time.LocalDate\").contains($column.type))\n        #set($jdbcType=\"TIMESTAMP\")\n    #else\n        ##其他类型\n        #set($jdbcType=\"VARCHAR\")\n    #end\n    $tool.call($column.ext.put(\"jdbcType\", $jdbcType))\n#end\n\n##定义宏，查询所有列\n#macro(allSqlColumn)#foreach($column in $tableInfo.fullColumn)$column.obj.name#if($foreach.hasNext), #end#end#end\n"}]}}}
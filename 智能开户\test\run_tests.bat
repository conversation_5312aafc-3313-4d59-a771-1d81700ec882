@echo off
echo Flame API 测试工具
echo ==================

echo.
echo 选择测试方式:
echo 1. 运行简化版测试 (test_simple.py)
echo 2. 运行完整单元测试 (test2.py)
echo 3. 运行所有测试
echo.

set /p choice=请输入选择 (1-3): 

if "%choice%"=="1" (
    echo 运行简化版测试...
    python test_simple.py
) else if "%choice%"=="2" (
    echo 运行完整单元测试...
    python -m unittest test2.py -v
) else if "%choice%"=="3" (
    echo 运行所有测试...
    echo.
    echo === 1. 简化版测试 ===
    python test_simple.py
    echo.
    echo === 2. 完整单元测试 ===
    python -m unittest test2.py -v
) else (
    echo 无效选择，运行简化版测试...
    python test_simple.py
)

echo.
echo 测试完成！
pause

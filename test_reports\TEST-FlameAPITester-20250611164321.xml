<?xml version="1.0" ?>
<testsuite name="FlameAPITester-20250611164321" tests="1" time="2.002" failures="0" errors="0">
	<testcase classname="FlameAPITester" name="test_planning_agent" time="2.002"/>
	<system-out><![CDATA[signed_str=host: **********
date: Wed, 11 Jun 2025 08:43:21 GMT
GET /openapi/flames/api/v2/chat HTTP/1.1
发送消息: {"header": {"traceId": "TRACE-1749631401-95cb395e", "bodyId": "xzrbgent6ue6b0btaorwxi5phhbyld3d", "appId": "2741701DAF894C02BA78", "mode": 0}, "parameter": {}, "payload": {"sessionId": "SESSION_001", "text": [{"content": "开户申请", "content_type": "text", "role": "user"}]}}
收到原始消息: {"header":{"code":-1,"message":"Data Not Found","status":2,"sid":"TALKHGWUVZXA81EBE","traceId":"TRACE-1749631401-95cb395e"},"payload":{}}

测试结果:
- 总响应数: 1
- 是否有错误: False
- 是否有有效响应: True
- 测试用时: 0.00秒
- 所有响应: [{'header': {'code': -1, 'message': 'Data Not Found', 'status': 2, 'sid': 'TALKHGWUVZXA81EBE', 'traceId': 'TRACE-1749631401-95cb395e'}, 'payload': {}}]
]]></system-out>
	<system-err><![CDATA[]]></system-err>
</testsuite>

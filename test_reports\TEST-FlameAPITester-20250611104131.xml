<?xml version="1.0" ?>
<testsuite name="FlameAPITester-20250611104131" tests="1" time="1.004" failures="0" errors="1">
	<testcase classname="FlameAPITester" name="test_planning_agent" time="1.004">
		<error type="AssertionError" message="False is not true"><![CDATA[Traceback (most recent call last):
  File "c:\vs_project\test\智能开户\test\test2.py", line 137, in test_planning_agent
    self.assertTrue(valid_response)
AssertionError: False is not true
]]></error>
	</testcase>
	<system-out><![CDATA[Error: sequence item 7: expected str instance, int found
### 连接已关闭 ###
Received response: ('ERROR', 'sequence item 7: expected str instance, int found')
====================Error==================
]]></system-out>
	<system-err><![CDATA[]]></system-err>
</testsuite>

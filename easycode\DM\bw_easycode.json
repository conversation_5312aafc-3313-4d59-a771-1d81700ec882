{"author": "<PERSON><PERSON><PERSON>feng", "version": "1.2.9", "userSecure": "", "currTypeMapperGroupName": "baowu", "currTemplateGroupName": "baowu", "currColumnConfigGroupName": "baowu", "currGlobalConfigGroupName": "baowu", "typeMapper": {"baowu": {"name": "baowu", "elementList": [{"matchType": "REGEX", "columnType": "varchar(\\(\\d+\\))?", "javaType": "java.lang.String"}, {"matchType": "REGEX", "columnType": "char(\\(\\d+\\))?", "javaType": "java.lang.String"}, {"matchType": "REGEX", "columnType": "(tiny|medium|long)*text", "javaType": "java.lang.String"}, {"matchType": "REGEX", "columnType": "decimal(\\(\\d+,\\d+\\))?", "javaType": "java.math.BigDecimal"}, {"matchType": "ORDINARY", "columnType": "integer", "javaType": "java.lang.Integer"}, {"matchType": "REGEX", "columnType": "(tiny|small|medium)*int(\\(\\d+\\))?", "javaType": "java.lang.Integer"}, {"matchType": "ORDINARY", "columnType": "int4", "javaType": "java.lang.Integer"}, {"matchType": "ORDINARY", "columnType": "int8", "javaType": "java.lang.Long"}, {"matchType": "REGEX", "columnType": "bigint(\\(\\d+\\))?", "javaType": "java.lang.Long"}, {"matchType": "ORDINARY", "columnType": "date", "javaType": "java.lang.String"}, {"matchType": "ORDINARY", "columnType": "datetime", "javaType": "java.lang.String"}, {"matchType": "ORDINARY", "columnType": "timestamp", "javaType": "java.lang.String"}, {"matchType": "ORDINARY", "columnType": "time", "javaType": "java.lang.String"}, {"matchType": "ORDINARY", "columnType": "boolean", "javaType": "java.lang.String"}, {"matchType": "ORDINARY", "columnType": "bigint unsigned", "javaType": "java.lang.Long"}, {"matchType": "ORDINARY", "columnType": "NUMBER(19)", "javaType": "java.lang.Integer"}, {"matchType": "ORDINARY", "columnType": "NVARCHAR2(256)", "javaType": "java.lang.String"}]}}, "template": {"baowu": {"name": "baowu", "elementList": [{"name": "entity.java.vm", "code": "## 引入宏定义\n$!{baowu.vm}\n\n## 使用宏定义设置回调（保存位置与文件）\n$!callback.setSavePath($tableInfo.savePath+\"/domain\")\n$!callback.setFileName($entityName+\".java\")\n\n## 使用宏定义设置包后缀,打印包路径\n#setPackageSuffix(\"domain\")\n\n## 使用全局变量实现默认包导入\n$!{autoImport.vm}\nimport com.baosight.iplat4j.core.data.DaoEPBase;\nimport com.baosight.iplat4j.core.ei.EiColumn;\nimport com.baosight.iplat4j.core.util.NumberUtils;\nimport com.baosight.iplat4j.core.util.StringUtils;\nimport java.util.HashMap;\nimport java.util.Map;\n\n## 使用宏定义实现类注释信息\n/**\n * Title: ${entityName}.java <br>\n * Description: ${tableInfo.name} ${tableInfo.comment} <br>\n *\n * Copyright: Baosight Software LTD.co Copyright (c) 2019. <br>\n *\n * @version 1.0\n * @CreateTime ${time.currTime('yyyy-MM-dd HH:mm:ss')} \n * <AUTHOR>\n */\npublic class ${entityName} extends DaoEPBase {\n    private static final long serialVersionUID = 1L;\n\n#foreach($column in $tableInfo.fullColumn)\n    public static final String FIELD_${tool.hump2Underline($column.name).toUpperCase()} = \"$!{column.name}\";    \t\t/* $!{column.comment} */\n#end\n\n#foreach($column in $tableInfo.fullColumn)\n    public static final String COL_${tool.hump2Underline($column.name).toUpperCase()} = \"$!{tool.hump2Underline($column.name).toUpperCase()}\";    \t\t/* $!{column.comment} */\n#end\n\n    public static final String QUERY = \"${entityName}.query\";\n    public static final String COUNT = \"${entityName}.count\";\n    public static final String INSERT = \"${entityName}.insert\";\n    public static final String UPDATE = \"${entityName}.update\";\n    public static final String DELETE = \"${entityName}.delete\";\n\n#foreach($column in $tableInfo.fullColumn)\n    private ${column.shortType} $!{column.name} =#if($column.shortType==\"String\") \"\"#else new ${column.shortType}(0)#end;\t\t/* $!{column.comment} */\n#end\n\n    /**\n     * initialize the metadata.\n     */\n    public void initMetaData() {\n        EiColumn eiColumn;\n\n#foreach($column in $tableInfo.fullColumn)\n        eiColumn = new EiColumn(FIELD_${tool.hump2Underline($column.name).toUpperCase()});\n        eiColumn.setFieldLength(${column.obj.getDataType().getLength()});\n        eiColumn.setDescName(\"$!{column.comment}\");\n        eiMetadata.addMeta(eiColumn);\n#end\n    }\n\n    /**\n     * the constructor.\n     */\n    public ${entityName}() {\n        initMetaData();\n    }\n\n#foreach($column in $tableInfo.fullColumn)\n    /**\n     * get the $!{column.name} - $!{column.comment}.\n     * @return the $!{column.name}\n     */\n    public ${column.shortType} get${tool.firstUpperCase($column.name)}() {\n        return this.$!{column.name};\n    }\n\n    /**\n     * set the $!{column.name} - $!{column.comment}.\n     *\n     * @param $!{column.name} - $!{column.comment}\n     */\n    public void set${tool.firstUpperCase($column.name)}(${column.shortType} $!{column.name}) {\n        this.$!{column.name} = $!{column.name};\n    }\n#end\n\n    /**\n     * get the value from Map.\n     *\n     * @param map - source data map\n     */\n    @Override\n    public void fromMap(Map map) {\n#foreach($column in $tableInfo.fullColumn)\n        #if($column.shortType==\"String\")\n            set${tool.firstUpperCase($column.name)}(StringUtils.defaultIfEmpty(StringUtils.toString(map.get(FIELD_${tool.hump2Underline($column.name).toUpperCase()})), $!{column.name}));\n        #else\n            set${tool.firstUpperCase($column.name)}(NumberUtils.to${column.shortType}(StringUtils.toString(map.get(FIELD_${tool.hump2Underline($column.name).toUpperCase()})), $!{column.name}));\n        #end\n#end\n    }\n\n    /**\n     * set the value to Map.\n     */\n    @Override\n    public Map toMap() {\n        Map map = new HashMap();\n#foreach($column in $tableInfo.fullColumn)\n        map.put(FIELD_${tool.hump2Underline($column.name).toUpperCase()}, StringUtils.toString($!{column.name}, eiMetadata.getMeta(FIELD_${tool.hump2Underline($column.name).toUpperCase()})));\n#end\n        return map;\n    }\n}"}, {"name": "mapper.xml.vm", "code": "## 引入宏定义\n$!{baowu.vm}\n\n## 使用宏定义设置回调（保存位置与文件）\n$!callback.setSavePath($tableInfo.savePath+\"/sql\")\n$!callback.setFileName($entityName+\".xml\")\n\n<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE sqlMap  PUBLIC \"-//ibatis.apache.org//DTD SQL Map 2.0//EN\" \"http://ibatis.apache.org/dtd/sql-map-2.dtd\">\n<!--      table information\n\t\tGenerate time : ${time.currTime('yyyy-MM-dd HH:mm:ss')}\n   \t\tVersion :  1.0\n\t\ttable comment : ${tableInfo.comment}\n\t\tschema : ${dasUtil.getSchema($tableInfo.obj)}\n\t\ttableName : ${tableInfo.obj.getName()}\n#foreach($column in $tableInfo.fullColumn)\n\t\t$!column.obj.name  ${column.obj.getDataType()}  default ${column.obj.getDefault()} #if($column.obj.isNotNull()) NOT #end NULL, \n#end\n\t-->\n<sqlMap namespace=\"${entityName}\">\n\n\t<sql id=\"condition\">\n#foreach($column in $tableInfo.fullColumn)\n\t\t<isNotEmpty prepend=\" AND \" property=\"${column.name}\">\n\t\t\t$!column.obj.name = #${column.name}#\n\t\t</isNotEmpty>\n#end\n\t</sql>\n\n\t<select id=\"duplication\" parameterClass=\"java.util.HashMap\"\n\t\t\tresultClass=\"${tableInfo.savePackageName}.${entityName}\">\n\t\tSELECT\n#foreach($column in $tableInfo.fullColumn)\n\t\t\t$!column.obj.name\tas \"${column.name}\",  <!-- ${column.comment} -->\n#end\n\t\tFROM ${dasUtil.getSchema($tableInfo.obj)}.${tableInfo.obj.getName()} WHERE 1=1 \n#foreach($column in $tableInfo.fullColumn)\n\t\t\tAND $!column.obj.name = #${column.name}#\n#end\n\n\t\t<isNotEmpty prepend=\" AND \" property=\"notContain$!pkFirstUpperCase\">\n\t\t\t$!pk.obj.name  != #notContain$!pkFirstUpperCase#\n\t\t</isNotEmpty>\n\n\t\t<dynamic prepend=\"ORDER BY\">\n\t\t\t<isNotEmpty property=\"orderBy\">\n    \t\t  $orderBy$\n\t\t\t</isNotEmpty>\n\t\t</dynamic>\n\n\t</select>\n\n\t<select id=\"query\" parameterClass=\"java.util.HashMap\"\n\t\t\tresultClass=\"${tableInfo.savePackageName}.${tableInfo.name.toUpperCase()}\">\n\t\tSELECT\n#foreach($column in $tableInfo.fullColumn)\n\t\t$!column.obj.name\tas \"${column.name}\",  <!-- ${column.comment} -->\n#end\n\t\tFROM ${dasUtil.getSchema($tableInfo.obj)}.${tableInfo.obj.getName()} WHERE 1=1\n\t\t<include refid=\"condition\" />\n\t\t<dynamic prepend=\"ORDER BY\">\n\t\t\t<isNotEmpty property=\"orderBy\">\n\t\t\t\t$orderBy$\n\t\t\t</isNotEmpty>\n\t\t</dynamic>\n\n\t</select>\n\n\t<select id=\"count\" resultClass=\"int\">\n\t\tSELECT COUNT(*) FROM ${dasUtil.getSchema($tableInfo.obj)}.${tableInfo.obj.getName()} WHERE 1=1\n    <include refid=\"condition\" />\n\t</select>\n\n\t<insert id=\"insert\">\n\t\tINSERT INTO ${dasUtil.getSchema($tableInfo.obj)}.${tableInfo.obj.getName()} (\n#foreach($column in $tableInfo.fullColumn)\n\t\t\t$!column.obj.name #if($foreach.hasNext),#end <!-- ${column.comment} -->\n#end\n\t\t\t)\n\t    VALUES (\n#foreach($column in $tableInfo.fullColumn)\n\t\t\t#${column.name}:$!column.ext.jdbcType# #if($foreach.hasNext),#end\n#end\n\t\t\t)\n\t</insert>\n\n\t<delete id=\"delete\">\n\t\tDELETE FROM ${dasUtil.getSchema($tableInfo.obj)}.${tableInfo.obj.getName()} WHERE $!pk.obj.name = #$!pk.name#\n\t</delete>\n\n\t<update id=\"update\">\n\t\tUPDATE ${dasUtil.getSchema($tableInfo.obj)}.${tableInfo.obj.getName()}\n\t\tSET\n#foreach($column in $tableInfo.otherColumn)\n\t\t\t$!column.obj.name\t= #${column.name}# #if($foreach.hasNext),#end   <!-- ${column.comment} -->\n#end\n\t\tWHERE\n            $!pk.obj.name = #$!pk.name#\n            \n\t</update>\n\n</sqlMap>\n"}, {"name": "entity.js.vm", "code": "## 引入宏定义\n$!{baowu.vm}\n\n## 使用宏定义设置回调（保存位置与文件）\n$!callback.setSavePath($baowuViewDir)\n$!callback.setFileName($entityName+\".js\")\n\n$(function () {\n\n    $('#QUERY').on('click', function (e) {\n        resultGrid.dataSource.page(1);\n    });\n    //当前修改行\n    var editRow =0;\n\n    IPLATUI.EFGrid = {\n        \"result\": {\n            pageable: {\n                pageSize: 10,\n                pageSizes: [10, 20, 50, 100, 500]\n            },\n            // 列是否可以拖动切换位置\n            reorderable: true,\n            columns: [],\n            beforeEdit: function (e) {\n                editRow = e.row;\n                // 获取当前修改行\n            #foreach($column in $tableInfo.fullColumn)\n                // if(e.field === \"${column.name}\") { ${foreach.index};}\n            #end\n            },\n            afterEdit: function (e) {\n                // 执行自定义逻辑代码，假设根据逻辑要求不关闭单元格编辑状态\n            #foreach($column in $tableInfo.fullColumn)\n                //if (e.model[\"${column.name}\"] === \"\") {\n                //    e.model.set(\"${column.name}\", \"\");\n                //}\n            #end\n            },\n            /**\n             * 双击数据行时触发的事件，注意编辑状态时不会触发\n             * @param e\n             * e.sender     kendoGrid对象，resultGrid\n             * e.model      双击的行数据，kendo.data.Model\n             * e.row        当前行的行号\n             * e.tr         行的tr,包括固定列和数据列 jquery对象\n             */\n            onRowDblClick: function (e) {\n                let dataItems = resultGrid.getDataItems();\n                var model = dataItems[e.row];\n            #foreach($column in $tableInfo.fullColumn)\n                //model.set(\"${column.name}\", \"\");\n            #end\n\n                //var popupGridWindow = $(\"#querynum\").data(\"kendoWindow\");\n                //popupGridWindow.close();\n            }\n        },\n    }\n});"}, {"name": "entity.jsp.vm", "code": "## 引入宏定义\n$!{baowu.vm}\n\n## 使用宏定义设置回调（保存位置与文件）\n$!callback.setSavePath($baowuViewDir)\n$!callback.setFileName($entityName+\".jsp\")\n\n<!DOCTYPE html>\n<%@ page contentType=\"text/html; charset=UTF-8\" %>\n<%@ taglib uri=\"http://java.sun.com/jsp/jstl/core\" prefix=\"c\" %>\n<%@ taglib prefix=\"EF\" tagdir=\"/WEB-INF/tags/EF\" %>\n<%@ page import=\"com.baosight.iplat4j.core.ei.EiInfo\" %>\n<%@ page import=\"com.baosight.iplat4j.core.resource.I18nMessages\" %>\n\n<c:set var=\"ctx\" value=\"${pageContext.request.contextPath}\"/>\n<EF:EFPage prefix=\"imc\">\n    <EF:EFRegion id=\"inqu\" title=\"查询条件\">\n        <div class=\"row\">\n#foreach($column in $tableInfo.otherColumn)\n            <EF:EFInput ename=\"inqu_status-0-${column.name}\" cname=\"${column.comment}\" colWidth=\"3\" ratio=\"4:8\"/>\n#end\n        </div>\n    </EF:EFRegion>\n\n    <EF:EFRegion id=\"result\" title=\"记录集\">\n        <EF:EFGrid blockId=\"result\" isFloat=\"true\" autoDraw=\"no\" toolbarConfig=\"true\" needAuth=\"true\">\n            <EF:EFColumn ename=\"${pk.obj.name.toLowerCase()}\" cname=\"\" hidden=\"true\"/>\n#foreach($column in $tableInfo.otherColumn)\n            <EF:EFColumn ename=\"${column.name}\" cname=\"${column.comment}\" sort=\"true\" required=\"true\" align=\"center\"/>\n#end\n        </EF:EFGrid>\n    </EF:EFRegion>\n</EF:EFPage>"}, {"name": "service.java.vm", "code": "## 引入宏定义\n$!{baowu.vm}\n\n## 使用宏定义设置回调（保存位置与文件）\n$!callback.setSavePath($tableInfo.savePath+\"/service\")\n$!callback.setFileName(\"Service\"+$entityName+\".java\")\n\n## 使用宏定义设置包后缀,打印包路径\n#setPackageSuffix(\"service\")\n\n## 使用全局变量实现默认包导入\n$!{autoImport.vm}\nimport com.baosight.elim.common.utils.GlobalUtils;\nimport com.baosight.imc.common.utils.BeanUtil;\nimport com.baosight.imc.common.utils.FormUtils;\nimport com.baosight.imc.interfaces.vz.bm.domain.VZBM1300;\nimport com.baosight.imc.interfaces.vz.bm.service.ServiceVZBM1300;\nimport com.baosight.imc.xb.ai.domain.XBAI13;\nimport com.baosight.iplat4j.core.ei.EiConstant;\nimport com.baosight.iplat4j.core.ei.EiInfo;\nimport com.baosight.iplat4j.core.service.impl.ServiceEPBase;\nimport org.apache.commons.lang.StringUtils;\nimport org.jetbrains.annotations.Nullable;\nimport org.slf4j.Logger;\nimport org.slf4j.LoggerFactory;\n\nimport java.io.UnsupportedEncodingException;\nimport java.math.BigDecimal;\nimport java.util.ArrayList;\nimport java.util.HashMap;\nimport java.util.List;\nimport java.util.Objects;\n\n/**\n * ${tableInfo.comment} 服务类\n * @DateTime ${time.currTime('yyyy-MM-dd HH:mm:ss')} \n * <AUTHOR>\n */\npublic class Service${entityName} extends ServiceEPBase {\n\n    private static final Logger logger = LoggerFactory.getLogger(Service${entityName}.class);\n\n    @Override\n    public EiInfo initLoad(EiInfo inInfo) {\n        return super.initLoad(inInfo, new ${entityName}());\n    }\n\n    /**\n     * 查询功能\n     *\n     * @param inInfo\n     * @return\n     */\n    @Override\n    public EiInfo query(EiInfo inInfo) {\n        EiInfo outInfo = super.query(inInfo, \"${entityName}.query\", new ${entityName}());\n        return outInfo;\n    }\n\n    /**\n     * 新增功能\n     *\n     * @param inInfo\n     * @return\n     */\n    @Override\n    public EiInfo insert(EiInfo inInfo) {\n\n        EiInfo outInfo = new EiInfo();\n        List rows = new ArrayList();\n        for (int i = 0; i < inInfo.getBlock(EiConstant.resultBlock).getRowCount(); i++) {\n            ${entityName} ${entityInstanceName} = new ${entityName}();\n            ${entityInstanceName}.fromMap(inInfo.getRow(EiConstant.resultBlock, i));\n            BeanUtil.beanAttributeValueTrim(${entityInstanceName});\n            FormUtils.setCreatorProertyFromXservice(${entityInstanceName});\n            boolean isCheckPass = check${entityName}(${entityInstanceName}, outInfo);\n            if (!isCheckPass) {\n                if (StringUtils.isNotBlank(outInfo.getMsg())) {\n                    outInfo.setMsg(errorString(\"VZBM0810001\", \"第\" + (i + 1) + \"行\" + outInfo.getMsg()));\n                }\n                return outInfo;\n            }\n            int countDuplication = dao.count(\"${entityName}.duplication\", new HashMap<String, Object>() {{\n                #foreach($column in $tableInfo.fullColumn)\n                        put(\"${column.name}\", ${entityInstanceName}.get${tool.firstUpperCase($column.name)}());\n                #end\n            }});\n            if (countDuplication > 0) {\n                outInfo.setStatus(EiConstant.STATUS_FAILURE);\n                outInfo.setMsg(errorString(\"VZBM0810001\", \"第\" + (i + 1) + \"行记录重复！\"));\n                return outInfo;\n            }\n            rows.add(${entityInstanceName}.toMap());\n        }\n        inInfo.getBlock(EiConstant.resultBlock).setRows(rows);\n\n        return insert(inInfo, \"${entityName}.insert\");\n    }\n\n    @Nullable\n    private boolean check${entityName}(${entityName} ${entityInstanceName}, EiInfo outInfo) {\n        try {\n            #foreach($column in $tableInfo.otherColumn)\n                #if($column.obj.isNotNull())\n                    ${column.shortType} ${column.name} = ${entityInstanceName}.get${tool.firstUpperCase($column.name)}();\n                    #if($column.shortType==\"String\")\n                    if (StringUtils.isEmpty(${column.name})) {\n                    #else\n                    if (Objects.isNull(${column.name})) {\n                    #end\n                        outInfo.setStatus(EiConstant.STATUS_FAILURE);\n                        outInfo.setMsg(\"${column.comment}不能为空！\");\n                        return false;\n                    }\n                #end\n                    #if($column.shortType==\"String\")\n                    if (${column.name}.getBytes(\"gbk\").length > ${column.obj.getDataType().getLength()}) {\n                        outInfo.setStatus(EiConstant.STATUS_FAILURE);\n                        outInfo.setMsg(\"${column.comment}最大为${column.obj.getDataType().getLength()}个文字！\");\n                        return false;\n                    }\n                    #end\n            #end\n\n        } catch (UnsupportedEncodingException e) {\n            e.printStackTrace();\n            throw new RuntimeException(e);\n        }\n        return true;\n    }\n\n    /**\n     * @param code\n     * @param msg\n     * @return\n     */\n    private String errorString(String code, String msg) {\n        VZBM1300 queryMsg = new VZBM1300();\n        queryMsg.setErrorNum(code);\n        queryMsg.setFormEname(\"${entityName}\");\n        GlobalUtils.setCreatorProerty(queryMsg);\n        return ServiceVZBM1300.getMessageTextByErrorNumAndRecord(new String[]{msg}, queryMsg);\n    }\n\n    /**\n     * 修改功能\n     *\n     * @param inInfo\n     * @return\n     */\n    @Override\n    public EiInfo update(EiInfo inInfo) {\n        EiInfo outInfo = new EiInfo();\n\n        for (int i = 0; i < inInfo.getBlock(EiConstant.resultBlock).getRowCount(); i++) {\n            ${entityName} ${entityInstanceName} = new ${entityName}();\n            ${entityInstanceName}.fromMap(inInfo.getRow(EiConstant.resultBlock, i));\n            BeanUtil.beanAttributeValueTrim(${entityInstanceName});\n            FormUtils.setCreatorProertyFromXservice(${entityInstanceName});\n            boolean isCheckPass = check${entityName}(${entityInstanceName}, outInfo);\n            if (!isCheckPass) {\n                if (StringUtils.isNotBlank(outInfo.getMsg())) {\n                    outInfo.setMsg(errorString(\"VZBM0810001\", \"第\" + (i + 1) + \"行\" + outInfo.getMsg()));\n                }\n                return outInfo;\n            }\n            if (StringUtils.isEmpty(${entityInstanceName}.get${pkFirstUpperCase}())) {\n                outInfo.setStatus(EiConstant.STATUS_FAILURE);\n                outInfo.setMsg(\"记录$!pk.obj.name为空！\");\n                return outInfo;\n            }\n            int countDuplication = dao.count(\"${entityName}.duplication\", new HashMap<String, Object>() {{\n                #foreach($column in $tableInfo.fullColumn)\n                put(\"${column.name}\", ${entityInstanceName}.get${tool.firstUpperCase($column.name)}());\n                #end\n                put(\"notContain$!pkFirstUpperCase\", ${entityInstanceName}.get$!pkFirstUpperCase());\n            }});\n            if (countDuplication > 0) {\n                outInfo.setStatus(EiConstant.STATUS_FAILURE);\n                outInfo.setMsg(errorString(\"VZBM0810001\", \"第\" + (i + 1) + \"行记录重复！\"));\n                return outInfo;\n            }\n        }\n\n        return super.update(inInfo, \"${entityName}.update\");\n    }\n\n    /**\n     * 删除功能\n     *\n     * @param inInfo\n     * @return\n     */\n    @Override\n    public EiInfo delete(EiInfo inInfo) {\n        EiInfo outInfo = super.delete(inInfo, \"${entityName}.delete\");\n        return outInfo;\n    }\n\n}"}, {"name": "test.java.vm", "code": "## 引入宏定义\n$!{baowu.vm}\n\n## 使用宏定义设置回调（保存位置与文件）\n$!callback.setSavePath($baowuTestDir+\"/service\")\n$!callback.setFileName(\"Service\"+$entityName+\"Test.java\")\n\n#setPackageSuffix(\"service\")\n$!{autoImport.vm}\nimport com.baosight.BaseTest;\nimport com.baosight.iplat4j.core.ei.EiInfo;\nimport org.junit.Before;\nimport org.junit.Test;\n\n/**\n * ${tableInfo.comment} 服务测试类\n * @DateTime ${time.currTime('yyyy-MM-dd HH:mm:ss')} \n * <AUTHOR>\n */\npublic class Service${entityName}Test extends BaseTest {\n    Service${entityName} service${entityName};\n    @Before\n    public void init() {\n        setSessionUser();\n        service${entityName} =(Service${entityName})getServiceBean(\"${entityName}\");\n    }\n\n    private ${entityName} get${entityName}() {\n        ${entityName} ${entityInstanceName} = new ${entityName}();\n        #foreach($column in $tableInfo.fullColumn)\n        ${entityInstanceName}.set${tool.firstUpperCase($column.name)}(${column.obj.getDefaultValue()});\n        #end\n        return ${entityInstanceName};\n    }\n\n    @Test\n    public void testQuery() {\n        EiInfo info =service${entityName}.query(new EiInfo(){{\n\n        }});\n        System.out.println(info.toJSONString());\n    }\n\n    @Test\n    public void testInsert() {\n                inInfo.getBlock(EiConstant.resultBlock).setRows(rows);\n\n        EiBlock eiBlock=new EiBlock(EiConstant.resultBlock)\n                .setRows(\n                    new ArrayList<${entityName}>(){{\n                        add(get${entityName}());\n                    }}\n                );\n        EiInfo info =service${entityName}.insert(new EiInfo(){{\n            setBlock(eiBlock);\n        }});\n            \n        System.out.println(info.toJSONString());\n    }\n\n    @Test\n    public void testUpdate() {\n        EiBlock eiBlock=new EiBlock(EiConstant.resultBlock)\n                .setRows(\n                    new ArrayList<${entityName}>(){{\n                        add(get${entityName}());\n                    }}\n                );\n        EiInfo info =service${entityName}.update(new EiInfo(){{\n            setBlock(eiBlock);\n        }});\n        System.out.println(info.toJSONString());\n    }\n\n    @Test\n    public void testDelete() {\n        EiBlock eiBlock=new EiBlock(EiConstant.resultBlock)\n                .setRows(\n                    new ArrayList<${entityName}>(){{\n                        add(get${entityName}());\n                    }}\n                );\n        EiInfo info =service${entityName}.delete(new EiInfo(){{\n            setBlock(eiBlock);\n        }});\n        System.out.println(info.toJSONString());\n    }\n\n}"}]}}, "columnConfig": {"baowu": {"name": "baowu", "elementList": [{"title": "disable", "type": "BOOLEAN", "selectValue": ""}, {"title": "support", "type": "SELECT", "selectValue": "add,edit,query,del,ui"}]}}, "globalConfig": {"baowu": {"name": "baowu", "elementList": [{"name": "autoImport.vm", "value": "##自动导入包（仅导入实体属性需要的包，通常用于实体类）\n#foreach($import in $importList)\nimport $!import;\n#end"}, {"name": "baowu.vm", "value": "##------------------------define.vm----------------------\n##（Velocity宏定义）\n\n##定义设置表名后缀的宏定义，调用方式：#setTableSuffix(\"Test\")\n#macro(setTableSuffix $suffix)\n    #set($tableName = $!tool.append($tableInfo.name, $suffix))\n#end\n\n##定义设置包名后缀的宏定义，调用方式：#setPackageSuffix(\"Test\")\n#macro(setPackageSuffix $suffix)\n#if($suffix!=\"\")package #end#if($tableInfo.savePackageName!=\"\")$!{tableInfo.savePackageName}.#{end}$!suffix;\n#end\n\n##定义直接保存路径与文件名简化的宏定义，调用方式：#save(\"/entity\", \".java\")\n#macro(save $path $fileName)\n    $!callback.setSavePath($tool.append($tableInfo.savePath, $path))\n    $!callback.setFileName($tool.append($tableInfo.name, $fileName))\n#end\n\n##定义表注释的宏定义，调用方式：#tableComment(\"注释信息\")\n#macro(tableComment $desc)\n/**\n * $!{tableInfo.comment}($!{tableInfo.name})$desc\n *\n * <AUTHOR>\n * @since $!time.currTime()\n */\n#end\n\n##定义GET，SET方法的宏定义，调用方式：#getSetMethod($column)\n#macro(getSetMethod $column)\n\n    public $!{tool.getClsNameByFullName($column.type)} get$!{tool.firstUpperCase($column.name)}() {\n        return $!{column.name};\n    }\n\n    public void set$!{tool.firstUpperCase($column.name)}($!{tool.getClsNameByFullName($column.type)} $!{column.name}) {\n        this.$!{column.name} = $!{column.name};\n    }\n#end\n\n##------------------------mybatis.vm----------------------\n##针对Mybatis 进行支持，主要用于生成xml文件\n#foreach($column in $tableInfo.fullColumn)\n    ##储存列类型\n    $tool.call($column.ext.put(\"sqlType\", $tool.getField($column.obj.dataType, \"typeName\")))\n    #if($tool.newHashSet(\"java.lang.String\").contains($column.type))\n        #set($jdbcType=\"VARCHAR\")\n    #elseif($tool.newHashSet(\"java.lang.Boolean\", \"boolean\").contains($column.type))\n        #set($jdbcType=\"BOOLEAN\")\n    #elseif($tool.newHashSet(\"java.lang.Byte\", \"byte\").contains($column.type))\n        #set($jdbcType=\"BYTE\")\n    #elseif($tool.newHashSet(\"java.lang.Integer\", \"int\", \"java.lang.Short\", \"short\").contains($column.type))\n        #set($jdbcType=\"INTEGER\")\n    #elseif($tool.newHashSet(\"java.lang.Long\", \"long\").contains($column.type))\n        #set($jdbcType=\"INTEGER\")\n    #elseif($tool.newHashSet(\"java.lang.Float\", \"float\", \"java.lang.Double\", \"double\").contains($column.type))\n        #set($jdbcType=\"NUMERIC\")\n    #elseif($tool.newHashSet(\"java.util.Date\", \"java.sql.Timestamp\", \"java.time.Instant\", \"java.time.LocalDateTime\", \"java.time.OffsetDateTime\", \"\tjava.time.ZonedDateTime\").contains($column.type))\n        #set($jdbcType=\"TIMESTAMP\")\n    #elseif($tool.newHashSet(\"java.sql.Date\", \"java.time.LocalDate\").contains($column.type))\n        #set($jdbcType=\"TIMESTAMP\")\n    #else\n        ##其他类型\n        #set($jdbcType=\"VARCHAR\")\n    #end\n    $tool.call($column.ext.put(\"jdbcType\", $jdbcType))\n#end\n\n##定义宏，查询所有列\n#macro(allSqlColumn)#foreach($column in $tableInfo.fullColumn)$column.obj.name#if($foreach.hasNext), #end#end#end\n\n##------------------自定义-------------------\n\n##entity类名\n#set($entityName = $tableInfo.name.toUpperCase())\n##entity 实例名\n#set($entityInstanceName = $tableInfo.name.toLowerCase())\n\n#if(!$tableInfo.pkColumn.isEmpty())\n    ##表主键数据\n    #set($pk = $tableInfo.pkColumn.get(0))\n    \n    ##主键第一个字母大写\n    #set($pkFirstUpperCase =$tool.firstUpperCase($pk.obj.name.toLowerCase()))\n#end\n\n#set($baowuViewDir =$projectPath+\"/src/main/resources/META-INF/resources/\"+$entityName.substring(0,2)+\"/\"+$entityName.substring(2,4)+\"/\")\n#set($baowuJavaDir =$projectPath+\"/src/main/java/com/baosight/imc/\"+$entityName.substring(0,2).toLowerCase()+\"/\"+$entityName.substring(2,4).toLowerCase()+\"/\")\n#set($baowuTestDir =$projectPath+\"/src/test/java/com/baosight/imc/\"+$entityName.substring(0,2).toLowerCase()+\"/\"+$entityName.substring(2,4).toLowerCase()+\"/\")\n\n#if($tableInfo.savePath==\"\")\n    $!callback.setSavePath($baowuJavaDir)\n#end"}]}}}
{"input_path": "t.pdf", "page_index": 0, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": true, "use_doc_unwarping": true}, "angle": 0}, "dt_polys": [[[217, 179], [573, 177], [573, 207], [218, 209]], [[418, 234], [847, 228], [848, 264], [418, 270]], [[504, 288], [759, 283], [760, 324], [505, 330]], [[154, 355], [245, 358], [244, 386], [153, 384]], [[336, 359], [584, 354], [584, 379], [336, 383]], [[152, 399], [320, 402], [320, 430], [152, 427]], [[328, 398], [402, 398], [402, 428], [328, 428]], [[487, 397], [540, 397], [540, 429], [487, 429]], [[568, 398], [633, 398], [633, 427], [568, 427]], [[653, 397], [742, 395], [743, 423], [654, 425]], [[753, 395], [836, 395], [836, 423], [753, 423]], [[857, 392], [911, 392], [911, 424], [857, 424]], [[151, 425], [205, 428], [203, 459], [149, 455]], [[569, 429], [592, 429], [592, 454], [569, 454]], [[653, 423], [685, 423], [685, 454], [653, 454]], [[753, 425], [815, 425], [815, 450], [753, 450]], [[151, 471], [318, 473], [318, 500], [151, 498]], [[485, 470], [539, 470], [539, 501], [485, 501]], [[568, 470], [632, 470], [632, 499], [568, 499]], [[653, 467], [706, 467], [706, 498], [653, 498]], [[753, 466], [837, 466], [837, 494], [753, 494]], [[855, 463], [912, 463], [912, 497], [855, 497]], [[332, 473], [474, 473], [474, 497], [332, 497]], [[157, 501], [318, 503], [318, 527], [157, 525]], [[332, 502], [473, 502], [473, 526], [332, 526]], [[569, 502], [590, 502], [590, 527], [569, 527]], [[751, 496], [815, 494], [817, 522], [752, 525]], [[149, 527], [196, 527], [196, 557], [149, 557]], [[330, 529], [421, 529], [421, 556], [330, 556]], [[151, 572], [243, 572], [243, 601], [151, 601]], [[330, 572], [474, 574], [474, 598], [330, 596]], [[487, 574], [640, 571], [640, 598], [488, 601]], [[328, 600], [392, 600], [392, 629], [328, 629]], [[487, 604], [639, 601], [639, 626], [488, 629]], [[152, 644], [242, 647], [241, 675], [151, 673]], [[330, 647], [404, 647], [404, 673], [330, 673]], [[487, 645], [576, 645], [576, 673], [487, 673]], [[653, 645], [782, 642], [782, 670], [654, 672]], [[329, 692], [471, 692], [471, 716], [329, 716]], [[486, 690], [576, 690], [576, 718], [486, 718]], [[653, 687], [724, 687], [724, 716], [653, 716]], [[152, 714], [305, 714], [305, 741], [152, 741]], [[327, 719], [396, 719], [396, 747], [327, 747]], [[486, 734], [639, 731], [639, 758], [486, 760]], [[650, 730], [724, 727], [725, 757], [651, 760]], [[152, 740], [181, 740], [181, 768], [152, 768]], [[484, 759], [537, 759], [537, 791], [484, 791]], [[149, 802], [222, 804], [221, 837], [148, 834]], [[327, 805], [400, 805], [400, 835], [327, 835]], [[486, 807], [639, 805], [639, 830], [486, 832]], [[650, 805], [771, 803], [771, 828], [651, 830]], [[149, 850], [418, 845], [419, 875], [149, 879]], [[420, 846], [478, 846], [478, 876], [420, 876]], [[483, 846], [807, 845], [807, 873], [483, 874]], [[810, 845], [863, 842], [864, 871], [811, 874]], [[150, 892], [203, 892], [203, 922], [150, 922]], [[211, 891], [299, 891], [299, 924], [211, 924]], [[304, 894], [423, 894], [423, 919], [304, 919]], [[149, 920], [201, 920], [201, 949], [149, 949]], [[213, 922], [243, 922], [243, 947], [213, 947]], [[301, 919], [335, 919], [335, 950], [301, 950]], [[148, 947], [201, 947], [201, 979], [148, 979]], [[213, 957], [274, 967], [269, 1001], [207, 992]], [[259, 965], [292, 965], [292, 990], [259, 990]], [[304, 965], [424, 965], [424, 990], [304, 990]], [[436, 963], [507, 963], [507, 993], [436, 993]], [[579, 964], [930, 961], [930, 986], [579, 989]], [[212, 993], [243, 993], [243, 1019], [212, 1019]], [[306, 993], [418, 993], [418, 1018], [306, 1018]], [[151, 1035], [601, 1035], [601, 1061], [151, 1061]], [[159, 1079], [282, 1079], [282, 1104], [159, 1104]], [[328, 1078], [401, 1078], [401, 1107], [328, 1107]], [[454, 1078], [544, 1078], [544, 1106], [454, 1106]], [[578, 1079], [753, 1079], [753, 1103], [578, 1103]], [[776, 1075], [850, 1075], [850, 1104], [776, 1104]], [[912, 1074], [965, 1074], [965, 1106], [912, 1106]], [[647, 1105], [683, 1105], [683, 1135], [647, 1135]], [[797, 1103], [831, 1103], [831, 1135], [797, 1135]], [[157, 1150], [283, 1150], [283, 1174], [157, 1174]], [[453, 1150], [542, 1147], [543, 1176], [454, 1178]], [[777, 1146], [850, 1146], [850, 1176], [777, 1176]], [[798, 1175], [830, 1175], [830, 1206], [798, 1206]], [[956, 1195], [1052, 1200], [1049, 1257], [953, 1253]], [[818, 1216], [967, 1218], [967, 1250], [817, 1247]], [[225, 1232], [305, 1220], [313, 1266], [232, 1279]], [[516, 1241], [588, 1258], [556, 1391], [484, 1375]], [[152, 1267], [170, 1267], [170, 1286], [152, 1286]], [[483, 1266], [507, 1266], [507, 1291], [483, 1291]], [[150, 1297], [168, 1297], [168, 1315], [150, 1315]], [[844, 1306], [908, 1306], [908, 1339], [844, 1339]], [[338, 1324], [382, 1337], [373, 1364], [330, 1351]], [[679, 1319], [710, 1334], [699, 1358], [667, 1344]], [[277, 1350], [343, 1350], [343, 1380], [277, 1380]], [[375, 1346], [467, 1350], [466, 1382], [374, 1379]], [[800, 1343], [1023, 1350], [1022, 1387], [799, 1381]], [[544, 1383], [568, 1383], [568, 1407], [544, 1407]], [[712, 1383], [882, 1383], [882, 1410], [712, 1410]], [[519, 1395], [529, 1385], [538, 1393], [527, 1403]]], "text_det_params": {"limit_side_len": 736, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["附件1编号：BGMGZXS08-01B", "广州宝钢南方贸易有限公司", "客户登记表", "客户名称", "上海硕海金属材料有限公司", "注册地址/开票", "上海市", "邮编", "20190", "开票电", "021-567", "传真", "地址", "0", "话", "89696", "经营联系地址", "邮编", "20190", "电话", "021-567", "传真", "上海市宝山区", "（发票邮 寄 地", "友谊路1588弄", "0", "89696", "址）", "11号907", "开户银行", "建行上海宝钢宝", "帐号31001517", "山支行", "700050050602", "注册资金", "1000万", "企业性质", "有限责任公司", "9131011359643", "法人代表", "袁绿庆", "统一社会信用代", "7691Y", "单位所在省份及", "上海市", "码", "城市", "联系人", "班云春", "联系人手机号码", "15901800122", "是否自助打印质保书：是", "否□", "是否开通电子商务平台账号：是", "否□", "发票", "自取", "取发票人签", "传递", "□", "名", "方式", "邮", "寄", "寄往（注册", "收件人", "上海市宝山区友谊路1588弄11号907", "□", "地/经营地）", "本公司授权下列人员代表公司处理有关合同业务：", "委托代理人1", "班云春", "身份证号", "3101131976062539", "授权期", "长期", "10", "限", "委托代理人2", "身份证号", "授权期", "限", "保衣", "法人代表签名：", "材料", "", "单", "合", "章", "签章：", "华", "口", "20%5年", "月20日", "2025年3月20日", "章", "回传至南方公司", "王"], "rec_scores": [0.9966143369674683, 0.9989986419677734, 0.9982539415359497, 0.9972742795944214, 0.998084545135498, 0.9967570900917053, 0.9996321797370911, 0.9999170899391174, 0.9986029863357544, 0.9993786215782166, 0.9974397420883179, 0.9998304843902588, 0.9997153282165527, 0.9930167198181152, 0.9992807507514954, 0.999210000038147, 0.9987151622772217, 0.999686062335968, 0.998011589050293, 0.9992212653160095, 0.9986616373062134, 0.9997373819351196, 0.9995209574699402, 0.8464593887329102, 0.9969947338104248, 0.9914372563362122, 0.9988986849784851, 0.9513593912124634, 0.9987238049507141, 0.9985437989234924, 0.9996424913406372, 0.9939414262771606, 0.9996247291564941, 0.9981557726860046, 0.9995015859603882, 0.9957278966903687, 0.9997664093971252, 0.999819278717041, 0.9994738101959229, 0.9991704225540161, 0.9981739521026611, 0.9946442246437073, 0.998565673828125, 0.9991394877433777, 0.9997425079345703, 0.9999343156814575, 0.9997857213020325, 0.9996177554130554, 0.9984950423240662, 0.9993475675582886, 0.9995359778404236, 0.9898557066917419, 0.8415824174880981, 0.9946414232254028, 0.7719948291778564, 0.9997578859329224, 0.988797664642334, 0.9997908473014832, 0.9998376369476318, 0.9657235741615295, 0.9997472167015076, 0.9990170001983643, 0.9999899864196777, 0.9999915361404419, 0.993302047252655, 0.9997618198394775, 0.9981653094291687, 0.962605357170105, 0.9201473593711853, 0.9991509914398193, 0.9982008934020996, 0.997608482837677, 0.9996222257614136, 0.999504804611206, 0.9990003108978271, 0.9997690320014954, 0.9730974435806274, 0.9998865127563477, 0.9983384609222412, 0.9996564984321594, 0.9997067451477051, 0.999911904335022, 0.2319386601448059, 0.9887485504150391, 0.9232661724090576, 0.0, 0.9988186955451965, 0.999755322933197, 0.9996333122253418, 0.9866812229156494, 0.04666292667388916, 0.07326015084981918, 0.8067992329597473, 0.8074904680252075, 0.9152145385742188, 0.9998459815979004, 0.9991251826286316, 0.14176124334335327], "rec_polys": [[[217, 179], [573, 177], [573, 207], [218, 209]], [[418, 234], [847, 228], [848, 264], [418, 270]], [[504, 288], [759, 283], [760, 324], [505, 330]], [[154, 355], [245, 358], [244, 386], [153, 384]], [[336, 359], [584, 354], [584, 379], [336, 383]], [[152, 399], [320, 402], [320, 430], [152, 427]], [[328, 398], [402, 398], [402, 428], [328, 428]], [[487, 397], [540, 397], [540, 429], [487, 429]], [[568, 398], [633, 398], [633, 427], [568, 427]], [[653, 397], [742, 395], [743, 423], [654, 425]], [[753, 395], [836, 395], [836, 423], [753, 423]], [[857, 392], [911, 392], [911, 424], [857, 424]], [[151, 425], [205, 428], [203, 459], [149, 455]], [[569, 429], [592, 429], [592, 454], [569, 454]], [[653, 423], [685, 423], [685, 454], [653, 454]], [[753, 425], [815, 425], [815, 450], [753, 450]], [[151, 471], [318, 473], [318, 500], [151, 498]], [[485, 470], [539, 470], [539, 501], [485, 501]], [[568, 470], [632, 470], [632, 499], [568, 499]], [[653, 467], [706, 467], [706, 498], [653, 498]], [[753, 466], [837, 466], [837, 494], [753, 494]], [[855, 463], [912, 463], [912, 497], [855, 497]], [[332, 473], [474, 473], [474, 497], [332, 497]], [[157, 501], [318, 503], [318, 527], [157, 525]], [[332, 502], [473, 502], [473, 526], [332, 526]], [[569, 502], [590, 502], [590, 527], [569, 527]], [[751, 496], [815, 494], [817, 522], [752, 525]], [[149, 527], [196, 527], [196, 557], [149, 557]], [[330, 529], [421, 529], [421, 556], [330, 556]], [[151, 572], [243, 572], [243, 601], [151, 601]], [[330, 572], [474, 574], [474, 598], [330, 596]], [[487, 574], [640, 571], [640, 598], [488, 601]], [[328, 600], [392, 600], [392, 629], [328, 629]], [[487, 604], [639, 601], [639, 626], [488, 629]], [[152, 644], [242, 647], [241, 675], [151, 673]], [[330, 647], [404, 647], [404, 673], [330, 673]], [[487, 645], [576, 645], [576, 673], [487, 673]], [[653, 645], [782, 642], [782, 670], [654, 672]], [[329, 692], [471, 692], [471, 716], [329, 716]], [[486, 690], [576, 690], [576, 718], [486, 718]], [[653, 687], [724, 687], [724, 716], [653, 716]], [[152, 714], [305, 714], [305, 741], [152, 741]], [[327, 719], [396, 719], [396, 747], [327, 747]], [[486, 734], [639, 731], [639, 758], [486, 760]], [[650, 730], [724, 727], [725, 757], [651, 760]], [[152, 740], [181, 740], [181, 768], [152, 768]], [[484, 759], [537, 759], [537, 791], [484, 791]], [[149, 802], [222, 804], [221, 837], [148, 834]], [[327, 805], [400, 805], [400, 835], [327, 835]], [[486, 807], [639, 805], [639, 830], [486, 832]], [[650, 805], [771, 803], [771, 828], [651, 830]], [[149, 850], [418, 845], [419, 875], [149, 879]], [[420, 846], [478, 846], [478, 876], [420, 876]], [[483, 846], [807, 845], [807, 873], [483, 874]], [[810, 845], [863, 842], [864, 871], [811, 874]], [[150, 892], [203, 892], [203, 922], [150, 922]], [[211, 891], [299, 891], [299, 924], [211, 924]], [[304, 894], [423, 894], [423, 919], [304, 919]], [[149, 920], [201, 920], [201, 949], [149, 949]], [[213, 922], [243, 922], [243, 947], [213, 947]], [[301, 919], [335, 919], [335, 950], [301, 950]], [[148, 947], [201, 947], [201, 979], [148, 979]], [[213, 957], [274, 967], [269, 1001], [207, 992]], [[259, 965], [292, 965], [292, 990], [259, 990]], [[304, 965], [424, 965], [424, 990], [304, 990]], [[436, 963], [507, 963], [507, 993], [436, 993]], [[579, 964], [930, 961], [930, 986], [579, 989]], [[212, 993], [243, 993], [243, 1019], [212, 1019]], [[306, 993], [418, 993], [418, 1018], [306, 1018]], [[151, 1035], [601, 1035], [601, 1061], [151, 1061]], [[159, 1079], [282, 1079], [282, 1104], [159, 1104]], [[328, 1078], [401, 1078], [401, 1107], [328, 1107]], [[454, 1078], [544, 1078], [544, 1106], [454, 1106]], [[578, 1079], [753, 1079], [753, 1103], [578, 1103]], [[776, 1075], [850, 1075], [850, 1104], [776, 1104]], [[912, 1074], [965, 1074], [965, 1106], [912, 1106]], [[647, 1105], [683, 1105], [683, 1135], [647, 1135]], [[797, 1103], [831, 1103], [831, 1135], [797, 1135]], [[157, 1150], [283, 1150], [283, 1174], [157, 1174]], [[453, 1150], [542, 1147], [543, 1176], [454, 1178]], [[777, 1146], [850, 1146], [850, 1176], [777, 1176]], [[798, 1175], [830, 1175], [830, 1206], [798, 1206]], [[956, 1195], [1052, 1200], [1049, 1257], [953, 1253]], [[818, 1216], [967, 1218], [967, 1250], [817, 1247]], [[225, 1232], [305, 1220], [313, 1266], [232, 1279]], [[516, 1241], [588, 1258], [556, 1391], [484, 1375]], [[152, 1267], [170, 1267], [170, 1286], [152, 1286]], [[483, 1266], [507, 1266], [507, 1291], [483, 1291]], [[150, 1297], [168, 1297], [168, 1315], [150, 1315]], [[844, 1306], [908, 1306], [908, 1339], [844, 1339]], [[338, 1324], [382, 1337], [373, 1364], [330, 1351]], [[679, 1319], [710, 1334], [699, 1358], [667, 1344]], [[277, 1350], [343, 1350], [343, 1380], [277, 1380]], [[375, 1346], [467, 1350], [466, 1382], [374, 1379]], [[800, 1343], [1023, 1350], [1022, 1387], [799, 1381]], [[544, 1383], [568, 1383], [568, 1407], [544, 1407]], [[712, 1383], [882, 1383], [882, 1410], [712, 1410]], [[519, 1395], [529, 1385], [538, 1393], [527, 1403]]], "rec_boxes": [[217, 177, 573, 209], [418, 228, 848, 270], [504, 283, 760, 330], [153, 355, 245, 386], [336, 354, 584, 383], [152, 399, 320, 430], [328, 398, 402, 428], [487, 397, 540, 429], [568, 398, 633, 427], [653, 395, 743, 425], [753, 395, 836, 423], [857, 392, 911, 424], [149, 425, 205, 459], [569, 429, 592, 454], [653, 423, 685, 454], [753, 425, 815, 450], [151, 471, 318, 500], [485, 470, 539, 501], [568, 470, 632, 499], [653, 467, 706, 498], [753, 466, 837, 494], [855, 463, 912, 497], [332, 473, 474, 497], [157, 501, 318, 527], [332, 502, 473, 526], [569, 502, 590, 527], [751, 494, 817, 525], [149, 527, 196, 557], [330, 529, 421, 556], [151, 572, 243, 601], [330, 572, 474, 598], [487, 571, 640, 601], [328, 600, 392, 629], [487, 601, 639, 629], [151, 644, 242, 675], [330, 647, 404, 673], [487, 645, 576, 673], [653, 642, 782, 672], [329, 692, 471, 716], [486, 690, 576, 718], [653, 687, 724, 716], [152, 714, 305, 741], [327, 719, 396, 747], [486, 731, 639, 760], [650, 727, 725, 760], [152, 740, 181, 768], [484, 759, 537, 791], [148, 802, 222, 837], [327, 805, 400, 835], [486, 805, 639, 832], [650, 803, 771, 830], [149, 845, 419, 879], [420, 846, 478, 876], [483, 845, 807, 874], [810, 842, 864, 874], [150, 892, 203, 922], [211, 891, 299, 924], [304, 894, 423, 919], [149, 920, 201, 949], [213, 922, 243, 947], [301, 919, 335, 950], [148, 947, 201, 979], [207, 957, 274, 1001], [259, 965, 292, 990], [304, 965, 424, 990], [436, 963, 507, 993], [579, 961, 930, 989], [212, 993, 243, 1019], [306, 993, 418, 1018], [151, 1035, 601, 1061], [159, 1079, 282, 1104], [328, 1078, 401, 1107], [454, 1078, 544, 1106], [578, 1079, 753, 1103], [776, 1075, 850, 1104], [912, 1074, 965, 1106], [647, 1105, 683, 1135], [797, 1103, 831, 1135], [157, 1150, 283, 1174], [453, 1147, 543, 1178], [777, 1146, 850, 1176], [798, 1175, 830, 1206], [953, 1195, 1052, 1257], [817, 1216, 967, 1250], [225, 1220, 313, 1279], [484, 1241, 588, 1391], [152, 1267, 170, 1286], [483, 1266, 507, 1291], [150, 1297, 168, 1315], [844, 1306, 908, 1339], [330, 1324, 382, 1364], [667, 1319, 710, 1358], [277, 1350, 343, 1380], [374, 1346, 467, 1382], [799, 1343, 1023, 1387], [544, 1383, 568, 1407], [712, 1383, 882, 1410], [519, 1385, 538, 1403]]}
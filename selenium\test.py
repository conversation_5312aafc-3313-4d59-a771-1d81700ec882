from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
import time
import os
import sys

# 尝试导入我们的ChromeDriver下载器
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
try:
    from download_chromedriver import download_chromedriver, get_chrome_version
    has_downloader = True
except ImportError:
    has_downloader = False

# 设置Chrome WebDriver选项
options = webdriver.ChromeOptions()
options.add_argument("--start-maximized")  # 最大化窗口


# 添加其他有用的选项
options.add_argument("--disable-blink-features=AutomationControlled")  # 避免被检测为自动化工具
options.add_experimental_option("excludeSwitches", ["enable-automation"])  # 禁用自动化提示
options.add_experimental_option("useAutomationExtension", False)  # 禁用自动化扩展


# 添加选项以处理Chrome已在运行的情况
options.add_argument("--remote-debugging-port=9222")  # 添加调试端口

# 如果Chrome已经在运行，可能会出现DevToolsActivePort文件问题
# 添加这个选项可以避免该问题
options.add_argument("--no-sandbox")
options.add_argument("--disable-dev-shm-usage")

# 尝试初始化
driver = webdriver.Chrome(options=options)
print("成功使用Chrome用户配置文件初始化WebDriver")


# 要打开的链接列表
# 默认只打开5个标签页，可以根据需要调整数量
num_tabs = 10  # 可以根据需要修改这个值
urls = ["https://imctest.baogang.info/imc-xa/"] * num_tabs

# 如果需要不同的URL，可以使用下面的方式
# urls = [f"https://example.com/page_{i}" for i in range(1, num_tabs+1)]

print(f"将打开 {len(urls)} 个标签页")

# 定义一个函数来保存和加载cookies
def save_cookies(driver, cookie_file='cookies.txt'):
    """保存当前浏览器的cookies到文件"""
    cookies = driver.get_cookies()
    if cookies:
        with open(cookie_file, 'w') as f:
            for cookie in cookies:
                f.write(str(cookie) + '\n')
        print(f"已保存 {len(cookies)} 个cookies到 {cookie_file}")
    else:
        print("没有找到cookies可保存")

def load_cookies(driver, cookie_file='cookies.txt'):
    """从文件加载cookies到浏览器"""
    if os.path.exists(cookie_file):
        with open(cookie_file, 'r') as f:
            cookies = f.readlines()

        if cookies:
            print(f"正在加载 {len(cookies)} 个cookies...")
            for cookie_str in cookies:
                try:
                    cookie_dict = eval(cookie_str.strip())
                    # 某些cookie可能有特定的domain要求
                    if 'domain' in cookie_dict:
                        # 确保当前URL与cookie的domain匹配
                        current_url = driver.current_url
                        if cookie_dict['domain'].strip('.') in current_url:
                            print("加载cookie：name="+cookie_dict['name']+ "  value="+cookie_dict['value'])
                            driver.add_cookie(cookie_dict)
                    else:
                        driver.add_cookie(cookie_dict)
                except Exception as e:
                    print(f"加载cookie时出错: {e}")
            print("Cookies加载完成")
            return True
    print("没有找到cookies文件或文件为空")
    return False

# 打开第一个链接作为基准页面
firstUrl="http://imctest.baogang.info/imc-xa/assets/img/backgroundRobot.ae1092cd.png"
print(f"正在打开第一个链接: {firstUrl}")
driver.get(firstUrl)

# 尝试从文件加载cookies
cookies_loaded = load_cookies(driver)

# if not cookies_loaded:
#     print("没有找到保存的cookies，可能需要手动登录")
#     # 保存cookies以便将来使用
#     save_cookies(driver)
#     print("已保存cookies，下次运行时将自动使用")
# else:
#     # 刷新页面以应用cookies
#     driver.refresh()
#     # time.sleep(2)
# time.sleep(2)  # 等待页面加载

# 检查是否需要手动保存cookies（如果使用用户配置文件失败）

driver.get(urls[0])
time.sleep(2)
while(driver.current_url!=urls[0]):
    # //需要登入
    time.sleep(2)
    print(f"需要登入：current_url={driver.current_url} url={urls[0]}")
save_cookies(driver)
# # //查找内容是“情报检索”的section标签，点击一下
# driver.find_element(By.XPATH, "//section[contains(text(), '情报检索')]").click()
# #在id="textareaInput"的textarea中输入内容aaa
# driver.find_element(By.ID, "textareaInput").send_keys("钛元素和铌元素分别如何影响钢材的耐腐蚀性和综合性能？")
# # 查找title="发送"的img标签，点击一下
# driver.find_element(By.XPATH, "//img[@title='发送']").click()


# 循环新建标签页并打开链接
print("正在打开其他标签页...")
for i, url in enumerate(urls[1:], 2):
    print(f"正在打开标签页 {i}: {url}")
    # 执行JavaScript打开新标签页
    driver.execute_script(f"window.open('{url}', '_blank');")
    time.sleep(1)  # 控制标签页打开速度
   
    driver.switch_to.window(driver.window_handles[i-1])


    driver.find_element(By.XPATH, "//section[contains(text(), '情报检索')]").click()
    #在id="textareaInput"的textarea中输入内容aaa
    driver.find_element(By.ID, "textareaInput").send_keys(f"i={i}")
    # 查找title="发送"的img标签，点击一下
    # driver.find_element(By.XPATH, "//img[@title='发送']").click()

# 切换到最新打开的标签页
# driver.switch_to.window(driver.window_handles[-1])
# print(f"已切换到最后一个标签页: {driver.current_url}")

# 保持浏览器窗口打开，直到用户决定关闭
print("\n所有标签页已打开。脚本将继续运行，浏览器窗口将保持打开状态。")
print("按Ctrl+C终止脚本并关闭浏览器。")

try:
    # 保持脚本运行
    while True:
        time.sleep(10)
        # save_cookies(driver)
except KeyboardInterrupt:
    print("\n用户终止脚本，正在关闭浏览器...")
    driver.quit()
    print("浏览器已关闭。")
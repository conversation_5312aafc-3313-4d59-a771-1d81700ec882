<?xml version="1.0" ?>
<testsuite name="unittest.suite._ErrorHolder-20250611130741" tests="1" time="0.000" failures="0" errors="1">
	<testcase classname="unittest.suite._ErrorHolder" name="FlameAPITester)" time="0.000">
		<error type="TypeError" message="FlameAPITester.assemble_auth_url() missing 1 required positional argument: 'self'"><![CDATA[Traceback (most recent call last):
  File "c:\vs_project\test\智能开户\test\test2.py", line 23, in setUpClass
    cls.ws_url = cls.assemble_auth_url(hosturl="ws://**********:30009/openapi/flames/api/v2/chat")
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: FlameAPITester.assemble_auth_url() missing 1 required positional argument: 'self'
]]></error>
	</testcase>
	<system-out><![CDATA[]]></system-out>
	<system-err><![CDATA[]]></system-err>
</testsuite>

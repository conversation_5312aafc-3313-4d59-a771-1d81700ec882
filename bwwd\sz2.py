import pandas as pd
import json,os

# 获取当前文件的绝对路径
current_file_path = os.path.abspath(__file__)
# 提取目录部分
current_dir = os.path.dirname(current_file_path)
# 文件路径
file_path = current_dir+r"/智能员工助理会话记录.xlsx"

# 读取 Excel 文件
df = pd.read_excel(file_path)

if '创建时间' in df.columns:
    # 提取 "content" 内容到新列 "答内容"
    def extract_date_content(x):
        date_str=str(x)
        if len(date_str)>8:
            return date_str[:8]
        return ""
        

    df['创建日期'] = df['创建时间'].apply(extract_date_content)
else:
    print("列 '创建时间' 不存在于文件中")
    
    
if '提问' in df.columns:
    # 提取 "content" 内容到新列 "答内容"
    def extract_user_content(x):
        if isinstance(x, str):
            try:
                data = json.loads(x)
                return data.get('content',None)
                
            except json.JSONDecodeError:
                
                pass
            except Exception as e:
                print(x)
                # exit()
        return None

    df['用户问题内容'] = df['提问'].apply(extract_user_content)
else:
    print("列 '提问' 不存在于文件中")
    

if '回答' in df.columns:
    # 提取 "content" 内容到新列 "答内容"
    def extract_content(x):
        if isinstance(x, str):
            try:
                data = json.loads(x)
                contents = data.get('contents', [])
                if contents and isinstance(contents, list) and len(contents) > 0:
                    content_dict = contents[0].get('content', {})
                    return content_dict.get('content', None)
            except json.JSONDecodeError:
                
                pass
            except Exception as e:
                print(x)
                # exit()
        return None

    df['回答内容'] = df['回答'].apply(extract_content)
else:
    print("列 '回答' 不存在于文件中")

# 显示前几行数据
# print(df.head())

# 保存到新文件
output_file_path = current_dir+r"/tmp.xlsx"
df.to_excel(output_file_path, index=False)
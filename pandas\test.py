import pandas as pd

# 文件路径
file_path = r'C:\vs_project\test\pandas\报告撰写宝信+讯飞问题跟踪表.xlsx'

# 读取 Excel 文件
df = pd.read_excel(file_path, sheet_name='问题集')   
# 默认读取第一个工作表    df = pd.read_excel(file_path, sheet_name='Sheet1')    读取指定工作表      df = pd.read_excel(file_path, sheet_name=0)     读取指定索引的工作表    

# 遍历 宝信收集问题 列
for index, row in df.iterrows():
    # 获取 "宝信收集问题" 列的值
    content = row['宝信收集问题']
    # 处理 "宝信收集问题" 列的值
    # ...
    # 将处理后的值保存回 "宝信收集问题" 列
    df.at[index, '讯飞结果'] = len(content)
    
print(df)
# 保存excel文件
# df.to_excel('output.xlsx', index=False)



import pandas as pd
import re

# 假设tmp是你的DataFrame
# tmp = pd.read_csv('tmp.csv')  # 根据实际情况读取文件

# tmp = pd.read_csv('tmp', header=None, names=['text'])  # 根据实际情况读取文件


# # 过滤出包含数字或字母的行
# filtered_tmp = tmp[tmp['text'].str.contains('[a-zA-Z0-9]', regex=True)]

# # 显示过滤后的数据
# print(filtered_tmp)

import pandas as pd
import re

arr = []
with open('tmp', 'r', encoding="utf-8") as file:
    for line in file:
        stripped_line = line.strip()
        
        if stripped_line and re.search(r'[a-zA-Z0-9]', stripped_line):
            # arr.append(stripped_line)
            # 去除年份和月份
            cleaned_line = re.sub(r'\d{4}年|\d{1,2}月', '', stripped_line)
            if re.search(r'[a-zA-Z0-9]', cleaned_line):
                arr.append(stripped_line)

# 去重
arr = list(set(arr))


# 去空行
arr = [line for line in arr if line]

# 显示处理后的数据
print("\n".join(arr))


# print(re.sub(r'\d{4}年|\d{1,2}月', '', "镀锡2月价格政策"))
from openpyxl import load_workbook
import os

# 获取当前文件的绝对路径
current_file_path = os.path.abspath(__file__)
# 提取目录部分
current_dir = os.path.dirname(current_file_path)
# 文件路径
# 加载 Excel 文件
wb = load_workbook(current_dir + r"/tmp.xlsx")
ws = wb.active

# 定义红色对应的 RGB 值（RGB(255,0,0)）
RED_COLOR = "FF0000"

# 遍历所有行，筛选红色字体的行
red_rows = []
for row in ws.iter_rows():
    for cell in row:
        # 检查单元格字体颜色是否为红色
        if cell.font and cell.font.color:
            # 确保 cell.font.color.rgb 是字符串格式
            font_color_rgb = cell.font.color.rgb
            if isinstance(font_color_rgb, str):
                print(font_color_rgb)
                if font_color_rgb == RED_COLOR:
                    red_rows.append(cell.row)  # 记录行号
                    break  # 跳出当前行循环
            else:
                # 如果 font_color_rgb 不是字符串，尝试将其转换为字符串
                # font_color_rgb = str(font_color_rgb)[2:]  # 去掉 '0x' 前缀
                font_color_rgb = str(font_color_rgb)
                print("vvv:"+font_color_rgb)
                if font_color_rgb == RED_COLOR:
                    red_rows.append(cell.row)  # 记录行号
                    break  # 跳出当前行循环

print(f"红色字体的行号：{red_rows}")

# 如果需要删除非红色行或保留红色行，可进一步操作
# 例如：保留红色字体行
rows_to_keep = set(red_rows)
for row in reversed(list(ws.iter_rows(min_row=1, max_row=ws.max_row))):
    if row[0].row not in rows_to_keep:
        ws.delete_rows(row[0].row)

# 保存修改后的文件
wb.save(current_dir + "/filtered_file.xlsx")
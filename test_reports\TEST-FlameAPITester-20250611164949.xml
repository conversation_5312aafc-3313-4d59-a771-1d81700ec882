<?xml version="1.0" ?>
<testsuite name="FlameAPITester-20250611164949" tests="1" time="2.002" failures="0" errors="0">
	<testcase classname="FlameAPITester" name="test_planning_agent" time="2.002"/>
	<system-out><![CDATA[发送消息: {"header": {"traceId": "TRACE-1749631789-cc7d4911", "bodyId": "xzrbgent6ue6b0btaorwxi5phhbyld3d", "appId": "2741701DAF894C02BA78", "mode": 0}, "parameter": {}, "payload": {"sessionId": "SESSION_001", "text": [{"content": "开户申请", "content_type": "text", "role": "user"}]}}
收到原始消息: {"header":{"code":-1,"message":"Data Not Found","status":2,"sid":"TALKSJOLKRJUNRZM5","traceId":"TRACE-1749631789-cc7d4911"},"payload":{}}

测试结果:
- 总响应数: 1
- 是否有错误: False
- 是否有有效响应: True
- 测试用时: 0.00秒
- 所有响应: [{'header': {'code': -1, 'message': 'Data Not Found', 'status': 2, 'sid': 'TALKSJOLKRJUNRZM5', 'traceId': 'TRACE-1749631789-cc7d4911'}, 'payload': {}}]
]]></system-out>
	<system-err><![CDATA[]]></system-err>
</testsuite>

import requests
import time
import sys
import json
from urllib.parse import urlparse

# Configuration
CRAWLER_API = "http://localhost:11235"
API_KEY = "Bearer 12345"
DEFAULT_TIMEOUT = 10  # seconds
MAX_RETRIES = 3
POLL_INTERVAL = 5  # seconds
MAX_POLL_ATTEMPTS = 60  # 5 minutes total polling time

def check_service_health():
    """Check if the crawler service is running and healthy."""
    try:
        response = requests.get(f"{CRAWLER_API}/health", timeout=DEFAULT_TIMEOUT)
        if response.status_code == 200:
            health_data = response.json()
            print(f"Service is healthy. Available slots: {health_data.get('available_slots', 'unknown')}")
            print(f"Memory usage: {health_data.get('memory_usage', 'unknown')}%")
            print(f"CPU usage: {health_data.get('cpu_usage', 'unknown')}%")
            return True
        else:
            print(f"Service returned status code {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"Error connecting to crawler service: {e}")
        return False

def check_url_accessibility(url):
    """Check if the URL is accessible."""
    try:
        # Just check the head to avoid downloading the entire page
        parsed_url = urlparse(url)
        # Use a different timeout for this check
        response = requests.head(url, timeout=5, allow_redirects=True)
        print(f"URL {url} is accessible (status code: {response.status_code})")
        return True
    except requests.exceptions.RequestException as e:
        print(f"Error accessing URL {url}: {e}")
        return False

def submit_crawl_job(url, priority=10):
    """Submit a crawl job to the service."""
    for attempt in range(MAX_RETRIES):
        try:
            response = requests.post(
                f"{CRAWLER_API}/crawl",
                json={"urls": url, "priority": priority},
                headers={"Authorization": API_KEY},
                timeout=DEFAULT_TIMEOUT
            )
            
            if response.status_code == 200:
                result = response.json()
                task_id = result.get("task_id")
                if task_id:
                    print(f"Successfully submitted crawl job. Task ID: {task_id}")
                    return task_id
                else:
                    print(f"No task ID in response: {result}")
            else:
                print(f"Failed to submit crawl job. Status code: {response.status_code}")
                print(f"Response: {response.text}")
            
        except requests.exceptions.RequestException as e:
            print(f"Error submitting crawl job (attempt {attempt+1}/{MAX_RETRIES}): {e}")
        
        # Wait before retrying
        if attempt < MAX_RETRIES - 1:
            print(f"Retrying in {DEFAULT_TIMEOUT} seconds...")
            time.sleep(DEFAULT_TIMEOUT)
    
    print("Failed to submit crawl job after multiple attempts")
    return None

def check_task_status(task_id):
    """Check the status of a crawl task."""
    try:
        response = requests.get(
            f"{CRAWLER_API}/task/{task_id}",
            headers={"Authorization": API_KEY},
            timeout=DEFAULT_TIMEOUT
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Failed to check task status. Status code: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"Error checking task status: {e}")
        return None

def poll_until_complete(task_id):
    """Poll the task status until it completes or fails."""
    for attempt in range(MAX_POLL_ATTEMPTS):
        status_data = check_task_status(task_id)
        
        if not status_data:
            print("Failed to get task status")
            time.sleep(POLL_INTERVAL)
            continue
            
        status = status_data.get("status")
        print(f"Task status: {status}")
        
        if status == "completed":
            print("Task completed successfully!")
            return status_data
        elif status in ["failed", "error"]:
            print(f"Task failed: {status_data.get('error', 'Unknown error')}")
            return status_data
        elif status == "running":
            progress = status_data.get("progress", {})
            if progress:
                print(f"Progress: {progress.get('current', 0)}/{progress.get('total', 0)} pages")
        
        print(f"Waiting {POLL_INTERVAL} seconds before checking again...")
        time.sleep(POLL_INTERVAL)
    
    print(f"Gave up after {MAX_POLL_ATTEMPTS} polling attempts")
    return None

def save_results(task_data, filename=None):
    """Save the crawl results to a file."""
    if not filename:
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        filename = f"crawl_results_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(task_data, f, indent=2)
    
    print(f"Results saved to {filename}")

def main():
    # Check if the service is healthy
    if not check_service_health():
        print("Crawler service is not healthy. Exiting.")
        return
    
    # Get URL from command line or use default
    if len(sys.argv) > 1:
        url = sys.argv[1]
    else:
        # Default URL - change this to a URL that's accessible in your environment
        url = "http://example.com"
        print(f"No URL provided. Using default: {url}")
    
    # Check if the URL is accessible
    if not check_url_accessibility(url):
        print("The URL is not accessible. Please check the URL and try again.")
        return
    
    # Submit the crawl job
    task_id = submit_crawl_job(url)
    if not task_id:
        print("Failed to submit crawl job. Exiting.")
        return
    
    # Poll until the task completes
    result = poll_until_complete(task_id)
    if result:
        # Save the results
        save_results(result)
    else:
        print("Failed to get task results")

if __name__ == "__main__":
    main()

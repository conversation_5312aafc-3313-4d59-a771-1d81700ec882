<?xml version="1.0" ?>
<testsuite name="FlameAPITester-20250611104950" tests="1" time="2.003" failures="0" errors="1">
	<testcase classname="FlameAPITester" name="test_planning_agent" time="2.003">
		<error type="AssertionError" message="测试过程中发生错误: [('ERROR', 'WebSocket错误: Handshake status 401 Unauthorized -+-+- {\'server\': \'nginx/1.21.5\', \'date\': \'Wed, 11 Jun 2025 02:49:50 GMT\', \'content-type\': \'application/json;charset=UTF-8\', \'content-length\': \'55\', \'connection\': \'keep-alive\', \'cache-control\': \'no-cache, no-store, max-age=0, must-revalidate\', \'pragma\': \'no-cache\', \'expires\': \'0\', \'x-content-type-options\': \'nosniff\', \'x-frame-options\': \'DENY\', \'x-xss-protection\': \'1 ; mode=block\', \'referrer-policy\': \'no-referrer\'} -+-+- b\'{&quot;code&quot;:401,&quot;data&quot;:null,&quot;message&quot;:&quot;\\xe7\\xad\\xbe\\xe5\\x90\\x8d\\xe5\\x8f\\x82\\xe6\\x95\\xb0\\xe4\\xb8\\xba\\xe7\\xa9\\xba&quot;}\'')]"><![CDATA[Traceback (most recent call last):
  File "c:\vs_project\test\智能开户\test\test2.py", line 215, in test_planning_agent
    self.fail(f"测试过程中发生错误: {all_responses}")
AssertionError: 测试过程中发生错误: [('ERROR', 'WebSocket错误: Handshake status 401 Unauthorized -+-+- {\'server\': \'nginx/1.21.5\', \'date\': \'Wed, 11 Jun 2025 02:49:50 GMT\', \'content-type\': \'application/json;charset=UTF-8\', \'content-length\': \'55\', \'connection\': \'keep-alive\', \'cache-control\': \'no-cache, no-store, max-age=0, must-revalidate\', \'pragma\': \'no-cache\', \'expires\': \'0\', \'x-content-type-options\': \'nosniff\', \'x-frame-options\': \'DENY\', \'x-xss-protection\': \'1 ; mode=block\', \'referrer-policy\': \'no-referrer\'} -+-+- b\'{"code":401,"data":null,"message":"\\xe7\\xad\\xbe\\xe5\\x90\\x8d\\xe5\\x8f\\x82\\xe6\\x95\\xb0\\xe4\\xb8\\xba\\xe7\\xa9\\xba"}\'')]
]]></error>
	</testcase>
	<system-out><![CDATA[开始测试规划型智能体...
构建的payload: {
  "header": {
    "traceId": "TRACE-1749610190-9cbd893c",
    "bodyId": "xzrbcess5ht7xw5o2l36x5px9owbscou",
    "appId": "9BC895C708EB440D804C",
    "mode": 0
  },
  "parameter": {},
  "payload": {
    "sessionId": "SESSION_001",
    "text": [
      {
        "content": "开户申请",
        "content_type": "text",
        "role": "user"
      }
    ]
  }
}
WebSocket错误: Handshake status 401 Unauthorized -+-+- {'server': 'nginx/1.21.5', 'date': 'Wed, 11 Jun 2025 02:49:50 GMT', 'content-type': 'application/json;charset=UTF-8', 'content-length': '55', 'connection': 'keep-alive', 'cache-control': 'no-cache, no-store, max-age=0, must-revalidate', 'pragma': 'no-cache', 'expires': '0', 'x-content-type-options': 'nosniff', 'x-frame-options': 'DENY', 'x-xss-protection': '1 ; mode=block', 'referrer-policy': 'no-referrer'} -+-+- b'{"code":401,"data":null,"message":"\xe7\xad\xbe\xe5\x90\x8d\xe5\x8f\x82\xe6\x95\xb0\xe4\xb8\xba\xe7\xa9\xba"}'
错误类型: <class 'websocket._exceptions.WebSocketBadStatusException'>
### 连接已关闭 ### 状态码: None, 消息: None
等待服务器响应...
收到响应: ('ERROR', 'WebSocket错误: Handshake status 401 Unauthorized -+-+- {\'server\': \'nginx/1.21.5\', \'date\': \'Wed, 11 Jun 2025 02:49:50 GMT\', \'content-type\': \'application/json;charset=UTF-8\', \'content-length\': \'55\', \'connection\': \'keep-alive\', \'cache-control\': \'no-cache, no-store, max-age=0, must-revalidate\', \'pragma\': \'no-cache\', \'expires\': \'0\', \'x-content-type-options\': \'nosniff\', \'x-frame-options\': \'DENY\', \'x-xss-protection\': \'1 ; mode=block\', \'referrer-policy\': \'no-referrer\'} -+-+- b\'{"code":401,"data":null,"message":"\\xe7\\xad\\xbe\\xe5\\x90\\x8d\\xe5\\x8f\\x82\\xe6\\x95\\xb0\\xe4\\xb8\\xba\\xe7\\xa9\\xba"}\'')
====================错误==================
错误详情: WebSocket错误: Handshake status 401 Unauthorized -+-+- {'server': 'nginx/1.21.5', 'date': 'Wed, 11 Jun 2025 02:49:50 GMT', 'content-type': 'application/json;charset=UTF-8', 'content-length': '55', 'connection': 'keep-alive', 'cache-control': 'no-cache, no-store, max-age=0, must-revalidate', 'pragma': 'no-cache', 'expires': '0', 'x-content-type-options': 'nosniff', 'x-frame-options': 'DENY', 'x-xss-protection': '1 ; mode=block', 'referrer-policy': 'no-referrer'} -+-+- b'{"code":401,"data":null,"message":"\xe7\xad\xbe\xe5\x90\x8d\xe5\x8f\x82\xe6\x95\xb0\xe4\xb8\xba\xe7\xa9\xba"}'

测试结果:
- 总响应数: 1
- 是否有错误: True
- 是否有有效响应: False
- 测试用时: 0.00秒
- 所有响应: [('ERROR', 'WebSocket错误: Handshake status 401 Unauthorized -+-+- {\'server\': \'nginx/1.21.5\', \'date\': \'Wed, 11 Jun 2025 02:49:50 GMT\', \'content-type\': \'application/json;charset=UTF-8\', \'content-length\': \'55\', \'connection\': \'keep-alive\', \'cache-control\': \'no-cache, no-store, max-age=0, must-revalidate\', \'pragma\': \'no-cache\', \'expires\': \'0\', \'x-content-type-options\': \'nosniff\', \'x-frame-options\': \'DENY\', \'x-xss-protection\': \'1 ; mode=block\', \'referrer-policy\': \'no-referrer\'} -+-+- b\'{"code":401,"data":null,"message":"\\xe7\\xad\\xbe\\xe5\\x90\\x8d\\xe5\\x8f\\x82\\xe6\\x95\\xb0\\xe4\\xb8\\xba\\xe7\\xa9\\xba"}\'')]
]]></system-out>
	<system-err><![CDATA[]]></system-err>
</testsuite>

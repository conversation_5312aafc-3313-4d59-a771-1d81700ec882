
import dmPython
import csv
from datetime import datetime, timedelta
import os
DB_CONFIG = {
    "user": "iplat4j",
    "password": "dameng123",
    "server": "**********",
    "port": 52025,
    # "charset": "GB18030"
    # "charset": "UTF-8"  # Use UTF-8 instead of GBK for better Unicode support
}
# 计算时间范围
now = datetime.now()
# 本周开始时间

start_date = (now - timedelta(days=now.weekday())).replace(hour=0, minute=0, second=0, microsecond=0)
end_date = start_date + timedelta(days=7)

# time_condition = f"{EXPORT_CONFIG['time_field']} >= '{start_date.strftime('%Y-%m-%d %H:%M:%S')}'"

# 连接数据库
conn = dmPython.connect(**DB_CONFIG)
cursor = conn.cursor()




# 查询数据
query = f'''
SELECT t1.UUID ,t1.SESSION_ID ,t1.SESSION_TITLE,t2.CONTENT  FROM IPLAT4J.TXBAI02 AS t1
LEFT JOIN TXBAI0201 t2 ON t1.SESSION_ID =t2.SESSION_ID
WHERE t1.INTENTION='1'  and t1.REC_CREATE_TIME >{start_date.strftime('%Y%m%d%H%M%S000')} and t1.REC_CREATE_TIME < {end_date.strftime('%Y%m%d%H%M%S000')}   '''
print(query)
cursor.execute(query)
rows = cursor.fetchall()
print(rows)
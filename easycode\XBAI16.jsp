<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<%@ page import="com.baosight.iplat4j.core.ei.EiInfo" %>
<%@ page import="com.baosight.iplat4j.core.resource.I18nMessages" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imc">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
            <EF:EFInput ename="inqu_status-0-customCompany" cname="走访公司" colWidth="3" ratio="4:8"/>


            <EF:EFInput ename="inqu_status-0-directUserNum" cname="订货用户代码" colWidth="3" ratio="4:8"
                        data-regex="/^([a-zA-Z0-9]|[_]|[-])+$/"/>
            <EF:EFInput ename="inqu_status-0-directUserName" cname="订货用户名称" colWidth="3" ratio="4:8"/>

            <EF:EFInput ename="inqu_status-0-finUserId" cname="最终用户代码" colWidth="3" ratio="4:8"
                        data-regex="/^([a-zA-Z0-9]|[_]|[-])+$/"/>
            <EF:EFInput ename="inqu_status-0-finUserName" cname="最终用户名称" colWidth="3" ratio="4:8"/>

        </div>


    </EF:EFRegion>

    <EF:EFRegion id="result" title="记录集">
        <input type="hidden" id="editRow"/>
        <EF:EFGrid blockId="result" isFloat="true" autoDraw="no" toolbarConfig="true" needAuth="true">
            <EF:EFColumn ename="uuid" cname="" hidden="true"/>
            <EF:EFColumn ename="customCompany" cname="走访用户" sort="true" required="true" align="center"/>
            <EF:EFPopupColumn ename="directUserNum" align="center" width="50" cname="订货用户编码"
                              colWidth="8"
                              resizable="true" containerId="queryDirectUserNum" popupWidth="1000"
                              popupHeight="500"
                              popupTitle="订货用户基本信息查询:"
                              maxLength="8">
            </EF:EFPopupColumn>
            <EF:EFColumn ename="directUserName" cname="订货用户名称" align="center" readonly="true"/>
            <EF:EFPopupColumn ename="finUserId" cname="最终用户编码"
                              popupHight="800" popupWidth="1000" align="center" center="true"
                              resizable="true" containerId="queryDirectUserNum"
                              popupTitle="最终用户基本信息查询" refresh="true"
                              maxLength="10"/>
            <EF:EFColumn ename="finUserName" cname="最终用户名称" align="center" readonly="true"/>
            <EF:EFColumn ename="recCreator" cname="创建人" align="center" enable="false"/>
            <EF:EFColumn ename="recCreatorName" cname="创建人姓名" align="center" enable="false"/>
            <EF:EFColumn ename="segNo" align="center" cname='公司业务单元' enable="false" hidden="true"
                         width="80"/>
            <EF:EFColumn ename="unitCode" align="center" cname='业务单元' enable="false" hidden="true"
                         width="105"/>
            <EF:EFColumn ename="recCreateTime" cname="创建时间" align="center" enable="false"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyyMMddHHmmss" editType="datetime"/>
            <EF:EFColumn ename="recRevisor" cname="修改人" align="center" enable="false"/>
            <EF:EFColumn ename="recRevisorName" cname="修改人姓名" align="center" enable="false"/>
            <EF:EFColumn ename="recReviseTime" cname="修改时间" align="center" enable="false"
                         parseFormats="['yyyyMMddHHmmss']" dateFormat="yyyyMMddHHmmss" editType="datetime"/>
        </EF:EFGrid>
    </EF:EFRegion>
    <div style="display: none;" id="queryDirectUserNum">
        <EF:EFRegion title="查询条件">
            <div class="col-xs-10">
                <EF:EFInput ename="inqu_custom-0-userNum" cname="客商代码" colWidth="4"/>
                <EF:EFInput ename="inqu_custom-0-chineseUserName" cname="客商中文名" colWidth="4"/>
                <EF:EFInput ename="inqu_custom-0-englishUserName" cname="客商英文名" colWidth="4"/>
            </div>
            <div class="col-xs-2" style="text-align: right; float: right" id="inqu_group">
                <EF:EFButton ename="QUERY_DIRECT_USER" cname="查询"></EF:EFButton>
            </div>
        </EF:EFRegion>
        <EF:EFRegion id="resultDirectUser" title="客商用户信息">
            <EF:EFGrid blockId="resultDirectUser" enable="false" serviceName="XBAI16" queryMethod="queryCustomer"
                       autoDraw="override" isFloat="true" height="360" checkMode="single">
                <EF:EFColumn ename="userNum" cname="客商代码" width="60" align="center" enable="false"/>
                <EF:EFColumn ename="chineseUserName" cname="客商中文名" width="60" align="center" enable="false"/>
                <EF:EFColumn ename="englishUserName" cname="客商英文名" width="60" align="center" enable="false"/>
            </EF:EFGrid>
        </EF:EFRegion>
    </div>
</EF:EFPage>






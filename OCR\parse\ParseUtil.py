import os
from PdfParse  import PdfParse
from WordParse import WordParse
from PicParse import PicParse
class ParseUtil:
    @staticmethod
    def parse(file_path):
        #file_path文件是否存在
        if not os.path.exists(file_path):
            return ""
        #判断文件类型
        image_type = ParseUtil.get_file_type(file_path)
        if image_type=="pdf":
            return PdfParse.parse(file_path)
        if image_type=="docx":
            return WordParse.parse(file_path)
        if image_type in ["bmp", "dib","icns","ico","jfif", "jpe", "jpeg", "jpg","j2c", "j2k", "jp2", "jpc", "jpf", "jpx","apng", "png","bw", "rgb", "rgba", "sgi","tif", "tiff","webp"]:
            return PicParse.parse(file_path,image_type)
        print("不支持的文件类型[%s]",file_path)
        return ""
    
    @staticmethod
    def get_file_type(file_path):
        return file_path.split(".")[-1].lower()


if __name__ == "__main__":
    pdf_path = "C:\\vs_project\\test\\OCR\\t.pdf"
    text = ParseUtil.parse(pdf_path)

    print(text)

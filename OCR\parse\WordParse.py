from docx import Document
from docx.oxml.ns import qn
from PicParse import PicParse
from docx.oxml.shape import CT_Inline
from docx.oxml.exceptions import InvalidXmlError
import os
import tempfile
from lxml import etree

class WordParse:

    @staticmethod
    def extract_paragraph(paragraph, doc):
        """从段落中提取图片"""
        images_text = ""
        
        # 遍历段落中的每个run
        for run in paragraph.runs:
        # 提取文本
            if run.text.strip():
                images_text += run.text

            run_element = run._r
            # print(f"Run XML: {run_element.xml}")

            lxml_xml = etree.fromstring(run_element.xml)

            
            elements = lxml_xml.xpath('.//*[local-name()="inline" or local-name()="anchor"]')

            for element in elements:
                blip = element.find('.//a:blip', namespaces={'a': 'http://schemas.openxmlformats.org/drawingml/2006/main'})
                if blip is not None:
                    embed_id = blip.get(qn('r:embed'))
                    if embed_id:
                        rel = doc.part.rels.get(embed_id)
                        if rel and rel.target_part:
                            try:
                                img_blob = rel.target_part.blob
                                image_ext = rel.target_part.content_type.split('/')[-1]
                                images_text += PicParse.parse(img_blob, image_ext) + "\n"
                            except Exception as e:
                                print(f"解析图片失败：{e}")

        return images_text  

    @staticmethod
    def parseEle(element, doc):
        text = ""
        if element.tag.endswith('p'):  # 段落
            # 获取段落文本
            # text += element.text if element.text else ""

            # 从段落中提取图片
            # try:
                # 将XML元素转换为paragraph对象
                from docx.text.paragraph import Paragraph
                paragraph = Paragraph(element, doc)
                text += WordParse.extract_paragraph(paragraph, doc)
                # text += images_text
            # except Exception as e:
            #     print(f"处理段落图片时出错: {e}")

        # 处理表格
        elif element.tag.endswith('tbl'):  # 表格
            for row in element.rows:
                for cell in row.cells:
                    text += WordParse.parseEle(cell, doc)  # 递归处理单元格

        # 处理文本框（SdtElement）
        elif element.tag.endswith('sdt'):  # 文本框内容
            text += WordParse.parseEle(element, doc)

        return text
    @staticmethod
    def parse(docx_path):
        """解析Word文档，提取文本和图片内容"""
        # print(f"开始解析Word文档: {docx_path}")

        doc = Document(docx_path)
        result = ""

       
        for element in doc.element.body:
            element_text = WordParse.parseEle(element, doc)
            result += element_text

      
        return result


if __name__ == "__main__":
    docx_path = "C:\\Users\\<USER>\\Documents\\2.docx"
    text = WordParse.parse(docx_path)
    print(text)
    print("-"*20)
    
    # from QVQClient import QVQClient
    # api = QVQClient()
    # messages = api.get_image_message("请用中文描述图片", "extracted_image.png")
    # print(api.sync_chat(messages))
    
    # print(api.parse("extracted_image.png"))
    
    
    # images_text = PicParse.parse("extracted_image.png") 
    # print(images_text)
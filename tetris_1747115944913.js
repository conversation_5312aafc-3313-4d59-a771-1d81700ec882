// 当文档加载完成后执行初始化
document.addEventListener('DOMContentLoaded', () => {
    // 获取画布元素和其上下文
    const canvas = document.getElementById('tetris');
    const ctx = canvas.getContext('2d');
    // 获取下一个方块预览的画布和其上下文
    const nextPieceCanvas = document.getElementById('next-piece');
    const nextPieceCtx = nextPieceCanvas.getContext('2d');

    // 定义游戏常量
    const BLOCK_SIZE = 20; // 方块大小
    const BOARD_WIDTH = 12; // 游戏板宽度
    const BOARD_HEIGHT = 20; // 游戏板高度
    // 定义颜色数组
    const COLORS = [
        null,
        '#FF0D72', // I型方块颜色
        '#0DC2FF', // J型方块颜色
        '#0DFF72', // L型方块颜色
        '#F538FF', // O型方块颜色
        '#FF8E0D', // S型方块颜色
        '#FFE138', // T型方块颜色
        '#3877FF'  // Z型方块颜色
    ];

    // 定义不同类型的俄罗斯方块形状
    const PIECES = [
        [
            [0, 0, 0, 0],
            [1, 1, 1, 1],
            [0, 0, 0, 0],
            [0, 0, 0, 0]
        ],
        [
            [2, 0, 0],
            [2, 2, 2],
            [0, 0, 0]
        ],
        [
            [0, 0, 3],
            [3, 3, 3],
            [0, 0, 0]
        ],
        [
            [4, 4],
            [4, 4]
        ],
        [
            [0, 5, 5],
            [5, 5, 0],
            [0, 0, 0]
        ],
        [
            [0, 6, 0],
            [6, 6, 6],
            [0, 0, 0]
        ],
        [
            [7, 7, 0],
            [0, 7, 7],
            [0, 0, 0]
        ]
    ];

    // 初始化游戏变量
    let board = createBoard(); // 创建游戏板
    let score = 0; // 初始分数
    let lines = 0; // 消除的行数
    let level = 1; // 初始等级
    let dropInterval = 1000; // 方块下落间隔时间（毫秒）
    let lastTime = 0; // 上一次更新的时间戳
    let dropCounter = 0; // 下落计数器
    let gameOver = false; // 游戏是否结束标志
    let paused = false; // 游戏是否暂停标志
    let requestId = null; // 动画帧请求ID

    // 玩家对象
    const player = {
        pos: { x: 0, y: 0 }, // 玩家位置
        piece: null, // 当前方块
        score: 0 // 玩家分数
    };

    // 下一个方块
    let nextPiece = createPiece(Math.floor(Math.random() * PIECES.length)); // 随机选择下一个方块

    // DOM元素引用
    const scoreElement = document.getElementById('score'); // 分数显示元素
    const linesElement = document.getElementById('lines'); // 行数显示元素
    const levelElement = document.getElementById('level'); // 等级显示元素
    const startButton = document.getElementById('start-button'); // 开始/暂停按钮

    // 添加事件监听器
    document.addEventListener('keydown', handleKeyPress); // 键盘按键事件
    startButton.addEventListener('click', toggleGame); // 开始/暂停按钮点击事件

    // 创建新的游戏板
    function createBoard() {
        return Array.from({ length: BOARD_HEIGHT }, () => Array(BOARD_WIDTH).fill(0));
    }

    // 创建新的俄罗斯方块
    function createPiece(type) {
        return PIECES[type];
    }

    // 绘制单个方块
    function drawBlock(x, y, color, context = ctx) {
        context.fillStyle = color;
        context.fillRect(x * BLOCK_SIZE, y * BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
        context.strokeStyle = '#000';
        context.strokeRect(x * BLOCK_SIZE, y * BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
    }

    // 绘制游戏板
    function drawBoard() {
        board.forEach((row, y) => {
            row.forEach((value, x) => {
                if (value !== 0) {
                    drawBlock(x, y, COLORS[value]);
                }
            });
        });
    }

    // 绘制当前方块
    function drawPiece(piece, offset, context = ctx) {
        piece.forEach((row, y) => {
            row.forEach((value, x) => {
                if (value !== 0) {
                    drawBlock(
                        x + offset.x,
                        y + offset.y,
                        COLORS[value],
                        context
                    );
                }
            });
        });
    }

    // 绘制下一个方块预览
    function drawNextPiece() {
        nextPieceCtx.clearRect(0, 0, nextPieceCanvas.width, nextPieceCanvas.height);
        nextPieceCtx.fillStyle = '#111';
        nextPieceCtx.fillRect(0, 0, nextPieceCanvas.width, nextPieceCanvas.height);
        
        const offset = {
            x: Math.floor((5 - nextPiece[0].length) / 2),
            y: Math.floor((5 - nextPiece.length) / 2)
        };
        
        drawPiece(nextPiece, offset, nextPieceCtx);
    }

    // 绘制所有内容
    function draw() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        drawBoard();
        drawPiece(player.piece, player.pos);
        drawNextPiece();
    }

    // 检测碰撞
    function collide() {
        const [piece, pos] = [player.piece, player.pos];
        for (let y = 0; y < piece.length; y++) {
            for (let x = 0; x < piece[y].length; x++) {
                if (piece[y][x] !== 0 &&
                    (board[y + pos.y] === undefined ||
                     board[y + pos.y][x + pos.x] === undefined ||
                     board[y + pos.y][x + pos.x] !== 0)) {
                    return true;
                }
            }
        }
        return false;
    }

    // 合并方块到游戏板
    function merge() {
        player.piece.forEach((row, y) => {
            row.forEach((value, x) => {
                if (value !== 0) {
                    board[y + player.pos.y][x + player.pos.x] = value;
                }
            });
        });
    }

    // 重置玩家位置并获取新方块
    function resetPlayer() {
        player.piece = nextPiece;
        nextPiece = createPiece(Math.floor(Math.random() * PIECES.length));
        player.pos.y = 0;
        player.pos.x = Math.floor((BOARD_WIDTH - player.piece[0].length) / 2);
        
        // 检查游戏是否结束
        if (collide()) {
            gameOver = true;
            cancelAnimationFrame(requestId);
            requestId = null;
            alert('Game Over! Your score: ' + score);
        }
    }

    // 移动方块
    function playerMove(dir) {
        player.pos.x += dir;
        if (collide()) {
            player.pos.x -= dir;
        }
    }

    // 下落方块
    function playerDrop() {
        player.pos.y++;
        if (collide()) {
            player.pos.y--;
            merge();
            resetPlayer();
            clearLines();
            updateScore();
        }
        dropCounter = 0;
    }

    // 快速下落（直接到底）
    function playerHardDrop() {
        while (!collide()) {
            player.pos.y++;
        }
        player.pos.y--;
        merge();
        resetPlayer();
        clearLines();
        updateScore();
        dropCounter = 0;
    }

    // 旋转方块
    function playerRotate() {
        const pos = player.pos.x;
        let offset = 1;
        rotate();
        
        // 处理旋转时的碰撞
        while (collide()) {
            player.pos.x += offset;
            offset = -(offset + (offset > 0 ? 1 : -1));
            if (offset > player.piece[0].length) {
                rotate(-1);
                player.pos.x = pos;
                return;
            }
        }
    }

    // 旋转矩阵
    function rotate(dir = 1) {
        const piece = player.piece;
        // 转置矩阵
        for (let y = 0; y < piece.length; y++) {
            for (let x = 0; x < y; x++) {
                [piece[x][y], piece[y][x]] = [piece[y][x], piece[x][y]];
            }
        }
        
        // 根据方向反转行或列
        if (dir > 0) {
            piece.forEach(row => row.reverse());
        } else {
            piece.reverse();
        }
    }

    // 清除完成的行
    function clearLines() {
        let linesCleared = 0;
        
        outer: for (let y = BOARD_HEIGHT - 1; y >= 0; y--) {
            for (let x = 0; x < BOARD_WIDTH; x++) {
                if (board[y][x] === 0) {
                    continue outer;
                }
            }
            
            // 删除该行并在顶部添加空行
            const row = board.splice(y, 1)[0].fill(0);
            board.unshift(row);
            y++;
            linesCleared++;
        }
        
        // 更新行数和等级
        if (linesCleared > 0) {
            lines += linesCleared;
            linesElement.textContent = lines;
            
            // 每10行升一级
            const newLevel = Math.floor(lines / 10) + 1;
            if (newLevel > level) {
                level = newLevel;
                levelElement.textContent = level;
                // 增加速度
                dropInterval = Math.max(100, 1000 - (level - 1) * 100);
            }
            
            // 根据清除的行数和等级计算分数
            const linePoints = [0, 40, 100, 300, 1200];
            score += linePoints[linesCleared] * level;
            scoreElement.textContent = score;
        }
    }

    // 更新分数显示
    function updateScore() {
        scoreElement.textContent = score;
    }

    // 处理键盘输入
    function handleKeyPress(e) {
        if (gameOver || paused) return;
        
        switch (e.keyCode) {
            case 37: // 左箭头
                playerMove(-1);
                break;
            case 39: // 右箭头
                playerMove(1);
                break;
            case 40: // 下箭头
                playerDrop();
                break;
            case 38: // 上箭头
                playerRotate();
                break;
            case 32: // 空格键
                playerHardDrop();
                break;
            case 80: // P键
                toggleGame();
                break;
        }
    }

    // 切换游戏暂停状态
    function toggleGame() {
        if (!requestId) {
            // 如果游戏未运行，则开始游戏
            gameOver = false;
            paused = false;
            startButton.textContent = 'Pause';
            resetGame();
            update();
        } else {
            // 切换暂停状态
            paused = !paused;
            startButton.textContent = paused ? 'Resume' : 'Pause';
            if (!paused) {
                lastTime = 0;
                update();
            } else {
                cancelAnimationFrame(requestId);
                requestId = null;
                
                // 绘制“PAUSED”文本
                ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                ctx.font = '30px Arial';
                ctx.fillStyle = 'white';
                ctx.textAlign = 'center';
                ctx.fillText('PAUSED', canvas.width / 2, canvas.height / 2);
            }
        }
    }

    // 重置游戏
    function resetGame() {
        board = createBoard();
        score = 0;
        lines = 0;
        level = 1;
        dropInterval = 1000;
        scoreElement.textContent = score;
        linesElement.textContent = lines;
        levelElement.textContent = level;
        nextPiece = createPiece(Math.floor(Math.random() * PIECES.length));
        resetPlayer();
    }

    // 游戏更新循环
    function update(time = 0) {
        if (paused) return;
        
        const deltaTime = time - lastTime;
        lastTime = time;
        
        dropCounter += deltaTime;
        if (dropCounter > dropInterval) {
            playerDrop();
        }
        
        draw();
        requestId = requestAnimationFrame(update);
    }

    // 初始化游戏
    resetPlayer();
    draw();
});
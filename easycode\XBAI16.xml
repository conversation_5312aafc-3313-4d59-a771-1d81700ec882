<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">	<!--      table information
		Generate time : 2025-04-15 16:40:05
   		Version :  1.0
		table comment : 订货规则配置（走访分析用）表
		schema : iai
		tableName : TXBAI16
		 UUID  VARCHAR   NOT NULL, 
		 REC_CREATOR  VARCHAR   NOT NULL, 
		 REC_CREATOR_NAME  VARCHAR   NOT NULL, 
		 REC_CREATE_TIME  VARCHAR   NOT NULL, 
		 REC_REVISOR  VARCHAR   NOT NULL, 
		 REC_REVISOR_NAME  VARCHAR   NOT NULL, 
		 REC_REVISE_TIME  VARCHAR   NOT NULL, 
		 ARCHIVE_FLAG  VARCHAR   NOT NULL, 
		 DEL_FLAG  SMALLINT   NOT NULL, 
		 TENANT_USER  VARCHAR   NOT NULL, 
		 UNIT_CODE  VARCHAR   NOT NULL, 
		 SEG_NO  VARCHAR   NOT NULL, 
		 DIRECT_USER_NUM  VARCHAR   NOT NULL, 
		 DIRECT_USER_NAME  VARCHAR   NOT NULL, 
		 FIN_USER_ID  VARCHAR   NOT NULL, 
		 FIN_USER_NAME  VARCHAR   NOT NULL
		 CUSTOM_COMPANY  VARCHAR   NOT NULL,

	-->
<sqlMap namespace="XBAI16">

	<sql id="condition">
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantUser">
			TENANT_USER = #tenantUser#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="directUserNum">
			DIRECT_USER_NUM = #directUserNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="directUserName">
			DIRECT_USER_NAME = #directUserName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="finUserId">
			FIN_USER_ID = #finUserId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="finUserName">
			FIN_USER_NAME = #finUserName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customCompany">
			CUSTOM_COMPANY = #customCompany#
		</isNotEmpty>
	</sql>

	<select id="duplication" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imc.xb.ai.domain.XBAI16">
		SELECT
				UUID	as "uuid",  <!-- ID -->
				REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
				REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
				REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
				REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
				REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
				REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
				ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
				DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
				TENANT_USER	as "tenantUser",  <!-- 租户 -->
				UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
				SEG_NO	as "segNo",  <!-- 系统账套 -->
				DIRECT_USER_NUM	as "directUserNum",  <!-- 订货用户代码 -->
				DIRECT_USER_NAME	as "directUserName",  <!-- 订货用户名称 -->
				FIN_USER_ID	as "finUserId",  <!-- 最终用户代码 -->
				FIN_USER_NAME	as "finUserName" ,<!-- 最终用户名称 -->
				CUSTOM_COMPANY	as "customCompany"  <!-- 客户公司 -->
		FROM IAI.TXBAI16 WHERE
		CUSTOM_COMPANY = #customCompany#

		AND DIRECT_USER_NUM = #directUserNum#

		AND FIN_USER_ID = #finUserId#
		<isNotEmpty prepend=" AND " property="notContainUuid">
			UUID != #notContainUuid#
		</isNotEmpty>


		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
    		  $orderBy$
			</isNotEmpty>
		</dynamic>

	</select>

	<select id="query" parameterClass="java.util.HashMap"
			resultClass="com.baosight.imc.xb.ai.domain.XBAI16">
		SELECT
		UUID	as "uuid",  <!-- ID -->
		REC_CREATOR	as "recCreator",  <!-- 记录创建人 -->
		REC_CREATOR_NAME	as "recCreatorName",  <!-- 记录创建人姓名 -->
		REC_CREATE_TIME	as "recCreateTime",  <!-- 记录创建时间 -->
		REC_REVISOR	as "recRevisor",  <!-- 记录修改人 -->
		REC_REVISOR_NAME	as "recRevisorName",  <!-- 记录修改人姓名 -->
		REC_REVISE_TIME	as "recReviseTime",  <!-- 记录修改时间 -->
		ARCHIVE_FLAG	as "archiveFlag",  <!-- 归档标记 -->
		DEL_FLAG	as "delFlag",  <!-- 记录删除标记 -->
		TENANT_USER	as "tenantUser",  <!-- 租户 -->
		UNIT_CODE	as "unitCode",  <!-- 业务单元代码 -->
		SEG_NO	as "segNo",  <!-- 系统账套 -->
		DIRECT_USER_NUM	as "directUserNum",  <!-- 订货用户代码 -->
		DIRECT_USER_NAME	as "directUserName",  <!-- 订货用户名称 -->
		FIN_USER_ID	as "finUserId",  <!-- 最终用户代码 -->
		FIN_USER_NAME	as "finUserName" ,<!-- 最终用户名称 -->
		CUSTOM_COMPANY	as "customCompany"  <!-- 客户公司 -->
		FROM IAI.TXBAI16 WHERE 1=1
		<include refid="condition" />
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
		</dynamic>

	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM IAI.TXBAI16 WHERE 1=1
    <include refid="condition" />
	</select>

	<!--
		<isNotEmpty prepend=" AND " property="uuid">
			UUID = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreator">
			REC_CREATOR = #recCreator#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreatorName">
			REC_CREATOR_NAME = #recCreatorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recCreateTime">
			REC_CREATE_TIME = #recCreateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisor">
			REC_REVISOR = #recRevisor#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recRevisorName">
			REC_REVISOR_NAME = #recRevisorName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recReviseTime">
			REC_REVISE_TIME = #recReviseTime#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="archiveFlag">
			ARCHIVE_FLAG = #archiveFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delFlag">
			DEL_FLAG = #delFlag#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="tenantUser">
			TENANT_USER = #tenantUser#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="unitCode">
			UNIT_CODE = #unitCode#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="segNo">
			SEG_NO = #segNo#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="directUserNum">
			DIRECT_USER_NUM = #directUserNum#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="directUserName">
			DIRECT_USER_NAME = #directUserName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="finUserId">
			FIN_USER_ID = #finUserId#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="finUserName">
			FIN_USER_NAME = #finUserName#
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="customCompany">
			CUSTOM_COMPANY = #customCompany#
		</isNotEmpty>
	-->

	<insert id="insert">
		INSERT INTO IAI.TXBAI16 (UUID,  <!-- ID -->
										REC_CREATOR,  <!-- 记录创建人 -->
										REC_CREATOR_NAME,  <!-- 记录创建人姓名 -->
										REC_CREATE_TIME,  <!-- 记录创建时间 -->
										REC_REVISOR,  <!-- 记录修改人 -->
										REC_REVISOR_NAME,  <!-- 记录修改人姓名 -->
										REC_REVISE_TIME,  <!-- 记录修改时间 -->
										ARCHIVE_FLAG,  <!-- 归档标记 -->
										DEL_FLAG,  <!-- 记录删除标记 -->
										TENANT_USER,  <!-- 租户 -->
										UNIT_CODE,  <!-- 业务单元代码 -->
										SEG_NO,  <!-- 系统账套 -->
										DIRECT_USER_NUM,  <!-- 订货用户代码 -->
										DIRECT_USER_NAME,  <!-- 订货用户名称 -->
										FIN_USER_ID,  <!-- 最终用户代码 -->
										FIN_USER_NAME,  <!-- 最终用户名称 -->
										CUSTOM_COMPANY
										)
	    VALUES (#uuid:VARCHAR#, #recCreator:VARCHAR#, #recCreatorName:VARCHAR#
		, #recCreateTime:VARCHAR#, #recRevisor:VARCHAR#, #recRevisorName:VARCHAR#
		, #recReviseTime:VARCHAR#, #archiveFlag:VARCHAR#, #delFlag#, #tenantUser:VARCHAR#
		, #unitCode:VARCHAR#, #segNo:VARCHAR#, #directUserNum:VARCHAR#, #directUserName:VARCHAR#
		, #finUserId:VARCHAR#, #finUserName:VARCHAR#, #customCompany:VARCHAR#)
	</insert>

	<delete id="delete">
		DELETE FROM IAI.TXBAI16 WHERE UUID	= #uuid#
	</delete>

	<update id="update">
		UPDATE IAI.TXBAI16
		SET

					REC_REVISOR	= #recRevisor#,   <!-- 记录修改人 -->
					REC_REVISOR_NAME	= #recRevisorName#,   <!-- 记录修改人姓名 -->
					REC_REVISE_TIME	= #recReviseTime#,   <!-- 记录修改时间 -->
					UNIT_CODE	= #unitCode#,   <!-- 业务单元代码 -->
					SEG_NO	= #segNo#,   <!-- 系统账套 -->
					DIRECT_USER_NUM	= #directUserNum#,   <!-- 订货用户代码 -->
					DIRECT_USER_NAME	= #directUserName#,   <!-- 订货用户名称 -->
					FIN_USER_ID	= #finUserId#,   <!-- 最终用户代码 -->
					FIN_USER_NAME	= #finUserName# , <!-- 最终用户名称 -->
					CUSTOM_COMPANY	= #customCompany#   <!-- 走访公司 -->
			WHERE
		UUID	= #uuid#
	</update>

</sqlMap>

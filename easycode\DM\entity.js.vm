## 引入宏定义
$!{baowu.vm}

## 使用宏定义设置回调（保存位置与文件）
$!callback.setSavePath($baowuViewDir)
$!callback.setFileName($entityName+".js")

$(function () {

    $('#QUERY').on('click', function (e) {
        resultGrid.dataSource.page(1);
    });
    //当前修改行
    var editRow =0;

    IPLATUI.EFGrid = {
        "result": {
            pageable: {
                pageSize: 10,
                pageSizes: [10, 20, 50, 100, 500]
            },
            // 列是否可以拖动切换位置
            reorderable: true,
            columns: [],
            beforeEdit: function (e) {
                editRow = e.row;
                // 获取当前修改行
            #foreach($column in $tableInfo.fullColumn)
                // if(e.field === "${column.name}") { ${foreach.index};}
            #end
            },
            afterEdit: function (e) {
                // 执行自定义逻辑代码，假设根据逻辑要求不关闭单元格编辑状态
            #foreach($column in $tableInfo.fullColumn)
                //if (e.model["${column.name}"] === "") {
                //    e.model.set("${column.name}", "");
                //}
            #end
            },
            /**
             * 双击数据行时触发的事件，注意编辑状态时不会触发
             * @param e
             * e.sender     kendoGrid对象，resultGrid
             * e.model      双击的行数据，kendo.data.Model
             * e.row        当前行的行号
             * e.tr         行的tr,包括固定列和数据列 jquery对象
             */
            onRowDblClick: function (e) {
                let dataItems = resultGrid.getDataItems();
                var model = dataItems[e.row];
            #foreach($column in $tableInfo.fullColumn)
                //model.set("${column.name}", "");
            #end

                //var popupGridWindow = $("#querynum").data("kendoWindow");
                //popupGridWindow.close();
            }
        },
    }
});
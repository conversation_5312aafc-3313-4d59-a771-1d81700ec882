import asyncio
import json
from crawl4ai import <PERSON><PERSON><PERSON><PERSON><PERSON>raw<PERSON>, CrawlerRunConfig, CacheMode,BrowserConfig
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
async def main():
    cookies= [ {"name": "JSESSIONID", "value": "1565A5EA7238B7D3074E7E9750588D83", "url": "https://confluence.baocloud.cn/"}
              ]
           
    browserconfig=BrowserConfig(cookies=cookies)
    
    async with AsyncWebCrawler(config=browserconfig) as crawler:
        
        result = await crawler.arun(
            url="https://confluence.baocloud.cn/display/ali",
            
            config=CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                            

                # extraction_strategy=JsonCssExtractionStrategy(schema)
            )
        )
        # data = json.loads(result.extracted_content)
        print(result.cleaned_html)
if __name__ == "__main__":
    asyncio.run(main())
    
    
    
    
    

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版测试脚本，用于验证WebSocket连接和消息发送
"""

import websocket
import json
import time
import threading
from queue import Queue

class SimpleFlameAPITester:
    def __init__(self):
        self.ws_url = "ws://10.81.7.91:30009/openapi/flames/api/v2/chat"
        self.response_queue = Queue()
        self.connected = False
        
    def on_message(self, ws, message):
        """消息处理回调"""
        print(f"收到消息: {message}")
        try:
            data = json.loads(message)
            self.response_queue.put(("SUCCESS", data))
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            self.response_queue.put(("ERROR", f"JSON解析失败: {message}"))
    
    def on_error(self, ws, error):
        """错误处理"""
        print(f"WebSocket错误: {error}")
        self.response_queue.put(("ERROR", str(error)))
    
    def on_open(self, ws):
        """连接建立回调"""
        print("WebSocket连接已建立")
        self.connected = True
    
    def on_close(self, ws, close_status_code, close_msg):
        """连接关闭处理"""
        print(f"连接已关闭 - 状态码: {close_status_code}, 消息: {close_msg}")
        self.connected = False
    
    def build_test_payload(self):
        """构建测试payload"""
        return {
            "header": {
                "traceId": f"TEST-{int(time.time())}",
                "bodyId": "xzrbcess5ht7xw5o2l36x5px9owbscou",
                "appId": "9BC895C708EB440D804C",
                "mode": 0
            },
            "parameter": {},
            "payload": {
                "sessionId": "TEST_SESSION_001",
                "text": [{
                    "content": "开户申请",
                    "content_type": "text",
                    "role": "user"
                }]
            }
        }
    
    def test_connection(self):
        """测试连接和消息发送"""
        print("开始测试WebSocket连接...")
        
        # 创建WebSocket连接
        headers = {
            "User-Agent": "SimpleFlameAPITester/1.0"
        }
        
        ws = websocket.WebSocketApp(
            self.ws_url,
            header=headers,
            on_message=self.on_message,
            on_error=self.on_error,
            on_open=self.on_open,
            on_close=self.on_close
        )
        
        # 启动WebSocket连接线程
        wst = threading.Thread(target=ws.run_forever)
        wst.daemon = True
        wst.start()
        
        # 等待连接建立
        print("等待连接建立...")
        timeout = 10
        start_time = time.time()
        
        while not self.connected and time.time() - start_time < timeout:
            time.sleep(0.1)
        
        if not self.connected:
            print("连接建立失败")
            return False
        
        print("连接建立成功，发送测试消息...")
        
        # 发送测试消息
        try:
            payload = self.build_test_payload()
            message = json.dumps(payload, ensure_ascii=False)
            print(f"发送消息: {message}")
            ws.send(message)
        except Exception as e:
            print(f"发送消息失败: {e}")
            return False
        
        # 等待响应
        print("等待服务器响应...")
        response_timeout = 30
        start_time = time.time()
        
        while time.time() - start_time < response_timeout:
            if not self.response_queue.empty():
                response_type, response_data = self.response_queue.get()
                
                print(f"响应类型: {response_type}")
                print(f"响应数据: {response_data}")
                
                if response_type == "SUCCESS":
                    print("✓ 测试成功！收到有效响应")
                    ws.close()
                    return True
                elif response_type == "ERROR":
                    print(f"✗ 测试失败：{response_data}")
                    ws.close()
                    return False
            
            time.sleep(0.1)
        
        print("✗ 测试超时，未收到响应")
        ws.close()
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("简化版Flame API测试")
    print("=" * 50)
    
    tester = SimpleFlameAPITester()
    
    try:
        success = tester.test_connection()
        
        if success:
            print("\n🎉 测试通过！WebSocket连接和消息发送正常")
        else:
            print("\n❌ 测试失败！请检查:")
            print("1. 网络连接是否正常")
            print("2. 服务器地址是否正确")
            print("3. 认证信息是否有效")
            print("4. 服务器是否正在运行")
            
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

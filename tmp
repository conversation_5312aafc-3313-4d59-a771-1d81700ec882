{"efFormEname": "CPBI0104", "efFormCname": "人员信息查询弹框", "efFormPopup": "", "efFormTime": "", "efCurFormEname": "CPBI0104", "efCurButtonEname": "", "packageName": "", "serviceName": "CPBI0104", "methodName": "initLoad", "efFormInfoTag": "", "efFormLoadPath": "/CP/BI/CPBI0104.jsp", "efFormButtonDesc": "{'__sys__':{'msg':'','traceId':'','detailMsg':'','msgKey':'','status':0},'__version__':'2.0','__blocks__':{'INQU':{'meta':{'columns':[{'pos':0,'name':'button_ename','descName':'按钮英文名','primaryKey':true},{'pos':1,'name':'button_cname','descName':'按钮中文'},{'pos':2,'name':'button_status','descName':' '},{'pos':3,'name':'button_desc','descName':'按钮描述'},{'pos':4,'name':'uri','descName':'资源地址'},{'pos':5,'name':'layout','descName':'样式'},{'pos':6,'name':'position','descName':'位置'},{'pos':7,'name':'rec_create_time','descName':' '},{'pos':8,'name':'rec_revisor','descName':' '},{'pos':9,'name':'region_id','descName':' '},{'pos':10,'name':'is_auth','descName':' '},{'pos':11,'name':'form_ename','descName':' '},{'pos':12,'name':'node_sort_id','descName':' '},{'pos':13,'name':'archive_flag','descName':' '},{'pos':14,'name':'rec_revise_time','descName':' '},{'pos':15,'name':'rec_creator','descName':' '}]},'rows':[['QUERY','查询','1',' ','css:fa fa-search','3','0','20221009192933','R00579','INQU','0','CPBI0104',' ',' ','20221009192954','R00579']]}}} ", "__$$DIAGNOSE$$__": "", "efSecurityToken": "144bad7a175003899135900005f9d", "COOKIE": "8db7302d-0e0a-43b2-88d6-3b1a673a1c36", "__version__": "2.0", "__sys__": {"name": "", "descName": "", "msg": "", "msgKey": "", "detailMsg": "", "status": 0, "traceId": ""}, "__blocks__": {"inqu_status": {"attr": {}, "meta": {"desc": "", "attr": {}, "columns": [{"pos": 0, "name": "userId"}, {"pos": 1, "name": "userName"}]}, "rows": [["YG0852", ""]]}, "result": {"attr": {"showCount": "false", "offset": 0, "limit": 10, "count": 0, "orderBy": ""}, "meta": {"desc": "", "attr": {}, "columns": [{"pos": 0, "name": "segNo", "descName": " "}, {"pos": 1, "name": "segFullName", "descName": " "}, {"pos": 2, "name": "mobile", "descName": " "}, {"pos": 3, "name": "userName", "descName": " "}, {"pos": 4, "name": "userId", "descName": " "}, {"pos": 5, "name": "email", "descName": " "}]}, "rows": []}}}
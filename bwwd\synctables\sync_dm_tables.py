import dmPython
import csv
from datetime import datetime, timedelta
import os

# 数据库连接配置

#测试环境
# DB_CONFIG = {
#     "user": "iplat4j",
#     "password": "dameng123",
#     "server": "**********",
#     "port": 5236
#     # "charset": "GBK"
# }

#生成环境
DB_CONFIG = {
    "user": "iplat4j",
    "password": "dameng123",
    "server": "**********",
    "port": 52025
    # "charset": "GBK"
}
# 导出配置
EXPORT_CONFIG = {
    "tables": [
"IAI.TXBAI04",
"IAI.TXBAI05",
"IAI.TXBAI06",
"IAI.TXBAI07",
"IAI.TXBAI08",
"IAI.TXBAI11",
"IAI.TXBAI12",
"IAI.TXBAI13",
"IAI.TXBAI14",
"IAI.TXBAI09",
"IAI.TXBAI10"
],  # 需导出的表名列表
    "time_field": [
        "DATA_TIME",
        "DATA_TIME",
        "DATA_TIME",
        "DATA_TIME",
        "DATA_TIME",
        "DATA_TIME",
        "DATA_TIME",
        "DATA_TIME",
        "DATA_TIME",
        "DATA_TIME_START",
        "DATA_TIME_START"
        ],      # 时间字段名
    "days": 50,                        # 导出最近N天数据
    "output_dir": r"C:\vs_project\test\bwwd\synctables\sql"      # SQL文件输出目录
}



def export_recent_data():
    # 计算时间范围
    end_date = datetime.now()
    start_date = end_date - timedelta(days=EXPORT_CONFIG["days"])
    # time_condition = f"{EXPORT_CONFIG['time_field']} >= '{start_date.strftime('%Y-%m-%d %H:%M:%S')}'"

    # 连接数据库
    conn = dmPython.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    
    sqls=delete_sql=""
    try:
        for idx, table in enumerate(EXPORT_CONFIG["tables"]):
            print(f"[{idx}/{len(EXPORT_CONFIG['tables'])}] 正在导出表: {table}")
            time_condition = f"{EXPORT_CONFIG['time_field'][idx]} >= '{start_date.strftime('%Y%m%d')}'"
            # 查询数据
            query = f"SELECT * FROM {table} WHERE {time_condition}"
            print(query)
            cursor.execute(query)
            rows = cursor.fetchall()

            if not rows:
                print(f"表 {table} 无符合条件的数据")
                continue
            #生成删除语句
            delete_sql += f"DELETE FROM {table} WHERE {time_condition};\n"

            # 生成 INSERT 语句
            columns = [desc[0] for desc in cursor.description]
            placeholders = ", ".join(["%s"] * len(columns))
            sql_template = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({placeholders});\n"

            # 写入文件
            file_path = f"{EXPORT_CONFIG['output_dir']}/{table}_{start_date.strftime('%Y%m%d')}.sql"
            if(os.path.exists(file_path)):
                os.remove(file_path)
            with open(file_path, "w", encoding="utf-8") as f:
                for row in rows:
                    values = []
                    for value in row:
                        if isinstance(value, str):
                            values.append("'{0}'".format(value.replace("'", "''")))  # 转义单引号

                        else:
                            values.append(str(value))
                    sql=sql_template % tuple(values)
                    f.write(sql)
                    sqls+=sql

            print(f"表 {table} 数据已导出至 {file_path}")
        sql_file_path = f"{EXPORT_CONFIG['output_dir']}/all_sql.sql"
        with open(sql_file_path, "w", encoding="utf-8") as f:
            f.write(delete_sql)
            f.write(sqls)
    finally:
        cursor.close()
        conn.close()
        
        
def export_all_tables():
    conn = dmPython.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    try:
        cursor.execute("SELECT TABLE_NAME FROM ALL_TABLES WHERE OWNER='SYSDBA'")
        tables = [row[0] for row in cursor.fetchall()]
        EXPORT_CONFIG["tables"] = tables
        export_recent_data()
    finally:
        cursor.close()
        conn.close()
        
export_recent_data()
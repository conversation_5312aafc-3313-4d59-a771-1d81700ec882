document.addEventListener('DOMContentLoaded', () => {
    // Canvas setup
    const canvas = document.getElementById('tetris');
    const ctx = canvas.getContext('2d');
    const nextPieceCanvas = document.getElementById('next-piece');
    const nextPieceCtx = nextPieceCanvas.getContext('2d');
    
    // Game constants
    const BLOCK_SIZE = 20;
    const BOARD_WIDTH = 12;
    const BOARD_HEIGHT = 20;
    const COLORS = [
        null,
        '#FF0D72', // I
        '#0DC2FF', // J
        '#0DFF72', // L
        '#F538FF', // O
        '#FF8E0D', // S
        '#FFE138', // T
        '#3877FF'  // Z
    ];

    // Tetromino shapes
    const PIECES = [
        [
            [0, 0, 0, 0],
            [1, 1, 1, 1],
            [0, 0, 0, 0],
            [0, 0, 0, 0]
        ],
        [
            [2, 0, 0],
            [2, 2, 2],
            [0, 0, 0]
        ],
        [
            [0, 0, 3],
            [3, 3, 3],
            [0, 0, 0]
        ],
        [
            [4, 4],
            [4, 4]
        ],
        [
            [0, 5, 5],
            [5, 5, 0],
            [0, 0, 0]
        ],
        [
            [0, 6, 0],
            [6, 6, 6],
            [0, 0, 0]
        ],
        [
            [7, 7, 0],
            [0, 7, 7],
            [0, 0, 0]
        ]
    ];

    // Game variables
    let board = createBoard();
    let score = 0;
    let lines = 0;
    let level = 1;
    let dropInterval = 1000; // milliseconds
    let lastTime = 0;
    let dropCounter = 0;
    let gameOver = false;
    let paused = false;
    let requestId = null;

    // Player object
    const player = {
        pos: { x: 0, y: 0 },
        piece: null,
        score: 0
    };

    // Next piece
    let nextPiece = createPiece(Math.floor(Math.random() * PIECES.length));

    // DOM elements
    const scoreElement = document.getElementById('score');
    const linesElement = document.getElementById('lines');
    const levelElement = document.getElementById('level');
    const startButton = document.getElementById('start-button');

    // Event listeners
    document.addEventListener('keydown', handleKeyPress);
    startButton.addEventListener('click', toggleGame);

    // Create a new game board
    function createBoard() {
        return Array.from({ length: BOARD_HEIGHT }, () => Array(BOARD_WIDTH).fill(0));
    }

    // Create a new tetromino piece
    function createPiece(type) {
        return PIECES[type];
    }

    // Draw a single block
    function drawBlock(x, y, color, context = ctx) {
        context.fillStyle = color;
        context.fillRect(x * BLOCK_SIZE, y * BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
        context.strokeStyle = '#fff';
        context.strokeRect(x * BLOCK_SIZE, y * BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
    }

    // Draw the game board
    function drawBoard() {
        board.forEach((row, y) => {
            row.forEach((value, x) => {
                if (value !== 0) {
                    drawBlock(x, y, COLORS[value]);
                }
            });
        });
    }

    // Draw the current piece
    function drawPiece(piece, offset, context = ctx) {
        piece.forEach((row, y) => {
            row.forEach((value, x) => {
                if (value !== 0) {
                    drawBlock(
                        x + offset.x,
                        y + offset.y,
                        COLORS[value],
                        context
                    );
                }
            });
        });
    }

    // Draw the next piece preview
    function drawNextPiece() {
        nextPieceCtx.clearRect(0, 0, nextPieceCanvas.width, nextPieceCanvas.height);
        nextPieceCtx.fillStyle = '#111';
        nextPieceCtx.fillRect(0, 0, nextPieceCanvas.width, nextPieceCanvas.height);
        
        const offset = {
            x: Math.floor((5 - nextPiece[0].length) / 2),
            y: Math.floor((5 - nextPiece.length) / 2)
        };
        
        drawPiece(nextPiece, offset, nextPieceCtx);
    }

    // Draw everything
    function draw() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        drawBoard();
        drawPiece(player.piece, player.pos);
        drawNextPiece();
    }

    // Check for collision
    function collide() {
        const [piece, pos] = [player.piece, player.pos];
        for (let y = 0; y < piece.length; y++) {
            for (let x = 0; x < piece[y].length; x++) {
                if (piece[y][x] !== 0 &&
                    (board[y + pos.y] === undefined ||
                     board[y + pos.y][x + pos.x] === undefined ||
                     board[y + pos.y][x + pos.x] !== 0)) {
                    return true;
                }
            }
        }
        return false;
    }

    // Merge the piece with the board
    function merge() {
        player.piece.forEach((row, y) => {
            row.forEach((value, x) => {
                if (value !== 0) {
                    board[y + player.pos.y][x + player.pos.x] = value;
                }
            });
        });
    }

    // Reset player position and get new piece
    function resetPlayer() {
        player.piece = nextPiece;
        nextPiece = createPiece(Math.floor(Math.random() * PIECES.length));
        player.pos.y = 0;
        player.pos.x = Math.floor((BOARD_WIDTH - player.piece[0].length) / 2);
        
        // Check if game is over
        if (collide()) {
            gameOver = true;
            cancelAnimationFrame(requestId);
            requestId = null;
            alert('Game Over! Your score: ' + score);
        }
    }

    // Move the piece
    function playerMove(dir) {
        player.pos.x += dir;
        if (collide()) {
            player.pos.x -= dir;
        }
    }

    // Drop the piece
    function playerDrop() {
        player.pos.y++;
        if (collide()) {
            player.pos.y--;
            merge();
            resetPlayer();
            clearLines();
            updateScore();
        }
        dropCounter = 0;
    }

    // Hard drop (drop piece all the way down)
    function playerHardDrop() {
        while (!collide()) {
            player.pos.y++;
        }
        player.pos.y--;
        merge();
        resetPlayer();
        clearLines();
        updateScore();
        dropCounter = 0;
    }

    // Rotate the piece
    function playerRotate() {
        const pos = player.pos.x;
        let offset = 1;
        rotate();
        
        // Handle collision during rotation
        while (collide()) {
            player.pos.x += offset;
            offset = -(offset + (offset > 0 ? 1 : -1));
            if (offset > player.piece[0].length) {
                rotate(-1);
                player.pos.x = pos;
                return;
            }
        }
    }

    // Rotate matrix
    function rotate(dir = 1) {
        const piece = player.piece;
        // Transpose matrix
        for (let y = 0; y < piece.length; y++) {
            for (let x = 0; x < y; x++) {
                [piece[x][y], piece[y][x]] = [piece[y][x], piece[x][y]];
            }
        }
        
        // Reverse rows or columns based on direction
        if (dir > 0) {
            piece.forEach(row => row.reverse());
        } else {
            piece.reverse();
        }
    }

    // Clear completed lines
    function clearLines() {
        let linesCleared = 0;
        
        outer: for (let y = BOARD_HEIGHT - 1; y >= 0; y--) {
            for (let x = 0; x < BOARD_WIDTH; x++) {
                if (board[y][x] === 0) {
                    continue outer;
                }
            }
            
            // Remove the line and add empty line at top
            const row = board.splice(y, 1)[0].fill(0);
            board.unshift(row);
            y++;
            linesCleared++;
        }
        
        // Update lines and level
        if (linesCleared > 0) {
            lines += linesCleared;
            linesElement.textContent = lines;
            
            // Level up every 10 lines
            const newLevel = Math.floor(lines / 10) + 1;
            if (newLevel > level) {
                level = newLevel;
                levelElement.textContent = level;
                // Increase speed
                dropInterval = Math.max(100, 1000 - (level - 1) * 100);
            }
            
            // Calculate score based on lines cleared and level
            const linePoints = [0, 40, 100, 300, 1200];
            score += linePoints[linesCleared] * level;
            scoreElement.textContent = score;
        }
    }

    // Update score display
    function updateScore() {
        scoreElement.textContent = score;
    }

    // Handle keyboard input
    function handleKeyPress(e) {
        if (gameOver || paused) return;
        
        switch (e.keyCode) {
            case 37: // Left arrow
                playerMove(-1);
                break;
            case 39: // Right arrow
                playerMove(1);
                break;
            case 40: // Down arrow
                playerDrop();
                break;
            case 38: // Up arrow
                playerRotate();
                break;
            case 32: // Space
                playerHardDrop();
                break;
            case 80: // P key
                toggleGame();
                break;
        }
    }

    // Toggle game pause
    function toggleGame() {
        if (!requestId) {
            // Start game if not running
            gameOver = false;
            paused = false;
            startButton.textContent = 'Pause';
            resetGame();
            update();
        } else {
            // Toggle pause
            paused = !paused;
            startButton.textContent = paused ? 'Resume' : 'Pause';
            if (!paused) {
                lastTime = 0;
                update();
            } else {
                cancelAnimationFrame(requestId);
                requestId = null;
                
                // Draw "PAUSED" text
                ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                // ctx.font = '30px Arial';
                ctx.fillStyle = 'white';
                ctx.textAlign = 'center';
                ctx.fillText('PAUSED', canvas.width / 2, canvas.height / 2);
            }
        }
    }

    // Reset the game
    function resetGame() {
        board = createBoard();
        score = 0;
        lines = 0;
        level = 1;
        dropInterval = 1000;
        scoreElement.textContent = score;
        linesElement.textContent = lines;
        levelElement.textContent = level;
        nextPiece = createPiece(Math.floor(Math.random() * PIECES.length));
        resetPlayer();
    }

    // Game update loop
    function update(time = 0) {
        if (paused) return;
        
        const deltaTime = time - lastTime;
        lastTime = time;
        
        dropCounter += deltaTime;
        if (dropCounter > dropInterval) {
            playerDrop();
        }
        
        draw();
        requestId = requestAnimationFrame(update);
    }

    // Initialize game
    resetPlayer();
    draw();
});

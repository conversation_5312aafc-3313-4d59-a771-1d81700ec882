<?xml version="1.0" ?>
<testsuite name="FlameAPITester-20250611150308" tests="1" time="2.006" failures="0" errors="1">
	<testcase classname="FlameAPITester" name="test_planning_agent" time="2.006">
		<error type="AssertionError" message="测试过程中发生错误: [('ERROR', 'WebSocket错误: Handshake status 403 Forbidden -+-+- {\'server\': \'nginx/1.21.5\', \'date\': \'Wed, 11 Jun 2025 07:03:08 GMT\', \'content-type\': \'application/json;charset=UTF-8\', \'content-length\': \'90\', \'connection\': \'keep-alive\', \'cache-control\': \'no-cache, no-store, max-age=0, must-revalidate\', \'pragma\': \'no-cache\', \'expires\': \'0\', \'x-content-type-options\': \'nosniff\', \'x-frame-options\': \'DENY\', \'x-xss-protection\': \'1 ; mode=block\', \'referrer-policy\': \'no-referrer\'} -+-+- b\'{&quot;code&quot;:403,&quot;data&quot;:null,&quot;message&quot;:&quot;\\xe7\\xad\\xbe\\xe5\\x90\\x8d\\xe5\\x8f\\x82\\xe6\\x95\\xb0\\xe8\\xa7\\xa3\\xe6\\x9e\\x90\\xe5\\xa4\\xb1\\xe8\\xb4\\xa5(\\xe4\\xb8\\x8d\\xe8\\xaf\\x86\\xe5\\x88\\xab\\xe7\\x9a\\x84 headers \\xe5\\x8f\\x82\\xe6\\x95\\xb0)&quot;}\'')]"><![CDATA[Traceback (most recent call last):
  File "c:\vs_project\test\智能开户\test\test2.py", line 287, in test_planning_agent
    self.fail(f"测试过程中发生错误: {all_responses}")
AssertionError: 测试过程中发生错误: [('ERROR', 'WebSocket错误: Handshake status 403 Forbidden -+-+- {\'server\': \'nginx/1.21.5\', \'date\': \'Wed, 11 Jun 2025 07:03:08 GMT\', \'content-type\': \'application/json;charset=UTF-8\', \'content-length\': \'90\', \'connection\': \'keep-alive\', \'cache-control\': \'no-cache, no-store, max-age=0, must-revalidate\', \'pragma\': \'no-cache\', \'expires\': \'0\', \'x-content-type-options\': \'nosniff\', \'x-frame-options\': \'DENY\', \'x-xss-protection\': \'1 ; mode=block\', \'referrer-policy\': \'no-referrer\'} -+-+- b\'{"code":403,"data":null,"message":"\\xe7\\xad\\xbe\\xe5\\x90\\x8d\\xe5\\x8f\\x82\\xe6\\x95\\xb0\\xe8\\xa7\\xa3\\xe6\\x9e\\x90\\xe5\\xa4\\xb1\\xe8\\xb4\\xa5(\\xe4\\xb8\\x8d\\xe8\\xaf\\x86\\xe5\\x88\\xab\\xe7\\x9a\\x84 headers \\xe5\\x8f\\x82\\xe6\\x95\\xb0)"}\'')]
]]></error>
	</testcase>
	<system-out><![CDATA[开始测试规划型智能体...
构建的payload: {
  "header": {
    "traceId": "TRACE-1749625388-2bd6560f",
    "bodyId": "xzrbcess5ht7xw5o2l36x5px9owbscou",
    "appId": "9BC895C708EB440D804C",
    "mode": 0
  },
  "parameter": {},
  "payload": {
    "sessionId": "SESSION_001",
    "text": [
      {
        "content": "开户申请",
        "content_type": "text",
        "role": "user"
      }
    ]
  }
}
signed_str=host: **********
date: Wed, 11 Jun 2025 07:03:08 GMT
GET /openapi/flames/api/v2/chat HTTP/1.1
------------ws.url---http://**********:30009/openapi/flames/api/v2/chat?host=**********&date=Wed%2C+11+Jun+2025+07%3A03%3A08+GMT&authorization=aG1hYyBhcGlfa2V5PSI5QkM4OTVDNzA4RUI0NDBEODA0QyIsYWxnb3JpdGhtPSJobWFjLXNoYTI1NiIsaGVhZGVycz0iaG9zdCUyMGRhdGUlMjByZXF1ZXN0LWxpbmUiLHNpZ25hdHVyZT0iMlFIMDRWdXljNExaaGJ0dXg2dHdhVW4ycjFhQlpPNnE1UVA5RyUyQll2a2hBJTNEIg%3D%3D&bodyId=xzrbcess5ht7xw5o2l36x5px9owbscou
------------ws.head---[]
WebSocket错误: Handshake status 403 Forbidden -+-+- {'server': 'nginx/1.21.5', 'date': 'Wed, 11 Jun 2025 07:03:08 GMT', 'content-type': 'application/json;charset=UTF-8', 'content-length': '90', 'connection': 'keep-alive', 'cache-control': 'no-cache, no-store, max-age=0, must-revalidate', 'pragma': 'no-cache', 'expires': '0', 'x-content-type-options': 'nosniff', 'x-frame-options': 'DENY', 'x-xss-protection': '1 ; mode=block', 'referrer-policy': 'no-referrer'} -+-+- b'{"code":403,"data":null,"message":"\xe7\xad\xbe\xe5\x90\x8d\xe5\x8f\x82\xe6\x95\xb0\xe8\xa7\xa3\xe6\x9e\x90\xe5\xa4\xb1\xe8\xb4\xa5(\xe4\xb8\x8d\xe8\xaf\x86\xe5\x88\xab\xe7\x9a\x84 headers \xe5\x8f\x82\xe6\x95\xb0)"}'
错误类型: <class 'websocket._exceptions.WebSocketBadStatusException'>
错误状态码: {'code': 403, 'data': None, 'message': '签名参数解析失败(不识别的 headers 参数)'}
### 连接已关闭 ### 状态码: None, 消息: None
等待服务器响应...
收到响应: ('ERROR', 'WebSocket错误: Handshake status 403 Forbidden -+-+- {\'server\': \'nginx/1.21.5\', \'date\': \'Wed, 11 Jun 2025 07:03:08 GMT\', \'content-type\': \'application/json;charset=UTF-8\', \'content-length\': \'90\', \'connection\': \'keep-alive\', \'cache-control\': \'no-cache, no-store, max-age=0, must-revalidate\', \'pragma\': \'no-cache\', \'expires\': \'0\', \'x-content-type-options\': \'nosniff\', \'x-frame-options\': \'DENY\', \'x-xss-protection\': \'1 ; mode=block\', \'referrer-policy\': \'no-referrer\'} -+-+- b\'{"code":403,"data":null,"message":"\\xe7\\xad\\xbe\\xe5\\x90\\x8d\\xe5\\x8f\\x82\\xe6\\x95\\xb0\\xe8\\xa7\\xa3\\xe6\\x9e\\x90\\xe5\\xa4\\xb1\\xe8\\xb4\\xa5(\\xe4\\xb8\\x8d\\xe8\\xaf\\x86\\xe5\\x88\\xab\\xe7\\x9a\\x84 headers \\xe5\\x8f\\x82\\xe6\\x95\\xb0)"}\'')
====================错误==================
错误详情: WebSocket错误: Handshake status 403 Forbidden -+-+- {'server': 'nginx/1.21.5', 'date': 'Wed, 11 Jun 2025 07:03:08 GMT', 'content-type': 'application/json;charset=UTF-8', 'content-length': '90', 'connection': 'keep-alive', 'cache-control': 'no-cache, no-store, max-age=0, must-revalidate', 'pragma': 'no-cache', 'expires': '0', 'x-content-type-options': 'nosniff', 'x-frame-options': 'DENY', 'x-xss-protection': '1 ; mode=block', 'referrer-policy': 'no-referrer'} -+-+- b'{"code":403,"data":null,"message":"\xe7\xad\xbe\xe5\x90\x8d\xe5\x8f\x82\xe6\x95\xb0\xe8\xa7\xa3\xe6\x9e\x90\xe5\xa4\xb1\xe8\xb4\xa5(\xe4\xb8\x8d\xe8\xaf\x86\xe5\x88\xab\xe7\x9a\x84 headers \xe5\x8f\x82\xe6\x95\xb0)"}'

测试结果:
- 总响应数: 1
- 是否有错误: True
- 是否有有效响应: False
- 测试用时: 0.00秒
- 所有响应: [('ERROR', 'WebSocket错误: Handshake status 403 Forbidden -+-+- {\'server\': \'nginx/1.21.5\', \'date\': \'Wed, 11 Jun 2025 07:03:08 GMT\', \'content-type\': \'application/json;charset=UTF-8\', \'content-length\': \'90\', \'connection\': \'keep-alive\', \'cache-control\': \'no-cache, no-store, max-age=0, must-revalidate\', \'pragma\': \'no-cache\', \'expires\': \'0\', \'x-content-type-options\': \'nosniff\', \'x-frame-options\': \'DENY\', \'x-xss-protection\': \'1 ; mode=block\', \'referrer-policy\': \'no-referrer\'} -+-+- b\'{"code":403,"data":null,"message":"\\xe7\\xad\\xbe\\xe5\\x90\\x8d\\xe5\\x8f\\x82\\xe6\\x95\\xb0\\xe8\\xa7\\xa3\\xe6\\x9e\\x90\\xe5\\xa4\\xb1\\xe8\\xb4\\xa5(\\xe4\\xb8\\x8d\\xe8\\xaf\\x86\\xe5\\x88\\xab\\xe7\\x9a\\x84 headers \\xe5\\x8f\\x82\\xe6\\x95\\xb0)"}\'')]
]]></system-out>
	<system-err><![CDATA[]]></system-err>
</testsuite>

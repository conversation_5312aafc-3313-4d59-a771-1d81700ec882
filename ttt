// Pre-request Script
const fs = require('fs');
const path = 'C:/vs_project/test/extracted_image_1.png';
// const imageUrl = "";

const imageUrl = 'file:///C:/vs_project/test/extracted_image_1.png';


let base64Data;

if (path) {
    try {
        const data = fs.readFileSync(path);
        base64Data = data.toString('base64');
    } catch (err) {
        console.error("Error reading file:", err);
    }
} else if (imageUrl) {
    pm.sendRequest(imageUrl, (err, response) => {
        if (!err && response.code === 200) {
            base64Data = response.stream.toString('base64');
        }
    });
}


if (base64Data) {
    console.log("base64 success");
    pm.environment.set("imageBase64",`data:image/jpg;base64,${base64Data}` );
    pm.request.body.update({mode:'raw',raw:`
    {
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "image_url",
          "image_url": {
            "url": "${imageBase64}"
          }
        },
        {
          "type": "text",
          "text": "解析文字，不要有其他内容"
        }
      ]
    }
  ],
  "model": "qwenVL7B"
}
    `})
} else {
    pm.test("Error", "No valid file path or image URL provided");
}


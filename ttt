// Pre-request Script
// Note: fs module is not available in Postman sandbox
// Using HTTP request to read local file instead

const imageUrl = 'file:///C:/vs_project/test/extracted_image_1.png';

// Use pm.sendRequest with callback to handle async operation
pm.sendRequest(imageUrl, (err, response) => {
    if (err) {
        console.error("Error reading file:", err);
        pm.test("File Read Error", () => {
            pm.expect.fail("Failed to read image file: " + err.message);
        });
        return;
    }

    if (response.code === 200) {
        // Convert response to base64
        const base64Data = response.stream.toString('base64');
        console.log("base64 conversion success");

        // Set environment variable
        const imageBase64 = `data:image/png;base64,${base64Data}`;
        pm.environment.set("imageBase64", imageBase64);

        // Update request body
        pm.request.body.update({
            mode: 'raw',
            raw: JSON.stringify({
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": imageBase64
                                }
                            },
                            {
                                "type": "text",
                                "text": "解析文字，不要有其他内容"
                            }
                        ]
                    }
                ],
                "model": "qwenVL7B"
            }, null, 2)
        });
    } else {
        console.error("Failed to read file, response code:", response.code);
        pm.test("File Read Error", () => {
            pm.expect.fail("Failed to read image file, response code: " + response.code);
        });
    }
});


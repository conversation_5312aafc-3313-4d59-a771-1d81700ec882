<?xml version="1.0" ?>
<testsuite name="FlameAPITester-20250611102833" tests="1" time="101.017" failures="0" errors="1">
	<testcase classname="FlameAPITester" name="test_planning_agent" time="101.017">
		<error type="AssertionError" message="False is not true"><![CDATA[Traceback (most recent call last):
  File "c:\vs_project\test\智能开户\test\test2.py", line 131, in test_planning_agent
AssertionError: False is not true
]]></error>
	</testcase>
	<system-out><![CDATA[### 连接已关闭 ###
Received response: ('ERROR', 'sequence item 7: expected str instance, int found')
]]></system-out>
	<system-err><![CDATA[]]></system-err>
</testsuite>

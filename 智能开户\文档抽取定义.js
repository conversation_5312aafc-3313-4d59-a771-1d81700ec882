
//开户登记表提取内容定义

// JavaScript 示例代码
function getExtracts() {
    var extracts = [
        "客户名称"
        , "开票地址"
        , "开票地址邮编"
        , "开票地址开票电话"
        , "开票地址传真"

        , "经营联系地址"
        , "经营联系地址邮编"
        , "经营联系地址电话"
        , "经营联系地址传真"

        , "开户银行名称"
        , "开户银行账号"
        , "开户银行银行账号"
        ,"开户银行所属省"

        , "注册资金"
        , "企业性质"

        , "统一社会信用代码"
        , "法人代表"
        , "单位所在省份及城市"

        , "联系人"
        , "联系人手机号码"

        , "是否自助打印质保书"
        , "是否开通电子商务平台账号"

        , "发票传递方式自取"
        , "发票传递方式自取发票人签名"
        , "发票传递方式邮寄"
        , "发票传递方式邮寄收件人"
        , "发票传递方式邮寄电话"
        , "发票传递方式数电票"
        , "发票传递方式数电票接收邮箱"
        , "发票传递方式数电票联系人电话"

        ,"委托代理人1姓名"
        ,"委托代理人1身份证号"
        // ,"委托代理人1授权期限"
        // ,"委托代理人1授权期限的开始日期"
        // ,"委托代理人1授权期限的结束日期"
        ,"委托代理人2姓名"
        ,"委托代理人2身份证号"
        // ,"委托代理人2授权期限"
        // ,"委托代理人2授权期限的开始日期"
        // ,"委托代理人2授权期限的结束日期"

    ];
    return extracts;
}

function getAdditionPrompt() {
    return "一，从委托代理人的授权期限分析出委托代理人授权期限的开始日期和结束日期，1.如果是授权日期是长期，则委托代理人授权期限的开始日期就是今天的日期，并且委托代理人授权期限的结束日期为20991231，2.如果授权期限中有两个日期，前面日期为委托代理人授权期限的开始日期，后面日期为委托代理人授权期限的结束日期，3.其他情况则委托代理人授权期限的开始日期为今天的日期，以委托代理人授权期限的开始日期计算出委托代理人授权期限的结束日期。授权日期格式为年份4位数字+月份2位数字+日2未数字，不足前面补0，比如20991201，需要严格按照此格式。在json结果中增加属性'委托代理人1授权期限的开始日期'，'委托代理人1授权期限的结束日期'，'委托代理人2授权期限的开始日期'，'委托代理人2授权期限的结束日期','委托代理人1授权期限'及从授权期限获取的值"
    +"\n二，注册资金要转换成万元，只能是数字，并且没有小数"
}
function execute(input) {

    return {
        extracts: getExtracts()
        , additionPrompt: getAdditionPrompt()
    };
}

// 营业执照提取内容定义
function execute(input) {
    
    var extracts= [
            "名称"
            ,"统一社会信用代码"
            ,"类型"
            ,"住所"
            ,"法定代表人"
            ,"住所省份"
            ,"注册资本"
            ,"成立日期"
            ,"营业期限"
            ,"经营范围"
 
        ];
  
    return {
        extracts: extracts
    };
}


//开票资料提取内容定义
function execute(input) {
    
    var extracts= [
            "开户银行"
            ,"开户银行账号"
            ,"电话"
            ,"地址"
            ,"开户银行所属省"
        ];
  
    return {
        extracts: extracts
    };
}


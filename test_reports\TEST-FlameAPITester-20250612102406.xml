<?xml version="1.0" ?>
<testsuite name="FlameAPITester-20250612102406" tests="1" time="2.003" failures="0" errors="0">
	<testcase classname="FlameAPITester" name="test_planning_agent" time="2.003"/>
	<system-out><![CDATA[发送消息: {"header": {"traceId": "TRACE-1749695046-372f0e23", "bodyId": "xzrbgent6ue6b0btaorwxi5phhbyld3d", "appId": "2741701DAF894C02BA78", "mode": 0}, "parameter": {}, "payload": {"sessionId": "SESSION_001", "text": [{"content": "重新为我开户，附件 TOSS_VjowMixCTjpmbGFtZXMsT046Y3pNNkx5OXdiR0Z1WlhSVWFXUXZjR3hoYm1WMEx6SXdNalV3TmpBMkwrVzhnT2FJdCtlWnUraXVzT2locUM1d1pHWT0=_TOSS ，TOSS_VjowMixCTjpmbGFtZXMsT046Y3pNNkx5OXdiR0Z1WlhSVWFXUXZjR3hoYm1WMEx6SXdNalV3TmpBMkwraVFwZVM0bXVhSnArZUZweTVxY0djPQ==_TOSS ,TOSS_VjowMixCTjpmbGFtZXMsT046Y3pNNkx5OXdiR0Z1WlhSVWFXUXZjR3hoYm1WMEx6SXdNalV3TlRBNEwrVzhnT2VscU9pMWhPYVdtUzV3Ym1jPQ==_TOSS", "content_type": "text", "role": "user"}]}}
收到原始消息: {"header":{"code":-1,"message":"Data Not Found","status":2,"sid":"TALKZVE59HN9MZLA3","traceId":"TRACE-1749695046-372f0e23"},"payload":{}}

测试结果:
- 总响应数: 1
- 是否有错误: False
- 是否有有效响应: True
- 测试用时: 0.00秒
- 所有响应: [{'header': {'code': -1, 'message': 'Data Not Found', 'status': 2, 'sid': 'TALKZVE59HN9MZLA3', 'traceId': 'TRACE-1749695046-372f0e23'}, 'payload': {}}]
]]></system-out>
	<system-err><![CDATA[]]></system-err>
</testsuite>

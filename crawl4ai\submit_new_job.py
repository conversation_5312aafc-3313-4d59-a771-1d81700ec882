import requests
import time
import sys
import json

# Configuration
API_URL = "http://localhost:11235"
API_KEY = "Bearer 12345"
DEFAULT_TIMEOUT = 10
POLL_INTERVAL = 5  # seconds

def check_service_health():
    """Check if the crawler service is running and healthy."""
    try:
        response = requests.get(f"{API_URL}/health", timeout=DEFAULT_TIMEOUT)
        if response.status_code == 200:
            health_data = response.json()
            print(f"Service is healthy. Available slots: {health_data.get('available_slots', 'unknown')}")
            return True
        else:
            print(f"Service returned status code {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"Error connecting to crawler service: {e}")
        return False

def submit_crawl_job(url, priority=10):
    """Submit a new crawl job."""
    try:
        print(f"Submitting crawl job for URL: {url}")
        response = requests.post(
            f"{API_URL}/crawl",
            json={"urls": url, "priority": priority},
            headers={"Authorization": API_KEY},
            timeout=DEFAULT_TIMEOUT
        )
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get("task_id")
            print(f"Submission successful. Task ID: {task_id}")
            return task_id
        else:
            print(f"Failed to submit job. Status code: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"Error submitting job: {e}")
        return None

def check_task_status(task_id):
    """Check the status of an existing task."""
    try:
        response = requests.get(
            f"{API_URL}/task/{task_id}",
            headers={"Authorization": API_KEY},
            timeout=DEFAULT_TIMEOUT
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Failed to check task. Status code: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"Error checking task: {e}")
        return None

def poll_until_complete(task_id, max_attempts=60):
    """Poll the task status until it completes or fails."""
    for attempt in range(max_attempts):
        status_data = check_task_status(task_id)
        
        if not status_data:
            print("Failed to get task status")
            time.sleep(POLL_INTERVAL)
            continue
            
        status = status_data.get("status")
        print(f"Task status: {status}")
        
        if status == "completed":
            print("Task completed successfully!")
            return status_data
        elif status in ["failed", "error"]:
            error = status_data.get("error", "Unknown error")
            print(f"Task failed: {error}")
            
            if "ACS-GOTO" in str(error):
                print("\nThe task failed due to navigation issues.")
                print("This is likely because the URL is not accessible from the crawler service.")
                print("Try using a different URL that is accessible.")
                
            return status_data
        elif status == "running":
            progress = status_data.get("progress", {})
            if progress:
                print(f"Progress: {progress.get('current', 0)}/{progress.get('total', 0)} pages")
        
        print(f"Waiting {POLL_INTERVAL} seconds before checking again...")
        time.sleep(POLL_INTERVAL)
    
    print(f"Gave up after {max_attempts} polling attempts")
    return None

def save_results(task_data, filename=None):
    """Save the crawl results to a file."""
    if not filename:
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        filename = f"crawl_results_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(task_data, f, indent=2)
    
    print(f"Results saved to {filename}")

def main():
    # Check if the service is healthy
    if not check_service_health():
        print("Service is not healthy. Exiting.")
        sys.exit(1)
    
    # Get URL from command line or use default
    if len(sys.argv) > 1:
        url = sys.argv[1]
    else:
        # Use a reliable default URL
        url = "https://example.com"
        print(f"No URL provided. Using default: {url}")
    
    # Submit the crawl job
    task_id = submit_crawl_job(url)
    if not task_id:
        print("Failed to submit crawl job. Exiting.")
        sys.exit(1)
    
    # Poll until the task completes
    result = poll_until_complete(task_id)
    if result:
        # Save the results
        save_results(result)

if __name__ == "__main__":
    main()

import os
import sys
import subprocess
from pathlib import Path

# Add the current directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# Try to import our downloader
try:
    from download_specific_driver import download_specific_chromedriver
except ImportError:
    print("Could not import download_specific_driver.py")
    sys.exit(1)

# Known working versions for Chrome 136
COMPATIBLE_VERSIONS = [
    "136.0.6099.0",  # Latest version for Chrome 136
    "135.0.6070.0",  # Might work with Chrome 136
    "114.0.5735.90"  # Fallback to a stable version
]

def main():
    print("Setting up ChromeDriver for Chrome 136...")
    
    # Try to install required packages
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "requests"])
        print("Successfully installed required packages")
    except Exception as e:
        print(f"Warning: Could not install required packages: {e}")
    
    # Try each version until one works
    driver_path = None
    for version in COMPATIBLE_VERSIONS:
        print(f"Trying ChromeDriver version {version}...")
        driver_path = download_specific_chromedriver(version)
        if driver_path:
            print(f"Successfully downloaded ChromeDriver {version}")
            break
    
    if not driver_path:
        print("Failed to download any compatible ChromeDriver version")
        sys.exit(1)
    
    # Create a simple test script to verify the driver works
    test_script = """
from selenium import webdriver
from selenium.webdriver.chrome.service import Service

# Path to the ChromeDriver
driver_path = "{}"

# Set up the service
service = Service(executable_path=str(driver_path))

# Set up Chrome options
options = webdriver.ChromeOptions()
options.add_argument("--headless")  # Run in headless mode for testing

try:
    # Initialize the driver
    driver = webdriver.Chrome(service=service, options=options)
    print("ChromeDriver initialized successfully!")
    
    # Get Chrome and driver versions
    print(f"Chrome version: {{driver.capabilities['browserVersion']}}")
    print(f"ChromeDriver version: {{driver.capabilities['chrome']['chromedriverVersion'].split(' ')[0]}}")
    
    # Close the driver
    driver.quit()
    print("Test completed successfully!")
except Exception as e:
    print(f"Error: {{e}}")
""".format(str(driver_path).replace('\\', '\\\\'))
    
    # Save the test script
    test_script_path = os.path.join(current_dir, "test_chromedriver.py")
    with open(test_script_path, "w") as f:
        f.write(test_script)
    
    print(f"Created test script at {test_script_path}")
    print("You can run this script to verify the ChromeDriver works:")
    print(f"  python {test_script_path}")
    
    # Update the main test.py file to use this driver
    print("Updating test.py to use the downloaded ChromeDriver...")
    
    # Path to test.py
    test_py_path = os.path.join(current_dir, "test.py")
    
    if os.path.exists(test_py_path):
        with open(test_py_path, "r") as f:
            content = f.read()
        
        # Add code to use the specific driver
        driver_setup_code = f"""
# Use the specific ChromeDriver we downloaded
driver_path = r"{str(driver_path).replace('\\', '\\\\')}"
if os.path.exists(driver_path):
    service = Service(executable_path=driver_path)
    driver = webdriver.Chrome(service=service, options=options)
else:
"""
        
        # Find the position to insert the code
        if "# 尝试多种方法初始化WebDriver" in content:
            content = content.replace(
                "# 尝试多种方法初始化WebDriver\ndriver = None\nerrors = []",
                "# 尝试多种方法初始化WebDriver\ndriver = None\nerrors = []\n" + driver_setup_code
            )
            
            # Update the file
            with open(test_py_path, "w") as f:
                f.write(content)
            
            print(f"Updated {test_py_path} to use the downloaded ChromeDriver")
        else:
            print(f"Could not update {test_py_path} - pattern not found")
    
    print("\nSetup completed!")
    print(f"ChromeDriver is available at: {driver_path}")
    print("You can now run your Selenium tests with this driver.")

if __name__ == "__main__":
    main()

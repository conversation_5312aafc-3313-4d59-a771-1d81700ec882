import asyncio
import json
from crawl4ai import As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,CrawlerRunConfig
from crawl4ai.extraction_strategy import <PERSON>sonCssExtractionStrategy
from pprint import pprint as pp

async def extract_movies():
    schema = {
        "name": "xbox games",
        "baseSelector": ".col-archive-rom",
        "type": "list",
        "fields": [
            {
                "name": "title",
                "type": "text",
                "selector": ".archive-left .h6",
            },
            {
                "name": "url",
                "type": "attribute",
                "selector": ".archive-left a",
                "attribute": "href",
            },
            {
                "name": "thumb",
                "type": "image",
                "selector": ".archive-left .attachment-thumbnail",
            }
        ],
    }

    extraction_strategy = JsonCssExtractionStrategy(schema, verbose=True)

    async with AsyncWebCrawler(
        verbose=True,
        headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Cache-Control': 'max-age=0',
            'Upgrade-Insecure-Requests': '1'
        },
        wait_time=5  # 增加页面加载等待时间
    ) as crawler:
        all_movies = []
        
        # 只爬取前2页的电影信息
        for page in range(1, 2):
            try:
                url = f"https://romsfun.com/roms/xbox-360/page/{page}/"
                result = await crawler.arun(
                    url=url,
                    config = CrawlerRunConfig(
                    extraction_strategy=extraction_strategy
                    
                )
,                    bypass_cache=True,
                    page_timeout=30000  # 增加超时时间
                )
                
                if not result.success:
                    print(f"爬取第 {page} 页失败，跳过该页")
                    continue
                print(result.html)
                movies = json.loads(result.extracted_content)
                all_movies.extend(movies)
                print(f"成功提取第 {page} 页的 {len(movies)} 部电影信息")
                
                # 增加请求间隔
                await asyncio.sleep(5)
                
            except Exception as e:
                print(f"爬取第 {page} 页时出错: {str(e)}")
                continue
        
        if not all_movies:
            print("未能成功提取任何电影信息")
            return []
        
        # 保存结果到文件
        # with open("dytt_movies.json", "w", encoding="utf-8") as f:
        #     json.dump(all_movies, f, ensure_ascii=False, indent=2)
            
        pp(all_movies)
        print(f"总共提取了 {len(all_movies)} 部电影信息")
        return all_movies

if __name__ == "__main__":
    asyncio.run(extract_movies())

import os
import sys
import zipfile
import requests
import platform
from pathlib import Path

def download_specific_chromedriver(version, output_dir="drivers"):
    """
    Download a specific version of ChromeDriver.
    
    Args:
        version (str): The specific version to download (e.g., "114.0.5735.90")
        output_dir (str): Directory to save the driver
    
    Returns:
        Path to the downloaded driver or None if download failed
    """
    system = platform.system()
    drivers_dir = Path(output_dir)
    drivers_dir.mkdir(exist_ok=True)
    
    # Determine platform
    if system == "Windows":
        platform_name = "win32"
    elif system == "Linux":
        platform_name = "linux64"
    elif system == "Darwin":
        platform_name = "mac64" if platform.machine() != "arm64" else "mac_arm64"
    else:
        print(f"Unsupported platform: {system}")
        return None
    
    # Try to download from Chrome for Testing
    try:
        # First check if this version is available in Chrome for Testing
        print(f"Checking if ChromeDriver {version} is available in Chrome for Testing...")
        cft_url = "https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json"
        response = requests.get(cft_url)
        
        if response.status_code == 200:
            data = response.json()
            
            # Find the exact version
            for version_info in data.get("versions", []):
                if version_info.get("version") == version:
                    # Find the chromedriver download
                    for download in version_info.get("downloads", {}).get("chromedriver", []):
                        if download.get("platform") == platform_name:
                            download_url = download.get("url")
                            
                            if download_url:
                                # Download path
                                zip_path = drivers_dir / f"chromedriver_{version}_{platform_name}.zip"
                                
                                # Download the file
                                print(f"Downloading ChromeDriver {version} from {download_url}...")
                                response = requests.get(download_url)
                                
                                if response.status_code == 200:
                                    with open(zip_path, 'wb') as f:
                                        f.write(response.content)
                                    
                                    # Extract the zip file
                                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                                        zip_ref.extractall(drivers_dir)
                                    
                                    # The Chrome for Testing zip has a different structure
                                    chromedriver_dir = drivers_dir / f"chromedriver-{platform_name}"
                                    if os.path.exists(chromedriver_dir):
                                        if system == "Windows":
                                            driver_path = chromedriver_dir / "chromedriver.exe"
                                        else:
                                            driver_path = chromedriver_dir / "chromedriver"
                                            os.chmod(driver_path, 0o755)
                                        
                                        print(f"ChromeDriver {version} downloaded and extracted to {driver_path}")
                                        return driver_path
    except Exception as e:
        print(f"Chrome for Testing API failed: {e}")
    
    # Try the old storage
    try:
        # Download URL
        download_url = f"https://chromedriver.storage.googleapis.com/{version}/chromedriver_{platform_name}.zip"
        
        # Download path
        zip_path = drivers_dir / f"chromedriver_{version}_{platform_name}.zip"
        
        # Download the file
        print(f"Trying to download ChromeDriver {version} from {download_url}...")
        response = requests.get(download_url)
        
        if response.status_code == 200:
            with open(zip_path, 'wb') as f:
                f.write(response.content)
            
            # Extract the zip file
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(drivers_dir)
            
            # Make the driver executable on Unix systems
            if system != "Windows":
                driver_path = drivers_dir / "chromedriver"
                os.chmod(driver_path, 0o755)
            else:
                driver_path = drivers_dir / "chromedriver.exe"
            
            print(f"ChromeDriver {version} downloaded and extracted to {driver_path}")
            return driver_path
    except Exception as e:
        print(f"Error downloading ChromeDriver {version}: {e}")
    
    print(f"Failed to download ChromeDriver {version}.")
    return None

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python download_specific_driver.py <version>")
        print("Example: python download_specific_driver.py 114.0.5735.90")
        sys.exit(1)
    
    version = sys.argv[1]
    driver_path = download_specific_chromedriver(version)
    
    if driver_path:
        print(f"Successfully downloaded ChromeDriver {version} to {driver_path}")
    else:
        print(f"Failed to download ChromeDriver {version}")
        sys.exit(1)

import os
import base64

import json
import logging
import requests

from typing import List, Optional, Callable, Union
from enum import Enum

# 假设存在对应的常量和工具类
BaosightConstants = type('BaosightConstants', (), {'STREAM_CHAT_URI': '/v1/chat/completions'})

BaosightLLMImageEnums = type('BaosightLLMImageEnums', (), {
    'getMimeTypeByExtension': staticmethod(lambda  ext: f'image/{ext.lstrip(".")}')
})

# 日志初始化
logger = logging.getLogger(__name__)


class Message:
    def __init__(self, role: str, contents: list):
        self.role = role
        self.contents = contents


class Content:
    def __init__(self, content_type: str, value: str):
        self.type = content_type
        self.value = value


class StreamCallback:
    def on_reasoning(self, reasoning: str):
        pass

    def on_response(self, response: str):
        pass

    def on_error(self, error: Exception):
        pass


class QVQClient:
    MEDIA_TYPE_JSON = 'application/json; charset=utf-8'

    def __init__(self, api_url: str = "http://************:8000/", api_key: str = ""):
        self.api_url = api_url.rstrip('/') 
        self.api_key = api_key

    def stream_chat(self, messages: List[Message], callback: StreamCallback):
        request = self._get_request(messages)
        self._request_async(request, callback)

    def sync_chat(self, messages: List[Message]) -> str:
        request = self._get_request(messages)
        return self._get_res_content(self._request_sync(request))

    def _get_callback(self, callback: StreamCallback) -> Callable:
        def handle_response(response, **kwargs):
            try:
                for line in response.iter_lines():
                    if line:
                        self._process_chunk(line.decode('utf-8'), callback)
            except Exception as e:
                callback.on_error(e)

        return handle_response

    def _request_async(self, request: dict, callback: StreamCallback):
        with requests.post(**request, stream=True) as resp:
            callback_func = self._get_callback(callback)
            callback_func(resp)

    def _request_sync(self, request: dict) -> str:
        # print(request)
        
            
        # print("请求开始")
        resp = requests.post(**request ,allow_redirects=False, timeout=30)
        # print("结果"+resp.text)
        return resp.text

    def _get_request(self, messages: List[Message]) -> dict:
        url = self.api_url + BaosightConstants.STREAM_CHAT_URI
        headers = {'Content-Type': self.MEDIA_TYPE_JSON}
        if self.api_key:
            headers['Authorization'] = f'Bearer {self.api_key}'

        data = {
            'model': 'qwenVL7B',
            'temperature': 0,
            'messages': [self._build_message(msg) for msg in messages]
        }

        return {
            'url': url,
            'headers': headers,
            'json': data
        }

    def _build_message(self, message: Message) -> dict:
        return {
            'role': message.role,
            'content': [
                self._build_content(content) for content in message.contents
            ]
        }

    def _build_content(self, content: Content) -> dict:
        content_data = {'type': content.type}
        content_type = content.type

        if content_type == "image_url":
            content_data['image_url'] = {'url': content.value}
        elif content_type == "video_url":
            content_data['video_url'] = {'url': content.value}
        elif content_type == "text":
            content_data['text'] = content.value
        else:
            logger.warning(f"不支持的消息类型：{content.type}")

        return content_data

    def _process_chunk(self, chunk: str, callback: StreamCallback):
        res_messages = self._get_response_messages(chunk)
        for msg in res_messages:
            if 'reasoning_content' in msg:
                callback.on_reasoning(msg['reasoning_content'])
            if 'content' in msg:
                callback.on_response(msg['content'])

    def _get_res_content(self, response_text: str) -> str:
        res_messages = self._get_response_messages(response_text)
        return ''.join([msg.get('content', '') for msg in res_messages])

    def _get_response_messages(self, chunk: str) -> list:
        obj = json.loads(chunk)
        res_messages = []

        if 'choices' in obj:
            for choice in obj['choices']:
                if 'message' in choice:
                    res_messages.append(choice['message'])

        return res_messages

    def get_text_message(self, ask: str) -> List[Message]:
        return [Message('user', [Content("text", ask)])]

    def get_image_message(self, ask: str, image_file: Union[str, bytes], image_type: str = None) -> List[Message]:
        if isinstance(image_file, str):
            if not os.path.exists(image_file):
                raise RuntimeError(f"图片文件不存在: {image_file}")
            with open(image_file, 'rb') as f:
                image_bytes = f.read()
            if not image_type:
                _, ext = os.path.splitext(image_file)
                image_type = ext.lower().lstrip('.')
        elif isinstance(image_file, bytes):
            image_bytes = image_file
        else:
            raise TypeError("image_file 必须是文件路径或字节数据")

        mime_type = BaosightLLMImageEnums.getMimeTypeByExtension(image_type)
        if not mime_type:
            raise RuntimeError(f"图片类型不支持: {image_type}")

        image_content = self._get_image_content(image_bytes, mime_type)
        return [Message('user', [
            Content("text", ask),
            Content("image_url", image_content)
        ])]

    def _get_image_content(self, image_bytes: bytes, mime_type: str) -> str:
        encoded = base64.b64encode(image_bytes).decode('utf-8')
        return f"data:{mime_type};base64,{encoded}"

    def parse(self, image_file: Union[str, bytes], image_type: str = None) :
        messages = self.get_image_message("完整解析所有信息，不要遗漏，尽量不改变原来文字", image_file,image_type)
        content=self.sync_chat(messages)
        
        return content
    
    
if __name__ == "__main__":
    api = QVQClient()
    messages = api.get_image_message("请用中文描述图片", "C:\\vs_project\\test\\OCR\\t.png")
    content=api.sync_chat(messages)
    print(content)
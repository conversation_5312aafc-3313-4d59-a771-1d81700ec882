{"input_path": "t.pdf_page0.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": true, "use_doc_unwarping": true}, "angle": 0}, "dt_polys": [[[107, 87], [288, 86], [288, 103], [107, 105]], [[208, 115], [425, 112], [425, 132], [208, 135]], [[251, 143], [381, 141], [382, 163], [251, 165]], [[74, 175], [125, 175], [125, 196], [74, 196]], [[168, 176], [294, 176], [294, 192], [168, 192]], [[75, 198], [160, 198], [160, 215], [75, 215]], [[165, 199], [202, 199], [202, 214], [165, 214]], [[244, 198], [272, 198], [272, 216], [244, 216]], [[284, 197], [319, 197], [319, 215], [284, 215]], [[327, 196], [373, 196], [373, 214], [327, 214]], [[376, 196], [421, 196], [421, 213], [376, 213]], [[428, 195], [457, 195], [457, 214], [428, 214]], [[74, 211], [105, 211], [105, 230], [74, 230]], [[284, 213], [299, 213], [299, 228], [284, 228]], [[327, 211], [344, 211], [344, 227], [327, 227]], [[376, 210], [411, 210], [411, 226], [376, 226]], [[73, 233], [161, 234], [160, 252], [73, 250]], [[166, 235], [238, 235], [238, 250], [166, 250]], [[243, 233], [272, 233], [272, 252], [243, 252]], [[284, 233], [319, 233], [319, 252], [284, 252]], [[327, 231], [355, 231], [355, 251], [327, 251]], [[376, 232], [420, 232], [420, 249], [376, 249]], [[427, 230], [457, 230], [457, 250], [427, 250]], [[76, 248], [160, 248], [160, 266], [76, 266]], [[165, 250], [238, 250], [238, 265], [165, 265]], [[284, 249], [299, 249], [299, 265], [284, 265]], [[376, 247], [410, 247], [410, 262], [376, 262]], [[75, 262], [101, 262], [101, 279], [75, 279]], [[165, 262], [214, 262], [214, 280], [165, 280]], [[74, 282], [123, 284], [122, 303], [73, 301]], [[165, 285], [239, 285], [239, 300], [165, 300]], [[244, 286], [321, 286], [321, 301], [244, 301]], [[164, 299], [200, 299], [200, 317], [164, 317]], [[243, 300], [322, 300], [322, 315], [243, 315]], [[75, 321], [123, 321], [123, 339], [75, 339]], [[164, 321], [205, 321], [205, 339], [164, 339]], [[242, 321], [289, 321], [289, 338], [242, 338]], [[325, 320], [394, 318], [394, 336], [325, 338]], [[165, 345], [238, 345], [238, 359], [165, 359]], [[242, 342], [290, 342], [290, 360], [242, 360]], [[326, 341], [365, 341], [365, 360], [326, 360]], [[75, 355], [155, 355], [155, 372], [75, 372]], [[164, 358], [199, 358], [199, 374], [164, 374]], [[76, 370], [91, 370], [91, 385], [76, 385]], [[244, 365], [320, 365], [320, 380], [244, 380]], [[325, 363], [364, 363], [364, 381], [325, 381]], [[243, 380], [270, 380], [270, 396], [243, 396]], [[73, 400], [112, 400], [112, 419], [73, 419]], [[163, 400], [202, 400], [202, 419], [163, 419]], [[244, 402], [321, 402], [321, 417], [244, 417]], [[324, 401], [388, 401], [388, 416], [324, 416]], [[74, 422], [207, 421], [207, 438], [74, 440]], [[210, 423], [239, 421], [241, 438], [211, 440]], [[241, 422], [404, 419], [404, 436], [241, 439]], [[407, 420], [435, 420], [435, 438], [407, 438]], [[75, 445], [102, 445], [102, 463], [75, 463]], [[108, 446], [122, 446], [122, 461], [108, 461]], [[131, 446], [148, 446], [148, 461], [131, 461]], [[152, 446], [213, 445], [213, 460], [152, 461]], [[75, 460], [103, 460], [103, 476], [75, 476]], [[104, 460], [124, 460], [124, 476], [104, 476]], [[153, 460], [170, 460], [170, 475], [153, 475]], [[75, 472], [102, 474], [100, 491], [74, 489]], [[106, 480], [125, 482], [123, 499], [104, 496]], [[133, 483], [146, 483], [146, 496], [133, 496]], [[153, 482], [213, 482], [213, 497], [153, 497]], [[219, 481], [257, 481], [257, 499], [219, 499]], [[289, 480], [467, 478], [467, 495], [289, 497]], [[106, 495], [123, 495], [123, 510], [106, 510]], [[153, 496], [211, 496], [211, 510], [153, 510]], [[74, 516], [302, 516], [302, 532], [74, 533]], [[79, 539], [142, 539], [142, 554], [79, 554]], [[164, 535], [202, 537], [201, 556], [163, 553]], [[226, 538], [271, 536], [272, 553], [227, 555]], [[289, 539], [379, 539], [379, 553], [289, 553]], [[388, 535], [427, 535], [427, 554], [388, 554]], [[456, 534], [485, 537], [484, 556], [454, 553]], [[325, 553], [343, 553], [343, 568], [325, 568]], [[397, 550], [420, 550], [420, 568], [397, 568]], [[78, 572], [144, 574], [144, 592], [77, 590]], [[225, 574], [272, 571], [273, 590], [226, 592]], [[389, 571], [427, 571], [427, 590], [389, 590]], [[397, 587], [421, 587], [421, 604], [397, 604]], [[404, 596], [527, 596], [527, 631], [404, 631]], [[119, 612], [155, 612], [155, 635], [119, 635]], [[157, 622], [171, 630], [165, 641], [151, 633]], [[77, 635], [84, 635], [84, 644], [77, 644]], [[244, 637], [253, 637], [253, 644], [244, 644]], [[259, 634], [294, 623], [300, 643], [265, 654]], [[168, 656], [196, 667], [188, 687], [160, 676]], [[341, 647], [356, 653], [351, 667], [336, 661]], [[335, 662], [359, 662], [359, 677], [335, 677]], [[422, 653], [456, 653], [456, 671], [422, 671]], [[141, 676], [173, 676], [173, 691], [141, 691]], [[193, 678], [200, 678], [200, 687], [193, 687]], [[201, 676], [234, 676], [234, 691], [201, 691]], [[399, 670], [513, 672], [513, 694], [398, 692]], [[121, 683], [131, 692], [122, 703], [111, 694]], [[126, 691], [142, 691], [142, 701], [126, 701]], [[139, 689], [170, 689], [170, 705], [139, 705]], [[180, 689], [264, 689], [264, 706], [180, 706]], [[250, 686], [353, 669], [359, 704], [256, 722]], [[355, 691], [443, 691], [443, 707], [355, 707]]], "text_det_params": {"limit_side_len": 736, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["附件1编号：BGMGZXS08-01B", "广州宝钢南方贸易有限公司", "客户登记表", "客户名称", "上海硕海金属材料有限公司", "注册地址/开票", "上海市", "邮编", "20190", "开票电", "021-567", "传真", "地址", "0", "话", "89696", "经营联系地址", "上海市宝山区", "邮编", "20190", "电话", "021-567", "传真", "（发票邮寄地", "友谊路1588弄", "0", "89696", "址）", "11号907", "开户银行", "建行上海宝钢宝", "账号31001517", "山支行", "7000 50050602", "注册资金", "1000万", "企业性质", "有限责任公司", "9131011359643", "法人代表", "袁绿庆", "统一社会信用代", "7691Y", "码", "单位所在省份及", "上海市", "城市", "联系人", "聪云春", "联系人手机号码", "15901800122", "是否自助打印质保书：是", "否口", "是否开通电子商务平台账号：是", "否□", "发票", "自", "取", "取发票人签", "售进", "□", "名", "方式", "邮", "寄", "寄往（注册", "收件人", "上海市室山区友谊路1588弄11号907", "□", "地/经营地）", "本公司授权下列人员代表公司处理有关合同业务：", "委托代理人1", "毕云春", "身份证号", "3101131976062539", "投权期", "长期", "10", "限", "委托代理人2", "身份证号", "投权期", "限", "法人代表签名：直保", "材料", "有", "单", "会", "属", "码", "元", "世", "签章：", "2025年", "月", "20日", "2025年3月20日", "福", "线", "核对", "速信息，并加益", "盖公非合同用章", "国传至南方公司"], "rec_scores": [0.9934636950492859, 0.9988741874694824, 0.9947563409805298, 0.97733473777771, 0.9870703816413879, 0.9807348251342773, 0.9994320869445801, 0.5975368022918701, 0.9993686676025391, 0.9946457743644714, 0.9980750679969788, 0.9759341478347778, 0.9992369413375854, 0.9679283499717712, 0.9122103452682495, 0.9934691190719604, 0.9566826224327087, 0.99713534116745, 0.9809191226959229, 0.9984800219535828, 0.9982690811157227, 0.9955070614814758, 0.9461378455162048, 0.9661421179771423, 0.9847986102104187, 0.9909787178039551, 0.9985066652297974, 0.7567185163497925, 0.9855124354362488, 0.9978123307228088, 0.9556037783622742, 0.949894905090332, 0.9967955946922302, 0.9633560180664062, 0.9081963300704956, 0.998150646686554, 0.9940184354782104, 0.9937567710876465, 0.9991844892501831, 0.9987173676490784, 0.9833803772926331, 0.9856637716293335, 0.998113751411438, 0.9594665169715881, 0.9788135886192322, 0.9996895790100098, 0.9640449285507202, 0.9961513876914978, 0.7187034487724304, 0.9673017859458923, 0.9989450573921204, 0.9480502605438232, 0.6032648086547852, 0.9883638024330139, 0.735841691493988, 0.889380931854248, 0.8549016714096069, 0.9882085919380188, 0.9719661474227905, 0.44242554903030396, 0.3465937674045563, 0.9856210350990295, 0.9963974952697754, 0.6691777110099792, 0.9951938986778259, 0.8929235339164734, 0.9991559982299805, 0.9698449373245239, 0.3034604489803314, 0.9759358763694763, 0.9753898978233337, 0.9866776466369629, 0.8385312557220459, 0.9994063377380371, 0.9988017678260803, 0.9257683753967285, 0.9362834095954895, 0.992065966129303, 0.9988677501678467, 0.9842707514762878, 0.9979968070983887, 0.9079480171203613, 0.9999632835388184, 0.7349920868873596, 0.9295381307601929, 0.9992306232452393, 0.8474494218826294, 0.28871092200279236, 0.6130834817886353, 0.19924040138721466, 0.06597524881362915, 0.13045012950897217, 0.9659053683280945, 0.7908313274383545, 0.9763630032539368, 0.8079074025154114, 0.955470860004425, 0.4044055938720703, 0.0537746287882328, 0.9946985244750977, 0.8674572706222534, 0.588865339756012, 0.9202044606208801], "rec_polys": [[[107, 87], [288, 86], [288, 103], [107, 105]], [[208, 115], [425, 112], [425, 132], [208, 135]], [[251, 143], [381, 141], [382, 163], [251, 165]], [[74, 175], [125, 175], [125, 196], [74, 196]], [[168, 176], [294, 176], [294, 192], [168, 192]], [[75, 198], [160, 198], [160, 215], [75, 215]], [[165, 199], [202, 199], [202, 214], [165, 214]], [[244, 198], [272, 198], [272, 216], [244, 216]], [[284, 197], [319, 197], [319, 215], [284, 215]], [[327, 196], [373, 196], [373, 214], [327, 214]], [[376, 196], [421, 196], [421, 213], [376, 213]], [[428, 195], [457, 195], [457, 214], [428, 214]], [[74, 211], [105, 211], [105, 230], [74, 230]], [[284, 213], [299, 213], [299, 228], [284, 228]], [[327, 211], [344, 211], [344, 227], [327, 227]], [[376, 210], [411, 210], [411, 226], [376, 226]], [[73, 233], [161, 234], [160, 252], [73, 250]], [[166, 235], [238, 235], [238, 250], [166, 250]], [[243, 233], [272, 233], [272, 252], [243, 252]], [[284, 233], [319, 233], [319, 252], [284, 252]], [[327, 231], [355, 231], [355, 251], [327, 251]], [[376, 232], [420, 232], [420, 249], [376, 249]], [[427, 230], [457, 230], [457, 250], [427, 250]], [[76, 248], [160, 248], [160, 266], [76, 266]], [[165, 250], [238, 250], [238, 265], [165, 265]], [[284, 249], [299, 249], [299, 265], [284, 265]], [[376, 247], [410, 247], [410, 262], [376, 262]], [[75, 262], [101, 262], [101, 279], [75, 279]], [[165, 262], [214, 262], [214, 280], [165, 280]], [[74, 282], [123, 284], [122, 303], [73, 301]], [[165, 285], [239, 285], [239, 300], [165, 300]], [[244, 286], [321, 286], [321, 301], [244, 301]], [[164, 299], [200, 299], [200, 317], [164, 317]], [[243, 300], [322, 300], [322, 315], [243, 315]], [[75, 321], [123, 321], [123, 339], [75, 339]], [[164, 321], [205, 321], [205, 339], [164, 339]], [[242, 321], [289, 321], [289, 338], [242, 338]], [[325, 320], [394, 318], [394, 336], [325, 338]], [[165, 345], [238, 345], [238, 359], [165, 359]], [[242, 342], [290, 342], [290, 360], [242, 360]], [[326, 341], [365, 341], [365, 360], [326, 360]], [[75, 355], [155, 355], [155, 372], [75, 372]], [[164, 358], [199, 358], [199, 374], [164, 374]], [[76, 370], [91, 370], [91, 385], [76, 385]], [[244, 365], [320, 365], [320, 380], [244, 380]], [[325, 363], [364, 363], [364, 381], [325, 381]], [[243, 380], [270, 380], [270, 396], [243, 396]], [[73, 400], [112, 400], [112, 419], [73, 419]], [[163, 400], [202, 400], [202, 419], [163, 419]], [[244, 402], [321, 402], [321, 417], [244, 417]], [[324, 401], [388, 401], [388, 416], [324, 416]], [[74, 422], [207, 421], [207, 438], [74, 440]], [[210, 423], [239, 421], [241, 438], [211, 440]], [[241, 422], [404, 419], [404, 436], [241, 439]], [[407, 420], [435, 420], [435, 438], [407, 438]], [[75, 445], [102, 445], [102, 463], [75, 463]], [[108, 446], [122, 446], [122, 461], [108, 461]], [[131, 446], [148, 446], [148, 461], [131, 461]], [[152, 446], [213, 445], [213, 460], [152, 461]], [[75, 460], [103, 460], [103, 476], [75, 476]], [[104, 460], [124, 460], [124, 476], [104, 476]], [[153, 460], [170, 460], [170, 475], [153, 475]], [[75, 472], [102, 474], [100, 491], [74, 489]], [[106, 480], [125, 482], [123, 499], [104, 496]], [[133, 483], [146, 483], [146, 496], [133, 496]], [[153, 482], [213, 482], [213, 497], [153, 497]], [[219, 481], [257, 481], [257, 499], [219, 499]], [[289, 480], [467, 478], [467, 495], [289, 497]], [[106, 495], [123, 495], [123, 510], [106, 510]], [[153, 496], [211, 496], [211, 510], [153, 510]], [[74, 516], [302, 516], [302, 532], [74, 533]], [[79, 539], [142, 539], [142, 554], [79, 554]], [[164, 535], [202, 537], [201, 556], [163, 553]], [[226, 538], [271, 536], [272, 553], [227, 555]], [[289, 539], [379, 539], [379, 553], [289, 553]], [[388, 535], [427, 535], [427, 554], [388, 554]], [[456, 534], [485, 537], [484, 556], [454, 553]], [[325, 553], [343, 553], [343, 568], [325, 568]], [[397, 550], [420, 550], [420, 568], [397, 568]], [[78, 572], [144, 574], [144, 592], [77, 590]], [[225, 574], [272, 571], [273, 590], [226, 592]], [[389, 571], [427, 571], [427, 590], [389, 590]], [[397, 587], [421, 587], [421, 604], [397, 604]], [[404, 596], [527, 596], [527, 631], [404, 631]], [[119, 612], [155, 612], [155, 635], [119, 635]], [[157, 622], [171, 630], [165, 641], [151, 633]], [[77, 635], [84, 635], [84, 644], [77, 644]], [[244, 637], [253, 637], [253, 644], [244, 644]], [[259, 634], [294, 623], [300, 643], [265, 654]], [[168, 656], [196, 667], [188, 687], [160, 676]], [[341, 647], [356, 653], [351, 667], [336, 661]], [[335, 662], [359, 662], [359, 677], [335, 677]], [[422, 653], [456, 653], [456, 671], [422, 671]], [[141, 676], [173, 676], [173, 691], [141, 691]], [[193, 678], [200, 678], [200, 687], [193, 687]], [[201, 676], [234, 676], [234, 691], [201, 691]], [[399, 670], [513, 672], [513, 694], [398, 692]], [[121, 683], [131, 692], [122, 703], [111, 694]], [[126, 691], [142, 691], [142, 701], [126, 701]], [[139, 689], [170, 689], [170, 705], [139, 705]], [[180, 689], [264, 689], [264, 706], [180, 706]], [[250, 686], [353, 669], [359, 704], [256, 722]], [[355, 691], [443, 691], [443, 707], [355, 707]]], "rec_boxes": [[107, 86, 288, 105], [208, 112, 425, 135], [251, 141, 382, 165], [74, 175, 125, 196], [168, 176, 294, 192], [75, 198, 160, 215], [165, 199, 202, 214], [244, 198, 272, 216], [284, 197, 319, 215], [327, 196, 373, 214], [376, 196, 421, 213], [428, 195, 457, 214], [74, 211, 105, 230], [284, 213, 299, 228], [327, 211, 344, 227], [376, 210, 411, 226], [73, 233, 161, 252], [166, 235, 238, 250], [243, 233, 272, 252], [284, 233, 319, 252], [327, 231, 355, 251], [376, 232, 420, 249], [427, 230, 457, 250], [76, 248, 160, 266], [165, 250, 238, 265], [284, 249, 299, 265], [376, 247, 410, 262], [75, 262, 101, 279], [165, 262, 214, 280], [73, 282, 123, 303], [165, 285, 239, 300], [244, 286, 321, 301], [164, 299, 200, 317], [243, 300, 322, 315], [75, 321, 123, 339], [164, 321, 205, 339], [242, 321, 289, 338], [325, 318, 394, 338], [165, 345, 238, 359], [242, 342, 290, 360], [326, 341, 365, 360], [75, 355, 155, 372], [164, 358, 199, 374], [76, 370, 91, 385], [244, 365, 320, 380], [325, 363, 364, 381], [243, 380, 270, 396], [73, 400, 112, 419], [163, 400, 202, 419], [244, 402, 321, 417], [324, 401, 388, 416], [74, 421, 207, 440], [210, 421, 241, 440], [241, 419, 404, 439], [407, 420, 435, 438], [75, 445, 102, 463], [108, 446, 122, 461], [131, 446, 148, 461], [152, 445, 213, 461], [75, 460, 103, 476], [104, 460, 124, 476], [153, 460, 170, 475], [74, 472, 102, 491], [104, 480, 125, 499], [133, 483, 146, 496], [153, 482, 213, 497], [219, 481, 257, 499], [289, 478, 467, 497], [106, 495, 123, 510], [153, 496, 211, 510], [74, 516, 302, 533], [79, 539, 142, 554], [163, 535, 202, 556], [226, 536, 272, 555], [289, 539, 379, 553], [388, 535, 427, 554], [454, 534, 485, 556], [325, 553, 343, 568], [397, 550, 420, 568], [77, 572, 144, 592], [225, 571, 273, 592], [389, 571, 427, 590], [397, 587, 421, 604], [404, 596, 527, 631], [119, 612, 155, 635], [151, 622, 171, 641], [77, 635, 84, 644], [244, 637, 253, 644], [259, 623, 300, 654], [160, 656, 196, 687], [336, 647, 356, 667], [335, 662, 359, 677], [422, 653, 456, 671], [141, 676, 173, 691], [193, 678, 200, 687], [201, 676, 234, 691], [398, 670, 513, 694], [111, 683, 131, 703], [126, 691, 142, 701], [139, 689, 170, 705], [180, 689, 264, 706], [250, 669, 359, 722], [355, 691, 443, 707]]}
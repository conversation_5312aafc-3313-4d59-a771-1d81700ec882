# 接口出参参数说明文档

# 入参

|  参数名   | 类型  | 说明  |
|----------------------|--------|----------------------------------------------------------------------|
| attachment_file_1  |附件1|  |
| attachment_file_2  |附件2|  |
| attachment_file_3  |附件3|  |



# 出参结果

|  参数名   | 类型  | 说明  |
|----------------------|--------|----------------------------------------------------------------------|
| status  |状态| 0：成功 其他：失败 |
| message  |描述|  |
| data  |返回数据|  |

## data.parseParams 附件解析内容汇总

| 参数名               | 类型   | 说明                                                                 | 来源/逻辑处理                                                                                     | 备注                                                                 |
|----------------------|--------|----------------------------------------------------------------------|-------------------------------------------------------------------------------------------------|----------------------------------------------------------------------|
| ​**客户基本信息**                                                                   |
| chineseUserName      | String | 客户中文全称                                                       |                                                               | 必填项                                                             |
| chineseUserAbbr      | String | 客户中文简称（最长5字符）                                            | （超长自动截断）                                         | 需人工复核                                                         |
| taxNum               | String | 统一社会信用代码                                                     |                                               | 固定18位格式                                                       |
| userAttr             | String | 客户属性                                                           | 固定值`"S"`（社会企业）                                                                         | 不可修改                                                           |
| tabFlag              | String | 是否集团公司标志                                                   | 固定值`"0"`                                                                                     | 0-否，1-是                                                         |
| activeStartDate      | String | 有效起始日期（YYYYMMDD）                                           |       | 日期格式校验                                                       |
| activeEndDate        | String | 有效截止日期（YYYYMMDD）                                           | 取B委托代理人中最晚结束日期，长期默认`20991231`                                                 | 需与起始日期逻辑关联                                               |
| openingAppFlag       | String | 申请类型                                                           | 固定值`"N"`（开户）                                                                             | 不可修改                                                           |
| openingType          | String | 申请性质                                                           | 固定值`"0"`（普通）                                                                             | 固定选项                                                           |
| countryId            | String | 国别地区                                                           | 固定值`"CHN"`（中国）                                                                           | ISO国家代码                                                        |
| trade                | String | 行业编码                                                           | 固定值`"00"`                                                                                    | 需与行业分类表对应                                                 |
| ​**地址及银行账号**           |
| regCurrency          | String | 注册金额币种                                                 |                                                           |                                                |
| regCapital          | String | 注册金额                                                 |                                                           | 万元                                               |
| regAddrLong          | String | 注册地址（长文本）                                                 |                                                           | 需完整包含省市区信息                                               |
| regAddr              | String | 注册地址（短文本）                                                 |                                                       | 保留省市区核心信息                                                 |
| regAddrProvince      | String | 注册省份编码                                                       |                                                       | 需映射表支持                                                       |
| teleNum              | String | 联系电话                                                           |                                              | 格式校验（11位数字）                                               |
| addressReLong        | String | 公司联系地址（长）                                                 |                                              | 需包含详细街道信息                                                 |
| addressRe            | String | 公司联系地址（短）                                                 |                                             | 保留核心地址信息                                                   |
| postCodeRe           | String | 邮编                                                               |                                              | 6位数字校验                                                        |
| bankBranchName       | String | 开户银行名称                                                       | 优先读取发票信息C中的开户银行，否则取B中的开户银行                                              | 需与银行代码映射表对应                                             |
| bankCode             | String | 银行代码                                                           |  称                                                                | 如"ICBKCNBJXXX"对应工商银行                                        |
| accountNum           | String | 银行账号                                                           | 优先读取发票信息C中的账号，否则取B中的账号                                                      | 长度校验（15-30位）                                                |
| accountName          | String | 账号名称                                                           |                                                            | 需与营业执照名称一致                                               |
| invoicePhone         | String | 发票电话                                                           | 优先读取C中的电话，否则取B中的开票电话                                                          | 格式校验（含区号）                                                 |
| invoiceAddrLong      | String | 发票地址（长）                                                     | 优先读取C中的地址，否则取B中的开票地址                                                          | 需包含省市区                                                       |
| invoiceAddr          | String | 发票地址（短）                                                     |                                                                        | 保留核心地址信息                                                   |
| invoiceMailAddress   | String | 发票邮寄地址                                                       | 直接读取B中的经营联系地址                                                                       | 需完整地址                                                         |
| ​**专属信息**                                                                       |
| customerPropertyId   | String | 客户分类代码                                                       | 固定值`"111"`                                                                                   | 需与客户分类表对应                                                 |
| insuranceType        | String | 保险类型代码                                                       | 固定值`"A"`                                                                                     | 预设选项                                                           |
| ifSettleMent         | String | 是否前置结算                                                       | 固定值`"1"`（是）                                                                               | 1-是，0-否                                                         |
| ifDFlag              | String | 是否分户                                                           | 固定值`"1"`（是）                                                                               | 1-是，0-否                                                         |
| ​**其他信息**                                                                       |
| applyId              | String | 申请人工号                                                         | 空值待人工补录                                                                                 | 需关联用户系统                                                     |
| applyName            | String | 申请人姓名                                                         | 空值待人工补录                                                                                 | 需与用户系统匹配                                                   |
| applyTel             | String | 申请人电话                                                         | 空值待人工补录                                                                                 | 需验证真实性                                                       |


## data.attachemnt 附件类型

| 参数名               | 类型   | 说明                                                                 | 来源/逻辑处理                                                                                     | 备注                                                                 |
|----------------------|--------|----------------------------------------------------------------------|-------------------------------------------------------------------------------------------------|----------------------------------------------------------------------|
| ​**客户基本信息**                                                                   |
| account_registration_file      | String | 开户登记表                                                       |                                                               | 必填项                                                             |
| business_license_file      | String | 营业执照                                            |                                          |                                                          |
| invoice_information_file               | String | 开票资料                                                     |                                               |                                                        |
from ollama_ocr import OCRProcessor

# 创建一个实例并指定模型
ocr = OCRProcessor(model_name='gemma3')
result = ocr.process_image(
    image_path="C:\\vs_project\\test\\OCR\\t.png", # pdf文档路径
    format_type="text", # 文档类型
    language="zh", # 语言
    custom_prompt="请提取其中的文字" # 可选，可选择用自定义prompt或预设好的prompt
)


# result = ocr.process_image(
#     image_path="OCR/t.pdf"
# , # pdf文档路径
#     format_type="text", # 文档类型
#     language="zh", # 语言
#     custom_prompt="请提取其中的文字" # 可选，可选择用自定义prompt或预设好的prompt
# )

print(result)
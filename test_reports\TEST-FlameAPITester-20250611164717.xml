<?xml version="1.0" ?>
<testsuite name="FlameAPITester-20250611164717" tests="1" time="2.004" failures="0" errors="1">
	<testcase classname="FlameAPITester" name="test_planning_agent" time="2.004">
		<error type="AssertionError" message="测试过程中发生错误: [('ERROR', 'WebSocket错误: Handshake status 403 Forbidden -+-+- {\'server\': \'nginx/1.21.5\', \'date\': \'Wed, 11 Jun 2025 08:47:17 GMT\', \'content-type\': \'application/json;charset=UTF-8\', \'content-length\': \'55\', \'connection\': \'keep-alive\', \'cache-control\': \'no-cache, no-store, max-age=0, must-revalidate\', \'pragma\': \'no-cache\', \'expires\': \'0\', \'x-content-type-options\': \'nosniff\', \'x-frame-options\': \'DENY\', \'x-xss-protection\': \'1 ; mode=block\', \'referrer-policy\': \'no-referrer\'} -+-+- b\'{&quot;code&quot;:403,&quot;data&quot;:null,&quot;message&quot;:&quot;\\xe7\\xad\\xbe\\xe5\\x90\\x8d\\xe6\\xa0\\xa1\\xe9\\xaa\\x8c\\xe5\\xa4\\xb1\\xe8\\xb4\\xa5&quot;}\'')]"><![CDATA[Traceback (most recent call last):
  File "c:\vs_project\test\智能开户\test\test2.py", line 295, in test_planning_agent
    self.fail(f"测试过程中发生错误: {all_responses}")
AssertionError: 测试过程中发生错误: [('ERROR', 'WebSocket错误: Handshake status 403 Forbidden -+-+- {\'server\': \'nginx/1.21.5\', \'date\': \'Wed, 11 Jun 2025 08:47:17 GMT\', \'content-type\': \'application/json;charset=UTF-8\', \'content-length\': \'55\', \'connection\': \'keep-alive\', \'cache-control\': \'no-cache, no-store, max-age=0, must-revalidate\', \'pragma\': \'no-cache\', \'expires\': \'0\', \'x-content-type-options\': \'nosniff\', \'x-frame-options\': \'DENY\', \'x-xss-protection\': \'1 ; mode=block\', \'referrer-policy\': \'no-referrer\'} -+-+- b\'{"code":403,"data":null,"message":"\\xe7\\xad\\xbe\\xe5\\x90\\x8d\\xe6\\xa0\\xa1\\xe9\\xaa\\x8c\\xe5\\xa4\\xb1\\xe8\\xb4\\xa5"}\'')]
]]></error>
	</testcase>
	<system-out><![CDATA[WebSocket错误: Handshake status 403 Forbidden -+-+- {'server': 'nginx/1.21.5', 'date': 'Wed, 11 Jun 2025 08:47:17 GMT', 'content-type': 'application/json;charset=UTF-8', 'content-length': '55', 'connection': 'keep-alive', 'cache-control': 'no-cache, no-store, max-age=0, must-revalidate', 'pragma': 'no-cache', 'expires': '0', 'x-content-type-options': 'nosniff', 'x-frame-options': 'DENY', 'x-xss-protection': '1 ; mode=block', 'referrer-policy': 'no-referrer'} -+-+- b'{"code":403,"data":null,"message":"\xe7\xad\xbe\xe5\x90\x8d\xe6\xa0\xa1\xe9\xaa\x8c\xe5\xa4\xb1\xe8\xb4\xa5"}'
WebSocket错误: {'code': 403, 'data': None, 'message': '签名校验失败'}
====================错误==================
错误详情: WebSocket错误: Handshake status 403 Forbidden -+-+- {'server': 'nginx/1.21.5', 'date': 'Wed, 11 Jun 2025 08:47:17 GMT', 'content-type': 'application/json;charset=UTF-8', 'content-length': '55', 'connection': 'keep-alive', 'cache-control': 'no-cache, no-store, max-age=0, must-revalidate', 'pragma': 'no-cache', 'expires': '0', 'x-content-type-options': 'nosniff', 'x-frame-options': 'DENY', 'x-xss-protection': '1 ; mode=block', 'referrer-policy': 'no-referrer'} -+-+- b'{"code":403,"data":null,"message":"\xe7\xad\xbe\xe5\x90\x8d\xe6\xa0\xa1\xe9\xaa\x8c\xe5\xa4\xb1\xe8\xb4\xa5"}'

测试结果:
- 总响应数: 1
- 是否有错误: True
- 是否有有效响应: False
- 测试用时: 0.00秒
- 所有响应: [('ERROR', 'WebSocket错误: Handshake status 403 Forbidden -+-+- {\'server\': \'nginx/1.21.5\', \'date\': \'Wed, 11 Jun 2025 08:47:17 GMT\', \'content-type\': \'application/json;charset=UTF-8\', \'content-length\': \'55\', \'connection\': \'keep-alive\', \'cache-control\': \'no-cache, no-store, max-age=0, must-revalidate\', \'pragma\': \'no-cache\', \'expires\': \'0\', \'x-content-type-options\': \'nosniff\', \'x-frame-options\': \'DENY\', \'x-xss-protection\': \'1 ; mode=block\', \'referrer-policy\': \'no-referrer\'} -+-+- b\'{"code":403,"data":null,"message":"\\xe7\\xad\\xbe\\xe5\\x90\\x8d\\xe6\\xa0\\xa1\\xe9\\xaa\\x8c\\xe5\\xa4\\xb1\\xe8\\xb4\\xa5"}\'')]
]]></system-out>
	<system-err><![CDATA[]]></system-err>
</testsuite>

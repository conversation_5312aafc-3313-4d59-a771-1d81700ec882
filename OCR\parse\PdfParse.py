import base64
import fitz  # PyMuPDF
from PicParse import PicParse


class PdfParse:
    @staticmethod
    def extract_elements_with_coords(pdf_path):
        doc = fitz.open(pdf_path)
        elements = []
        
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            # 提取文本块
            text_blocks = page.get_text("blocks")  # 返回 (x0,y0,x1,y1, text, block_no)
            # 提取图片
            images = page.get_images(full=True)
            
            # 处理文本块
            for block in text_blocks:
                x0, y0, x1, y1, text, block_no = block
                elements.append({
                    "type": "text",
                    "page": page_num+1,
                    "bbox": (x0, y0, x1, y1),
                    "content": text,
                    "confidence": 1.0  # 文本置信度
                })
            
            # 处理图片
            for img in images:
                xref = img[0]
                base_image = doc.extract_image(xref)
                pix = fitz.Pixmap(base_image["image"])
                # 图片边界框（需根据实际内容调整）
                img_bbox = (0, 0, pix.width, pix.height)  
                elements.append({
                    "type": "image",
                    "page": page_num+1,
                    "bbox": img_bbox,
                    "content": PicParse.parse(base_image["image"],base_image["ext"]),
                    "confidence": 0.8  # OCR 置信度
                })
        
        return elements
    @staticmethod
    def sort_elements(elements, dpi=300):
        # 坐标转换：PDF坐标 → 物理像素
        scale_factor = dpi / 72

        normalized = []
        for elem in elements:
            x0, y0, x1, y1 = elem["bbox"]
            # 转换为左上角为原点的物理坐标
            normalized.append({
                **elem,
                "x0_px": x0 * scale_factor,
                "y0_px": y0 * scale_factor,
                "x1_px": x1 * scale_factor,
                "y1_px": y1 * scale_factor
            })

        # 排序策略：先按页面 → 内容类型 → 水平位置 → 垂直位置
        sorted_elements = sorted(normalized, key=lambda x: (
            x["page"], 
            0 if x["type"]=="image" else 1,  # 图片优先于文本
            x["x0_px"], 
            x["y0_px"]
        ))

        return sorted_elements
    @staticmethod
    def parse(pdf_path):
        elements = PdfParse.extract_elements_with_coords(pdf_path)
        sorted_elements = PdfParse.sort_elements(elements)

        text = ""
        for elem in sorted_elements:
            # if elem["type"] == "text":
            text += elem["content"] + "\n"

        return text


# 示例调用
if __name__ == "__main__":
    pdf_path = "C:\\vs_project\\test\\OCR\\t.pdf"
    text = PdfParse.parse(pdf_path)

    print(text)

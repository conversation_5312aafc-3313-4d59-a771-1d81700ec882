转至内容
转至导航栏
转至主菜单
转至动作菜单
转至快速搜索
Profile picture
0
Confluence网研院宝联登平台应用平台一所平台二所大数据所人工智能所研发效能所
快速搜索
Search
智能编码
EFGrid 事件.md
 F收藏 观看 S分享
页面… 数据表格
跳到banner的尾部
回到标题开始
转至元数据结尾
由 田强强创建, 最终由 吴创修改于 十二月 23, 2024转至元数据起始
## EFGrid 事件说明
 
| 事件名 |  说明  |  参数  | 
|-----------|----------|-----------|
|  **loadComplete** | EFGrid loadComplelte事件，在表格渲染成功时触发执行  |  `e`: e.sender为EFGrid对象  | 
|  **onSave** | EFGrid onSave事件，在点击表格`保存`按钮时触发执行.  |  `e`: e.sender为EFGrid对象，e.preventDefault() 阻止`保存`动作进行下去  | 
|  **onDelete** | EFGrid onDelete事件，在点击表格`删除`按钮时触发执行.  |  `e`: e.sender为EFGrid对象，e.preventDefault() 阻止`删除`动作进行下去  | 
|  **onCancel** | EFGrid onCancel事件，在点击表格`取消`按钮时触发执行.  |  `e`: e.sender为EFGrid对象，e.preventDefault() 阻止`取消`动作进行下去  | 
|  **onAdd** | EFGrid onAdd事件，在点击表格`新增`按钮时触发执行.  |  `e`: e.sender为EFGrid对象;e.preventDefault() 是阻止`新增`动作进行下去,不会渲染新增的行;e.index是插入的行号,新增时默认为第一行，值为0，复制时默认从第一行开始插入，值为undefined;e.items是 将要插入的数据行数组。 | 
|  **onCheckRow** | EFGrid onCheckRow事件，在点击checkbox，勾选或取消勾选行时，触发的事件。 |  `e`: e.sender 表格实例对象； e.model 勾选行或取消勾选行的行数据，kendo.data.Model；e.row 当前行的行号；e.tr 行的tr,包括固定列和数据列 jquery对象；e.checked 用于区分是勾选还是取消勾选；e.fake 用于区分是手动点击的事件还是模拟的事件。 | 
|  **onCheckAllRows** | EFGrid onCheckAllRows 事件在点击表头的checkbox 时触发执行.  |  `e`: e.sender 表格实例对象； e.checked 用于区分是勾选还是取消勾选； e.fake 用于区分是手动点击的事件还是模拟的事件（使用API选中行触发此事件， 属于模拟的事件）。 | 
|  **onRowClick** | EFGrid onRowClick 事件在单机表格数据行时触发执行.  |  `e`: e.sender 表格实例对象；e.model 点击的行数据，kendo.data.Model类型；e.row 当前行的行号；e.tr 行的tr,包括固定列和数据列 jquery对象。 | 
|  **onRowDblClick** | EFGrid onRowDblClick 事件在 双击数据行时触发执行.  |  `e`: e.sender 表格实例对象；e.model 点击的行数据，kendo.data.Model类型；e.row 当前行的行号；e.tr 行的tr,包括固定列和数据列 jquery对象。 | 
|  **onCellClick** | EFGrid onCellClick 事件在单击单元格 时触发执行，注意编辑状态时不会触发。  |  `e`:  e.sender 表格实例对象；e.model 点击的行数据，kendo.data.Model类型；e.row 当前行的行号；e.tr 行的tr,包括固定列和数据列 jquery对象。| 
|  **onHeaderClick** | EFGrid onHeaderClick 事件在单击列头时触发执行.  |  `e`: e.sender 表格实例对象；e.columnName 点击的列名；e.th 行的th,列头 jquery对象。 | 
|  **onHeaderDblClick** | EFGrid onHeaderDblClick 事件在双击列头时触发执行.  |  `e`: e.sender 表格实例对象；e.columnName 点击的列名；e.th 行的th,列头 jquery对象。 | 
|  **beforeRequest** | EFGrid beforeRequest 事件在表格调用后台服务（如查询、删除、新增、更新）时触发执行。 |  `e`: e.sender 表格实例对象；e.type 操作类型（create、read、update、delete）。 | 
|  **onSuccess** | EFGrid onSuccess 事件在 表格调用后台服务（如查询、删除、新增、更新）`成功`之后触发执行.  |  `e`:  `e.sender` 表格实例对象；`e.eiInfo` 后台返回的EiInfo信息；`e.type` 操作类型（`create`、`read`、`update`、`delete`）。 | 
|  **onFail** | EFGrid onFail 事件在表格调用后台服务（如查询、删除、新增、更新）`失败`之后触发执行.  |  `e`:  `e.sender` 表格实例对象；`e.eiInfo` 后台返回的EiInfo信息；`e.type` 操作类型（`create`、`read`、`update`、`delete`）。| 
|  **beforeAdd** | EFGrid beforeAdd 事件在表格新增行之前触发执行.  |  `e`:e.sender为EFGrid对象;e.preventDefault() 是阻止`新增`动作进行下去,不会渲染新增的行 。 | 
|  **afterAdd** | EFGrid afterAdd 事件在表格新增行之后触发执行.  |  `e`: e.sender为EFGrid对象;e.preventDefault() 是取消渲染新增的行；e.index 插入的行号；e.items 将要插入的数据行数组；。 | 
|  **beforeEdit** | EFGrid beforeEdit 事件在单元格准备进入编辑状态时触发执行.  |  `e`: e.sender为EFGrid对象;e.container 单元格td jQuery对象；e.row 行号；e.col 列号；e.model 行数据对象 ，kendo.data.Model类型；e.field 编辑的列英文名；e.preventDefault() 阻止进入编辑状态。 | 
|  **edit** | EFGrid edit 事件在单元格进入编辑状态触发执行.  |  `e`: e.sender为EFGrid对象;e.container 单元格td jQuery对象；；e.model 行数据对象 ，kendo.data.Model类型。 | 
|  **afterEdit** | EFGrid afterEdit 事件在编辑结束，关闭单元格编辑状态时触发执行.  |  `e`: e.sender为EFGrid对象;e.container 单元格td jQuery对象；e.row 行号；e.col 列号；e.model 行数据对象 ，kendo.data.Model类型；e.field 编辑的列英文名；e.preventDefault() 保持单元格编辑状态。 | 
|  **dataBinding** | EFGrid dataBinding 事件在表格获取数据后绑定数据之前触发执行.  |  `e`:  e.sender为EFGrid对象; e.index 增加或删除数据时，显示操作数据的index；e.items 数据行数组。 | 
|  **dataBound** | EFGrid dataBound 事件在表格获取数据并绑定时触发执行.  |  `e`:  e.sender为EFGrid对象。 | 
|  **page** | EFGrid page 事件在 点翻页按钮，输入页数跳转时触发执行.  |  `e`:e.sender为EFGrid对象； e.page 期望翻到的页数。 | 
 
 
 
 
## EFGrid 事件 使用示例 
### 示例一：表格渲染完成后执行自定义代码示例 
```jsp 
<EF:EFGrid blockId="result" autoDraw="no">
   <EF:EFColumn ename="col1" cname="列1" />
   <EF:EFColumn ename="col2" cname="列2" />
</EF:EFGrid>
``` 
```javascript
$(function () {
   // 设置EFGrid配置信息
   IPLATUI.EFGrid = {
      // 设置blockId="result"的EFGrid配置信息
    "result": {
        /**
         * EFGrid渲染成功的回调事件
         * @param e kendo.ui.Grid
         */
        loadComplete: function (e) {
            var gridInstance = e.sender;
            // 执行用户自定义逻辑代码
            
        }
    }
   }
})
```
 
 
### 示例二：表格点击保存按钮时触发事件
```jsp 
<EF:EFGrid blockId="result" autoDraw="no">
   <EF:EFColumn ename="col1" cname="列1" />
   <EF:EFColumn ename="col2" cname="列2" />
</EF:EFGrid>
``` 
```javascript
$(function () {
   // 设置EFGrid配置信息
   IPLATUI.EFGrid = {
      // 设置blockId="result"的EFGrid配置信息
    "result": {
        /**
         * 点击EFGrid的【保存】按钮时，触发自定义逻辑代码
         * @param e kendo.ui.Grid
         */
        onSave: function (e) {
            var gridInstance = e.sender;
            var flag = false;
            // 执行用户自定义逻辑代码
 
            // 根据上述代码结果判断是否继续保存
            if(flag){
               e.preventDefault(); // 假设判断不继续保存，则阻止后续事件触发
            } 
            
        }
    }
   }
})
```
### 示例三：表格点击删除按钮时触发事件
```jsp 
<EF:EFGrid blockId="result" autoDraw="no">
   <EF:EFColumn ename="col1" cname="列1" />
   <EF:EFColumn ename="col2" cname="列2" />
</EF:EFGrid>
``` 
```javascript
$(function () {
   // 设置EFGrid配置信息
   IPLATUI.EFGrid = {
      // 设置blockId="result"的EFGrid配置信息
    "result": {
        /**
         * 点击EFGrid的【删除】按钮时，触发自定义逻辑代码
         * @param e kendo.ui.Grid
         */
        onDelete: function (e) {
            var gridInstance = e.sender;
            var flag = false;
            // 执行用户自定义逻辑代码
 
            // 根据上述代码结果判断是否继续删除
            if(flag){
               e.preventDefault(); // 假设判断不继续删除，则阻止后续事件触发
            } 
            
        }
    }
   }
})
```
 
### 示例四：表格点击取消按钮时触发事件
```jsp 
<EF:EFGrid blockId="result" autoDraw="no">
   <EF:EFColumn ename="col1" cname="列1" />
   <EF:EFColumn ename="col2" cname="列2" />
</EF:EFGrid>
``` 
```javascript
$(function () {
   // 设置EFGrid配置信息
   IPLATUI.EFGrid = {
      // 设置blockId="result"的EFGrid配置信息
    "result": {
        /**
         * 点击EFGrid的【取消】按钮时，触发自定义逻辑代码
         * @param e kendo.ui.Grid
         */
        onCancel: function (e) {
            var gridInstance = e.sender;
            var flag = false;
            // 执行用户自定义逻辑代码
 
            // 根据上述代码结果判断是否继续取消
            if(flag){
               e.preventDefault(); // 假设判断不继续取消，则阻止后续事件触发
            } 
            
        }
    }
   }
})
```
 
### 示例五：点击表格行checkbox，勾选行时触发事件
```jsp 
<EF:EFGrid blockId="result" autoDraw="no">
   <EF:EFColumn ename="col1" cname="列1" />
   <EF:EFColumn ename="col2" cname="列2" />
</EF:EFGrid>
``` 
```javascript
$(function () {
   // 设置EFGrid配置信息
   IPLATUI.EFGrid = {
      // 设置blockId="result"的EFGrid配置信息
    "result": {
        /**
         * 点击行首checkbox，勾选行时触发的事件
         * @param e     kendo的Event对象
         * e.sender     表格实例对象
         * e.fake       用于区分是手动点击的事件还是模拟的事件
         * e.checked    用于区分是勾选还是取消勾选
         * e.model      勾选或取消勾选的行数据，kendo.data.Model
         * e.row        当前行的行号
         * e.tr         行的tr,包括固定列和数据列 jquery对象
         */
        onCheckRow: function (e) {
            if (!e.fake) { // 手动点击触发的事件 例如使用API选中行/点击表头的checkbox 都是模拟的事件
                var grid = e.sender,
                    model = e.model,
                    $tr = e.tr,
                    row = e.row;
  
                // 执行自定义代码
                // 例如下述代码
                if (model["payment_type"] === "Alipay") {
                    if (e.checked) {
                        console.log("勾选了第" + row + "行数据");
                        $tr.css({
                            background: "red",
                            color: "yellow"
                        });
                    }
                }
            }
            
        }
    }
   }
})
```
 
### 示例六：点击表头checkbox，勾选或取消勾选全部的checkbox时触发事件
```jsp 
<EF:EFGrid blockId="result" autoDraw="no">
   <EF:EFColumn ename="col1" cname="列1" />
   <EF:EFColumn ename="col2" cname="列2" />
</EF:EFGrid>
``` 
```javascript
$(function () {
   // 设置EFGrid配置信息
   IPLATUI.EFGrid = {
      // 设置blockId="result"的EFGrid配置信息
    "result": {
        /**
         * 点击表头的勾选全部的checkbox的回调; 会触发每行的onCheckRow事件，事件是fake的
         * @param e     kendo的Event对象
         * e.sender     表格实例对象
         * e.fake       用于区分是手动点击的事件还是模拟的事件
         * e.checked    用于区分是勾选还是取消勾选
         */
        onCheckAllRows: function (e) {
            var action = e.checked ? "全部勾选" : "取消全部勾选";
            // 执行自定义代码
        }
    }
   }
})
```
  
### 示例七：表格行点击时触发事件
```jsp 
<EF:EFGrid blockId="result" autoDraw="no">
   <EF:EFColumn ename="col1" cname="列1" />
   <EF:EFColumn ename="col2" cname="列2" />
</EF:EFGrid>
``` 
```javascript
$(function () {
   // 设置EFGrid配置信息
   IPLATUI.EFGrid = {
      // 设置blockId="result"的EFGrid配置信息
    "result": {
       /**
         * 单击数据行时触发的事件，注意编辑状态时不会触发
         * @param e
         * e.sender     表格实例对象
         * e.model      点击的行数据，kendo.data.Model
         * e.row        当前行的行号
         * e.tr         行的tr,包括固定列和数据列 jquery对象
         */
        onRowClick: function (e) {
            // 行点击时触发自定义逻辑代码
        }
    }
   }
})
```
 
 
### 示例八：表格行双击时触发事件
```jsp 
<EF:EFGrid blockId="result" autoDraw="no">
   <EF:EFColumn ename="col1" cname="列1" />
   <EF:EFColumn ename="col2" cname="列2" />
</EF:EFGrid>
``` 
```javascript
$(function () {
   // 设置EFGrid配置信息
   IPLATUI.EFGrid = {
      // 设置blockId="result"的EFGrid配置信息
    "result": {
       /**
         * 双击数据行时触发的事件，注意编辑状态时不会触发
         * @param e
         * e.sender     表格实例对象
         * e.model      点击的行数据，kendo.data.Model
         * e.row        当前行的行号
         * e.tr         行的tr,包括固定列和数据列 jquery对象
         */
        onRowDblClick: function (e) {
            // 双击表格行时触发自定义逻辑代码
        }
    }
   }
})
```
 
### 示例九：表格单元格单击时触发事件
```jsp 
<EF:EFGrid blockId="result" autoDraw="no">
   <EF:EFColumn ename="col1" cname="列1" />
   <EF:EFColumn ename="col2" cname="列2" />
</EF:EFGrid>
``` 
```javascript
$(function () {
   // 设置EFGrid配置信息
   IPLATUI.EFGrid = {
      // 设置blockId="result"的EFGrid配置信息
    "result": {
       /**
         * 单击单元格时触发的事件，注意编辑状态时不会触发
         * @param e
         * e.sender     kendoGrid对象，resultGrid
         * e.model      点击的行数据，kendo.data.Model
         * e.row        当前行的行号
         * e.td         单元格td, jquery对象
         */
        onCellClick: function (e) {
            // 双击表格行时触发自定义逻辑代码
             
        }
    }
   }
})
```
 
### 示例十：表格列头单击或双击时触发事件
```jsp 
<EF:EFGrid blockId="result" autoDraw="no">
   <EF:EFColumn ename="col1" cname="列1" />
   <EF:EFColumn ename="col2" cname="列2" />
</EF:EFGrid>
``` 
```javascript
$(function () {
   // 设置EFGrid配置信息
   IPLATUI.EFGrid = {
      // 设置blockId="result"的EFGrid配置信息
    "result": {
       /**
         * 单击数据行时触发的事件，注意编辑状态时不会触发
         * @param e
         * e.sender     kendoGrid对象，resultGrid
         * e.columnName 当前行的行号
         * e.th         列头的th, jquery对象
         */
        onHeaderClick: function (e) {
             // 行双击时触发自定义逻辑代码
 
        },
        /**
         * 双击数据行时触发的事件，注意编辑状态时不会触发
         * @param e
         * e.sender     kendoGrid对象，resultGrid
         * e.columnName 当前行的行号
         * e.th         列头的th, jquery对象
         */
        onHeaderDblClick: function (e) {
             // 行双击时触发自定义逻辑代码
        }
    }
   }
})
```
 
### 示例十一：表格调用后台服务前触发事件
```jsp 
<EF:EFGrid blockId="result" autoDraw="no">
   <EF:EFColumn ename="col1" cname="列1" />
   <EF:EFColumn ename="col2" cname="列2" />
</EF:EFGrid>
``` 
```javascript
$(function () {
   // 设置EFGrid配置信息
   IPLATUI.EFGrid = {
      // 设置blockId="result"的EFGrid配置信息
    "result": {
       /**
             * 数据ajax提交前的回调。 包括调用新增服务、更新服务、删除服务、查询服务
             * @param e
             * e.sender     kendoGrid对象，resultGrid
             * e.type       操作类型 create read update delete
             */
            beforeRequest: function (e) {
                // 执行自定义逻辑代码
                e.preventDefault();  // 假设结果判定不继续调用后台服务
                 
            }
    }
   }
})
```
 
 
### 示例十二：表格调用后台服务成功或失败之后触发事件
```jsp 
<EF:EFGrid blockId="result" autoDraw="no">
   <EF:EFColumn ename="col1" cname="列1" />
   <EF:EFColumn ename="col2" cname="列2" />
</EF:EFGrid>
``` 
```javascript
$(function () {
   // 设置EFGrid配置信息
   IPLATUI.EFGrid = {
      // 设置blockId="result"的EFGrid配置信息
    "result": {
       /**
         * 表格调用后台服务成功的回调
         * @param e
         * e.sender     kendoGrid对象，resultGrid
         * e.eiInfo     后台返回的EiInfo信息
         * notifications 通知（数据ajax提交成功后，弹出成功提示信息）
         */
        onSuccess: function (e) {
 
            // 执行自定义逻辑代码
            // e.preventDefault(); // 不显示notifications
             
        },
       /**
         * 数表格调用后台服务失败的回调
         * @param e
         * e.sender     kendoGrid对象，resultGrid
         * e.errorMsg   后台返回的错误信息
         * e.xhr        ajax请求jqXHR对象
         */
        onFail: function (e) {
                // 执行自定义逻辑代码
                // e.preventDefault(); // 不显示notifications
        }
    }
   }
})
```
 
 
### 示例十三：表格新增行前后触发事件
```jsp 
<EF:EFGrid blockId="result" autoDraw="no">
   <EF:EFColumn ename="col1" cname="列1" />
   <EF:EFColumn ename="col2" cname="列2" />
</EF:EFGrid>
``` 
```javascript
$(function () {
   // 设置EFGrid配置信息
   IPLATUI.EFGrid = {
      // 设置blockId="result"的EFGrid配置信息
    "result": {
       /**
         * EFGrid新增行之前触发的事件，可以根据业务逻辑控制是否进行新增
         * @param e 事件对象
         * e.sender Grid对象
         * e.preventDefault 阻止事件发生
         */
        beforeAdd: function (e) {
            // 执行自定义逻辑代码
           var logic = true;
            if (logic) {  // 假设通过业务逻辑判断, 控制是否进行新增
                e.preventDefault();
            }
        },
        /**
         * EFGrid新增行时触发的事件，此时数据行tr元素还未渲染
         * @param e 事件对象
         * e.sender Grid对象
         * e.preventDefault 不会触发dataBound，不渲染tr
         * e.index 插入的行号
         * e.items 将要插入的数据行数组([Model,Model, ...])
         */
        onAdd: function (e) {
            // 执行自定义逻辑代码
        }
        /**
         * EFGrid新增后触发的事件，此时数据行tr元素已经渲染
         * @param e 事件对象
         * e.sender Grid对象
         * e.preventDefault 不会触发dataBound，不渲染tr
         * e.index 插入的行号
         * e.items 将要插入的数据行数组([Model,Model, ...])
         */
        afterAdd: function (e) {
            // 触发时机在dataBound之后
            // 执行自定义逻辑代码
        }
    }
   }
})
```
 
### 示例十四：表格编辑单元格前后触发事件
```jsp 
<EF:EFGrid blockId="result" autoDraw="no">
   <EF:EFColumn ename="col1" cname="列1" />
   <EF:EFColumn ename="col2" cname="列2" />
</EF:EFGrid>
``` 
```javascript
$(function () {
   // 设置EFGrid配置信息
   IPLATUI.EFGrid = {
      // 设置blockId="result"的EFGrid配置信息
    "result": {
      /**
         * 勾选行后，点击单元格准备编辑时的事件
         * beforeEdit可以用于自定义单元格是否可以编辑，不要和列的readonly，enable混用
         * @param e 事件对象
         * e.sender Grid对象
         * e.container 单元格td jQuery对象
         * e.row 行号
         * e.col 列号(columns中的列配置信息数组中的column对象的index)
         * e.model 行数据对象 kendo.data.Model
         * e.field 列英文名
         * e.preventDefault 禁止编辑
         */
        beforeEdit: function (e) {
            console.log("正准备编辑第" + e.row + "行数据");
            if (e.field === "order_number") {
                // 判断单元格 field 禁止编辑
                e.preventDefault();
            }
            // e.model.isNew() 判断当前行是不是新增的行
        },
        /**
         * KendoGrid的原生事件，不建议和beforeEdit一起使用，也不建议直接使用
         * Td单元格已经根据editor，渲染为Editable, 不能阻止单元格进入编辑状态
         * @param e事件对象
         * e.sender Grid对象
         * e.container 单元格td jQuery对象
         * e.model 行数据对象 kendo.data.Model
         */
        edit: function (e) {
            // 执行自定义逻辑代码
        },
        /**
         * 编辑结束，关闭单元格编辑状态时的事件
         * @param e 事件对象
         * * e.sender Grid对象
         * e.container 单元格td jQuery对象
         * e.row 行号
         * e.col 列号(columns中的列配置信息数组中的column对象的index)
         * e.model 行数据对象 kendo.data.Model
         * e.field 列英文名
         */
        afterEdit: function (e) {
            // 执行自定义逻辑代码，假设根据逻辑要求不关闭单元格编辑状态
            if (e.model[e.field] === "") {
                e.preventDefault(); // 不关闭单元格
            }
        }
    }
   }
})
```
 
### 示例十五：表格数据绑定前后触发事件
```jsp 
<EF:EFGrid blockId="result" autoDraw="no">
   <EF:EFColumn ename="col1" cname="列1" />
   <EF:EFColumn ename="col2" cname="列2" />
</EF:EFGrid>
``` 
```javascript
$(function () {
   // 设置EFGrid配置信息
   IPLATUI.EFGrid = {
      // 设置blockId="result"的EFGrid配置信息
    "result": {
      /**
         * 数据绑定
         * @param e
         * e.sender     Grid对象
         * e.action     触发此事件的行为 rebind, sync, add, remove
         * e.index      增加或删除数据时，显示操作数据的index
         * e.items      删除/增加的数据行数组 ([Model,Model, ...])
         */
        dataBinding: function (e) {
            // 执行自定义逻辑代码，例如做数据预处理，修改e.items 数据数组
        },
        /**
         * 当组件绑定到数据源中的数据时触发该事件
         * @param e
         *
         */
        dataBound: function (e) {
             // 执行自定义逻辑代码，例如根据col1列的值的大小，定制每行背景颜色
            var grid = e.sender;
            var trs = grid.table.find("tr");
            $.each(trs, function (i, tr) {
                var col1 = e.sender.dataItems()[i]['col1'];
                if (col1 && col1 > 9000) {
                    tr.style.background = "#FFB3FF";
                } else {
                    tr.style.background = "#99FFFF";
                }
            });
        }
    }
   }
})
```
 
### 示例十六：表格翻页时触发事件
```jsp 
<EF:EFGrid blockId="result" autoDraw="no">
   <EF:EFColumn ename="col1" cname="列1" />
   <EF:EFColumn ename="col2" cname="列2" />
</EF:EFGrid>
``` 
```javascript
$(function () {
   // 设置EFGrid配置信息
   IPLATUI.EFGrid = {
      // 设置blockId="result"的EFGrid配置信息
    "result": {
        /**
         * 翻页事件，点翻页按钮，输入页数跳转时触发
         * @param e 事件对象
         * e.sender Grid对象
         * e.page 期望翻到的页数
         */
        page: function (e) {
            // 禁止翻页
            e.preventDefault();
            console.log(e.page);
        }
    }
   }
})
```
赞成为第一个赞同者
无标签编辑标签

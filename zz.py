import os  # 添加: 导入os模块以便遍历目录
import pdfplumber  # 添加: 导入pdfplumber库以便读取PDF内容

dir = r"C:\Users\<USER>\Downloads"
print(dir)

# 添加: 遍历目录下以iplat开头的pdf文件
pdf_files = [f for f in os.listdir(dir) if f.startswith('iPlatV6Doc') and f.endswith('.pdf')]
print(pdf_files)

# 添加: 读取每个PDF文件的内容并获取标题
for pdf_file in pdf_files:
    pdf_path = os.path.join(dir, pdf_file)
    print(pdf_path)

    text=False
    with pdfplumber.open(pdf_path) as pdf:
        first_page = pdf.pages[0]
        text = first_page.extract_text()
    if text:
        # 假设标题在第一页的第一行
        title = text.split('\n')[0].strip()
        # print(f"Title of {pdf_file}: {title}")
        
        # 生成新的文件名，确保不包含非法字符
        new_title = ''.join(c for c in title if c.isalnum() or c in ' _-')
        new_filename = f"{new_title}.pdf"
        new_filepath = os.path.join(dir, new_filename)
        # 重命名文件
        if(not os.path.exists(new_filepath)):
            os.rename(pdf_path, new_filepath)
            print(f"Renamed {pdf_file} to {new_filename}")

        else:
            print(f"{new_filename} already exists")
    else:
        print(f"No text found in {pdf_file}")
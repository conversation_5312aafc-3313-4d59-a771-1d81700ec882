import unittest
import websocket
import json
import uuid
import threading
import time
from queue import Queue
from websocket import WebSocketBadStatusException
import  xmlrunner
import hmac
import hashlib
import base64
from urllib.parse import urlparse, urlenco<PERSON>, quote
from datetime import datetime, timezone


class FlameAPITester(unittest.TestCase):
    api_key="2741701DAF894C02BA78"
    api_secret="5127A65D9F874DAF86E9FAF002F8AF0C"
    body_id = "xzrbgent6ue6b0btaorwxi5phhbyld3d"
    
    # api_key="1AAC33F768114471B161"
    # api_secret="0573A49E71C14DC7AB1892A42E21FC9C"
    # body_id = "xzrbcess0nrn4iscnnun1dow4j6gzb73"
    
    @classmethod
    def setUpClass(cls):
        
        cls.ws_url = "ws://10.81.7.91:30009/openapi/flames/api/v2/chat"
        # cls.ws_url = "http://10.81.7.91:30009/openapi/flames/api/v2/chat"
        cls.response_queue = Queue(maxsize=10)
        cls.keep_running = False

    def generate_trace_id(self):
        """生成唯一的traceId"""
        return str(uuid.uuid1()).replace("-", "")

    def build_header(self, app_id, body_id):
        """构建请求头"""
        return {
            "traceId": self.generate_trace_id(),
            # "traceId": "58de252f-006d-4f29-891d-5a6eda26e9a2",
            "bodyId": body_id,
            "appId": app_id,
            "mode": 0
        }

    def build_planning_payload(self, session_id, text_content):
        """构建规划型/知识型请求体"""
        return {
            "header": self.build_header(self.api_key, self.body_id),
            "parameter": {},
            "payload": {
                "sessionId": session_id,
                "text": [{
                    "content": text_content,
                    "content_type": "text",
                    "role": "user"
                }]
            }
        }

    def build_task_payload(self, session_id="", form_params=None):
        """构建任务型请求体"""
        return {
            "header": self.build_header(self.api_key, self.body_id),
            "payload": {
                "sessionId": session_id,
                "text": [{
                    "content_type": "form_input",
                    "role": "user",
                    "formInput": {
                        "parameters": form_params or [{
                            "key": "arg1",
                            "name": "参数1",
                            "type": "string",
                            "required": True,
                            "value": "测试值"
                        }]
                    }
                }]
            }
        }
        
    

    def assemble_auth_url(self,hosturl: str) -> str:
        api_key=self.api_key
        api_secret=self.api_secret
        body_id = self.body_id
        # 解析主机URL
        parsed_url = urlparse(hosturl)
        if not parsed_url.scheme or not parsed_url.netloc:
            raise ValueError("Invalid URL format")

        # 生成UTC时间戳（RFC1123格式）
        date = datetime.now(timezone.utc).strftime("%a, %d %b %Y %H:%M:%S GMT")

        # 构建签名字符串
        sign_string = [
            f"host: {parsed_url.hostname}",
            f"date: {date}",
            f"GET {parsed_url.path} HTTP/1.1"
        ]
        signed_str = "\n".join(sign_string)
        # print("signed_str="+signed_str)
        # 计算HMAC-SHA256签名（Base64编码）
        signature = hmac.new(
            api_secret.encode('utf-8'),
            signed_str.encode('utf-8'),
            hashlib.sha256
        ).digest()
        signature_b64 = base64.b64encode(signature).decode('utf-8')

        # 构建Authorization头参数
        auth_params = {
            "hmac api_key": api_key,
            "algorithm": "hmac-sha256",
            "headers": "host date request-line",
            "signature": signature_b64
        }
        auth_header = ','.join([f'{k}="{str(v)}"' for k, v in auth_params.items()])

        # print("############"+auth_header)
        # Base64编码Authorization头
        auth_header_b64 = base64.b64encode(auth_header.encode('utf-8')).decode('utf-8')
        # print("############"+auth_header_b64)
        # 构建最终URL参数
        params = {
            "host": parsed_url.hostname,
            "date": date,
            "authorization": auth_header_b64,
            "bodyId": body_id
        }
        query_string = urlencode(params)

        # 返回完整URL
        return f"{hosturl}?{query_string}"

    def on_message(self, ws, message):
        """消息处理回调"""
        try:
            print(f"收到原始消息: {message}")
            data = json.loads(message)
            # print(f"解析后的JSON数据: {data}")
            self.response_queue.put(data)
        except json.JSONDecodeError as e:
            # print(f"JSON解析失败: {e}")
            print(f"原始消息: {message}")
            self.response_queue.put(("ERROR", f"JSON解析失败: {message}"))
        except Exception as e:
            print(f"处理消息时出现未知错误: {e}")
            self.response_queue.put(("ERROR", f"处理消息失败: {str(e)}"))

    def on_error(self, ws, error):
        """错误处理"""
        # print(f"WebSocket错误: {error}")
        # print(f"错误类型: {type(error)}")
        
        if isinstance(error, WebSocketBadStatusException):
            error_json = json.loads(error.resp_body)
            print(f"WebSocket错误: {error_json}")
        else:
            print(f"WebSocket错误: {error}")
        self.response_queue.put(("ERROR", f"WebSocket错误: {str(error)}"))

    def on_close(self, ws, close_status_code, close_msg):
        """连接关闭处理"""
        # print(f"### 连接已关闭 ### 状态码: {close_status_code}, 消息: {close_msg}")
        self.keep_running = False

    def start_connection(self, payload):
        """建立WebSocket连接并发送payload"""
        # 修复header格式 - 应该是字典而不是集合
        headers = {
            "User-Agent": "FlameAPITester/1.0",
            "Accept": "application/json"
        }

        ws = websocket.WebSocketApp(
            # self.assemble_auth_url(self.ws_url.replace("ws:", "http:").replace("wss:", "https:")),

            self.assemble_auth_url(self.ws_url),
            # header=headers,  # 修复：使用正确的字典格式
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close
        )
        # print("------------ws.url---"+ws.url )
        # print("------------ws.head---"+json.dumps(ws.header) )
        def on_open(ws):
            """连接建立后发送payload"""
            try:
                message = json.dumps(payload, ensure_ascii=False)
                print(f"发送消息: {message}")
                ws.send(message)
            except Exception as e:
                print(f"发送消息失败: {e}")
                self.response_queue.put(("ERROR", f"发送消息失败: {e}"))

        ws.on_open = on_open

        # 启动消息接收线程
        wst = threading.Thread(
            target=ws.run_forever,
            kwargs={"ping_interval": 30, "ping_timeout": 10}
        )
        wst.daemon = True
        wst.start()

        # 等待连接建立
        time.sleep(2)  # 增加等待时间确保连接建立
        return ws

    # def test_connection(self):
    #     """测试基础连接"""
    #     ws = self.start_connection({})
    #     self.assertIsNotNone(ws.sock)
    #     ws.close()

    def test_planning_agent(self):
        """测试规划型智能体"""
        # print("开始测试规划型智能体...")

        test_message = "重新为我开户，附件 TOSS_VjowMixCTjpmbGFtZXMsT046Y3pNNkx5OXdiR0Z1WlhSVWFXUXZjR3hoYm1WMEx6SXdNalV3TmpBMkwrVzhnT2FJdCtlWnUraXVzT2locUM1d1pHWT0=_TOSS ，TOSS_VjowMixCTjpmbGFtZXMsT046Y3pNNkx5OXdiR0Z1WlhSVWFXUXZjR3hoYm1WMEx6SXdNalV3TmpBMkwraVFwZVM0bXVhSnArZUZweTVxY0djPQ==_TOSS ,TOSS_VjowMixCTjpmbGFtZXMsT046Y3pNNkx5OXdiR0Z1WlhSVWFXUXZjR3hoYm1WMEx6SXdNalV3TlRBNEwrVzhnT2VscU9pMWhPYVdtUzV3Ym1jPQ==_TOSS"
        payload = self.build_planning_payload("", test_message)

        # print(f"构建的payload: {json.dumps(payload, ensure_ascii=False, indent=2)}")

        # 清空响应队列
        while not self.response_queue.empty():
            self.response_queue.get()

        ws = self.start_connection(payload)
        start_time = time.time()
        timeout = 60  # 减少超时时间到30秒

        # 等待响应
        valid_response = False
        error_occurred = False
        all_responses = []

        # print("等待服务器响应...")
        while time.time() - start_time < timeout:
            if not self.response_queue.empty():
                response = self.response_queue.get()
                all_responses.append(response)

                # print(f"收到响应: {response}")

                # 检查是否是错误响应
                if isinstance(response, tuple) and len(response) > 0 and response[0] == "ERROR":
                    # print(f"====================错误==================")
                    # print(f"错误详情: {response[1] if len(response) > 1 else '未知错误'}")
                    error_occurred = True
                    break

                # 检查是否是有效响应
                if isinstance(response, dict):
                    # print(f"响应类型: dict, 键: {list(response.keys())}")

                    # 检查多种可能的响应格式
                    if any(key in response for key in ["content", "payload", "data", "result"]):
                        # print("找到有效响应内容")
                        valid_response = True
                        # break

                    # 检查是否有嵌套的payload
                    if "payload" in response and isinstance(response["payload"], dict):
                        payload_data = response["payload"]
                        if any(key in payload_data for key in ["content", "text", "data"]):
                            # print("在payload中找到有效响应内容")
                            valid_response = True
                            # break
                else:
                    print("没有在响应中找到有效内容")

            time.sleep(0.1)  # 短暂等待避免CPU占用过高

        # 关闭连接
        try:
            ws.close()
        except:
            pass

        # 输出测试结果
        print(f"\n测试结果:")
        print(f"- 总响应数: {len(all_responses)}")
        print(f"- 是否有错误: {error_occurred}")
        print(f"- 是否有有效响应: {valid_response}")
        print(f"- 测试用时: {time.time() - start_time:.2f}秒")

        if all_responses:
            print(f"- 所有响应: {all_responses}")

        # 如果有错误，提供详细信息
        if error_occurred:
            self.fail(f"测试过程中发生错误: {all_responses}")

        # 如果没有有效响应，提供调试信息
        if not valid_response:
            if not all_responses:
                self.fail("未收到任何响应，可能是连接问题或服务器未响应")
            else:
                self.fail(f"未收到有效响应。收到的响应: {all_responses}")

        self.assertTrue(valid_response, "应该收到有效的响应")

    # def test_task_agent_validation(self):
    #     """测试任务型参数校验"""
    #     invalid_params = [{
    #         "key": "arg1",
    #         "name": "参数1",
    #         "type": "string",
    #         "required": True,
    #         "value": ""  # 故意留空触发校验
    #     }]
        
    #     payload = self.build_task_payload(form_params=invalid_params)
    #     ws = self.start_connection(payload)
        
    #     # 等待错误响应
    #     error_received = False
    #     start_time = time.time()
    #     while time.time() - start_time < 10:
    #         if not self.response_queue.empty():
    #             response_type, data = self.response_queue.get()
    #             if response_type == "ERROR":
    #                 error_received = True
    #                 break
        
    #     self.assertTrue(error_received)
    #     ws.close()

if __name__ == '__main__':
    unittest.main(testRunner=xmlrunner.XMLTestRunner(output='test_reports'))
    
    
    
#     {"header": {"traceId": "TRACE-1749690858-90d5b756", "bodyId": "xzrbgent6ue6b0btaorwxi5phhbyld3d", "appId": "2741701DAF894C02BA78", "mode": 0}, "parameter": {}, "payload": {"sessionId": "SESSION_001", "text": [{"content": "重新为我开户，附件 TOSS_VjowMixCTjpmbGFtZXMsT046Y3pNNkx5OXdiR0Z1WlhSVWFXUXZjR3hoYm1WMEx6SXdNalV3TmpBMkwrVzhnT2FJdCtlWnUraXVzT2locUM1d1pHWT0=_TOSS ，TOSS_VjowMixCTjpmbGFtZXMsT046Y3pNNkx5OXdiR0Z1WlhSVWFXUXZjR3hoYm1WMEx6SXdNalV3TmpBMkwraVFwZVM0bXVhSnArZUZweTVxY0djPQ==_TOSS ,TOSS_VjowMixCTjpmbGFtZXMsT046Y3pNNkx5OXdiR0Z1WlhSVWFXUXZjR3hoYm1WMEx6SXdNalV3TlRBNEwrVzhnT2VscU9pMWhPYVdtUzV3Ym1jPQ==_TOSS", "content_type": "text", "role": "user"}]}}       
# 收到原始消息: {"header":{"code":-1,"message":"Data Not Found","status":2,"sid":"TALKPHNC2XKGCLJJU","traceId":"TRACE-1749690858-90d5b756"},"payload":{}}
    
#     {"header":{"traceId":"58de252f-006d-4f29-891d-5a6eda26e9a2","mode":1,"bodyId":"xzrbgent6ue6b0btaorwxi5phhbyld3d","versionId":"xzrvgent5ljpojqvrgp6wicvjsp4pelj"},"payload":{"sessionId":"6849436b096a3a09ad3e115e","text":[{"content_type":"text","content":"请为我开户，附件 TOSS_VjowMixCTjpmbGFtZXMsT046Y3pNNkx5OXdiR0Z1WlhSVWFXUXZjR3hoYm1WMEx6SXdNalV3TmpBMkwrVzhnT2FJdCtlWnUraXVzT2locUM1d1pHWT0=_TOSS ，TOSS_VjowMixCTjpmbGFtZXMsT046Y3pNNkx5OXdiR0Z1WlhSVWFXUXZjR3hoYm1WMEx6SXdNalV3TmpBMkwraVFwZVM0bXVhSnArZUZweTVxY0djPQ==_TOSS ,TOSS_VjowMixCTjpmbGFtZXMsT046Y3pNNkx5OXdiR0Z1WlhSVWFXUXZjR3hoYm1WMEx6SXdNalV3TlRBNEwrVzhnT2VscU9pMWhPYVdtUzV3Ym1jPQ==_TOSS"}]},"parameter":{}}
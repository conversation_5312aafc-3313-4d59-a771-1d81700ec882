<?xml version="1.0" ?>
<testsuite name="FlameAPITester-20250611140312" tests="1" time="32.005" failures="0" errors="1">
	<testcase classname="FlameAPITester" name="test_planning_agent" time="32.005">
		<error type="AssertionError" message="未收到任何响应，可能是连接问题或服务器未响应"><![CDATA[Traceback (most recent call last):
  File "c:\vs_project\test\智能开户\test\test2.py", line 288, in test_planning_agent
    self.fail("未收到任何响应，可能是连接问题或服务器未响应")
AssertionError: 未收到任何响应，可能是连接问题或服务器未响应
]]></error>
	</testcase>
	<system-out><![CDATA[开始测试规划型智能体...
构建的payload: {
  "header": {
    "traceId": "TRACE-1749621792-28906a3b",
    "bodyId": "xzrbcess5ht7xw5o2l36x5px9owbscou",
    "appId": "9BC895C708EB440D804C",
    "mode": 0
  },
  "parameter": {},
  "payload": {
    "sessionId": "SESSION_001",
    "text": [
      {
        "content": "开户申请",
        "content_type": "text",
        "role": "user"
      }
    ]
  }
}
---------------http://**********:30009/openapi/flames/api/v2/chat?host=**********&date=Wed%2C+11+Jun+2025+06%3A03%3A12+GMT&authorization=aG1hYyBhcGlfa2V5PSI5QkM4OTVDNzA4RUI0NDBEODA0QyIsYWxnb3JpdGhtPSJobWFjLXNoYTI1NiIsaGVhZGVycz0iaG9zdCUyMGRhdGUlMjByZXF1ZXN0LWxpbmUiLHNpZ25hdHVyZT0iUDVzVHkwNVpXbUp6SUQ0WHREbzlEeldMUjMxNFZFS2syYlJtZ01WNmolMkZjJTNEIg%3D%3D&bodyId=xzrbcess5ht7xw5o2l36x5px9owbscou
等待服务器响应...

测试结果:
- 总响应数: 0
- 是否有错误: False
- 是否有有效响应: False
- 测试用时: 30.00秒
]]></system-out>
	<system-err><![CDATA[Exception in thread Thread-1 (run_forever):
Traceback (most recent call last):
  File "C:\ProgramData\anaconda3\Lib\threading.py", line 1038, in _bootstrap_inner
    self.run()
  File "C:\ProgramData\anaconda3\Lib\threading.py", line 975, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\websocket\_app.py", line 612, in run_forever
    ping_timeout, dispatcher, parse_url(self.url)[3]
                              ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\websocket\_url.py", line 63, in parse_url
    raise ValueError("scheme %s is invalid" % scheme)
ValueError: scheme http is invalid
]]></system-err>
</testsuite>

from crawl4ai import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>onfig, CrawlerRunConfig
from crawl4ai.extraction_strategy import <PERSON>sonCssExtractionStrategy
import os,time
import json

async def scrape_games():
    # 配置浏览器
    browser_config = BrowserConfig(
        headless=True,
        viewport={"width": 1920, "height": 1280},
        # extra_http_headers={
        #     "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36"
        # }
    )

    

    async with AsyncWebCrawler(config=browser_config) as crawler:
        # 定义提取策略
        extraction_strategy = JsonCssExtractionStrategy(
            schema={
                    "name": "xbox games",
                    "baseSelector": ".col-archive-rom",
                    "type": "list",
                    "fields": [
                        {
                            "name": "title",
                            "type": "text",
                            "selector": ".archive-left .h6",
                        },
                        {
                            "name": "url",
                            "type": "attribute",
                            "selector": ".archive-left a",
                            "attribute": "href",
                        },
                        {
                            "name": "thumb",
                            "type": "attribute",
                            "selector": ".archive-left .attachment-thumbnail",
                            "attribute": "src",
                        }
                    ],
            },
            verbose=True
        )

        # 配置爬虫运行参数
        run_config = CrawlerRunConfig(
            extraction_strategy=extraction_strategy,
            wait_for=".site-footer",
            # cache_mode="filesystem",
            # cache_dir="./cache",
            # max_pages=5,  # 最大爬取页数
            # page_retry=3,  # 重试次数
            # rate_limit=1  # 每秒1请求
        )
        markdown_table = "| 游戏名称 | 游戏链接 | 封面图 |\n" + \
                        "|----------|----------|--------|\n"
        for i in range(1, 145):
            games=None
            #重试2次
            for j in range(2):
                try:
                    games=None

                    result = await crawler.arun(
                        url="https://romsfun.com/roms/xbox-360/page/"+str(i)+"/",
                        
                        config=run_config
                    )
                    if result is  None or not result.success:
                        print(f"爬取第 {i}-{j} 页失败，跳过该页")
                        time.sleep(60)
                        continue
                    games = json.loads(result.extracted_content)

                    break
                except Exception as e:
                    print(f"爬取第 {i} 页时出错: {str(e)}")
                    # if j == 1:
                    #     raise e
            if games is None:
                print(f"爬取第 {i} 页失败，跳过该页")
                time.sleep(60)
                continue
            # print(result.extracted_content)

            # 生成Markdown表格
            
            
            for game in games:
                title = game.get("title", "未知游戏")
                link = game.get("url", "")
                image = game.get("thumb", "")
                
                markdown_table += f"| {title} | [{link}]({link}) | ![封面]({image}) |\n"

        # 保存文件
        os.makedirs("output", exist_ok=True)

        with open("output/xbox360_games.md", "w", encoding="utf-8") as f:
            f.write(f"# Xbox 360 游戏列表\n\n{markdown_table}")

        print("Markdown文件已生成：output/xbox360_games.md")

import asyncio
asyncio.run(scrape_games())
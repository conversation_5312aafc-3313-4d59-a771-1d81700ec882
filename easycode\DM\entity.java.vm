## 引入宏定义
$!{baowu.vm}

## 使用宏定义设置回调（保存位置与文件）
$!callback.setSavePath($tableInfo.savePath+"/domain")
$!callback.setFileName($entityName+".java")

## 使用宏定义设置包后缀,打印包路径
#setPackageSuffix("domain")

## 使用全局变量实现默认包导入
$!{autoImport.vm}
import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.util.StringUtils;
import java.util.HashMap;
import java.util.Map;

## 使用宏定义实现类注释信息
/**
 * Title: ${entityName}.java <br>
 * Description: ${tableInfo.name} ${tableInfo.comment} <br>
 *
 * Copyright: Baosight Software LTD.co Copyright (c) 2019. <br>
 *
 * @version 1.0
 * @CreateTime ${time.currTime('yyyy-MM-dd HH:mm:ss')} 
 * <AUTHOR>
 */
public class ${entityName} extends DaoEPBase {
    private static final long serialVersionUID = 1L;

#foreach($column in $tableInfo.fullColumn)
    public static final String FIELD_${tool.hump2Underline($column.name).toUpperCase()} = "$!{column.name}";    		/* $!{column.comment} */
#end

#foreach($column in $tableInfo.fullColumn)
    public static final String COL_${tool.hump2Underline($column.name).toUpperCase()} = "$!{tool.hump2Underline($column.name).toUpperCase()}";    		/* $!{column.comment} */
#end

    public static final String QUERY = "${entityName}.query";
    public static final String COUNT = "${entityName}.count";
    public static final String INSERT = "${entityName}.insert";
    public static final String UPDATE = "${entityName}.update";
    public static final String DELETE = "${entityName}.delete";

#foreach($column in $tableInfo.fullColumn)
    private ${column.shortType} $!{column.name} =#if($column.shortType=="String") ""#else new ${column.shortType}(0)#end;		/* $!{column.comment} */
#end

    /**
     * initialize the metadata.
     */
    public void initMetaData() {
        EiColumn eiColumn;

#foreach($column in $tableInfo.fullColumn)
        eiColumn = new EiColumn(FIELD_${tool.hump2Underline($column.name).toUpperCase()});
        eiColumn.setFieldLength(${column.obj.getDataType().getLength()});
        eiColumn.setDescName("$!{column.comment}");
        eiMetadata.addMeta(eiColumn);
#end
    }

    /**
     * the constructor.
     */
    public ${entityName}() {
        initMetaData();
    }

#foreach($column in $tableInfo.fullColumn)
    /**
     * get the $!{column.name} - $!{column.comment}.
     * @return the $!{column.name}
     */
    public ${column.shortType} get${tool.firstUpperCase($column.name)}() {
        return this.$!{column.name};
    }

    /**
     * set the $!{column.name} - $!{column.comment}.
     *
     * @param $!{column.name} - $!{column.comment}
     */
    public void set${tool.firstUpperCase($column.name)}(${column.shortType} $!{column.name}) {
        this.$!{column.name} = $!{column.name};
    }
#end

    /**
     * get the value from Map.
     *
     * @param map - source data map
     */
    @Override
    public void fromMap(Map map) {
#foreach($column in $tableInfo.fullColumn)
        #if($column.shortType=="String")
            set${tool.firstUpperCase($column.name)}(StringUtils.defaultIfEmpty(StringUtils.toString(map.get(FIELD_${tool.hump2Underline($column.name).toUpperCase()})), $!{column.name}));
        #else
            set${tool.firstUpperCase($column.name)}(NumberUtils.to${column.shortType}(StringUtils.toString(map.get(FIELD_${tool.hump2Underline($column.name).toUpperCase()})), $!{column.name}));
        #end
#end
    }

    /**
     * set the value to Map.
     */
    @Override
    public Map toMap() {
        Map map = new HashMap();
#foreach($column in $tableInfo.fullColumn)
        map.put(FIELD_${tool.hump2Underline($column.name).toUpperCase()}, StringUtils.toString($!{column.name}, eiMetadata.getMeta(FIELD_${tool.hump2Underline($column.name).toUpperCase()})));
#end
        return map;
    }
}
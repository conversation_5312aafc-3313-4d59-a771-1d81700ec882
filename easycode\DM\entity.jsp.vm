## 引入宏定义
$!{baowu.vm}

## 使用宏定义设置回调（保存位置与文件）
$!callback.setSavePath($baowuViewDir)
$!callback.setFileName($entityName+".jsp")

<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<%@ page import="com.baosight.iplat4j.core.ei.EiInfo" %>
<%@ page import="com.baosight.iplat4j.core.resource.I18nMessages" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<EF:EFPage prefix="imc">
    <EF:EFRegion id="inqu" title="查询条件">
        <div class="row">
#foreach($column in $tableInfo.otherColumn)
            <EF:EFInput ename="inqu_status-0-${column.name}" cname="${column.comment}" colWidth="3" ratio="4:8"/>
#end
        </div>
    </EF:EFRegion>

    <EF:EFRegion id="result" title="记录集">
        <EF:EFGrid blockId="result" isFloat="true" autoDraw="no" toolbarConfig="true" needAuth="true">
            <EF:EFColumn ename="${pk.obj.name.toLowerCase()}" cname="" hidden="true"/>
#foreach($column in $tableInfo.otherColumn)
            <EF:EFColumn ename="${column.name}" cname="${column.comment}" sort="true" required="true" align="center"/>
#end
        </EF:EFGrid>
    </EF:EFRegion>
</EF:EFPage>
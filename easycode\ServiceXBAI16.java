package com.baosight.imc.xb.ai.service;

import com.alibaba.fastjson2.JSONObject;
import com.baosight.elim.common.utils.GlobalUtils;
import com.baosight.eplat.shareservice.service.EServiceManager;
import com.baosight.eplat.upload.service.FileUploadManager;
import com.baosight.imc.common.utils.*;
import com.baosight.imc.interfaces.vz.bm.domain.VZBM1300;
import com.baosight.imc.interfaces.vz.bm.service.ServiceVZBM1300;
import com.baosight.imc.xb.ai.constant.AssistantCodeEnums;
import com.baosight.imc.xb.ai.constant.IapConstants;
import com.baosight.imc.xb.ai.domain.*;
import com.baosight.iplat4j.core.data.id.UUIDHexIdGenerator;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.resource.I18nMessages;
import com.baosight.iplat4j.core.service.impl.ServiceEPBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.util.DateUtils;
import com.baosight.iplat4j.core.util.ExceptionUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.FutureTask;
import java.util.stream.Collectors;

import static com.baosight.imc.common.utils.SegDataUtils.querySegDetailsBySegNo;

/**
 * 订货规则配置（走访分析用）
 */
public class ServiceXBAI16 extends ServiceEPBase {

    private static final Logger logger = LoggerFactory.getLogger(ServiceXBAI16.class);

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        super.initLoad(inInfo);
        return query(inInfo);
    }

    /**
     * 查询功能
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = super.query(inInfo, "XBAI16.query", new XBAI16());
        return outInfo;
    }

    /**
     * 新增功能
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo insert(EiInfo inInfo) {

        EiInfo outInfo = new EiInfo();


        String unitCode = " ";
        String segNo = "";

        Map map = SegDataUtils.querySegNoByEmpNo(UserSession.getUserId());
        if (map.get("status").toString().equals("1")) {
            List<Map> list = (List) map.get("data");
            unitCode = list.get(0).get("SEGNO").toString();
            segNo = list.get(0).get("RELATED0SEGNO").toString();
        } else {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("查询业务单元失败，请配置业务单元信息");
            return outInfo;
        }


        List rows = new ArrayList();

        for (int i = 0; i < inInfo.getBlock(EiConstant.resultBlock).getRowCount(); i++) {
            XBAI16 xbai16 = new XBAI16();
            xbai16.fromMap(inInfo.getRow(EiConstant.resultBlock, i));
            BeanUtil.beanAttributeValueTrim(xbai16);
            FormUtils.setCreatorProertyFromXservice(xbai16);
            boolean isCheckPass = checkXbai16(xbai16, outInfo);
            if (!isCheckPass) {
                if (StringUtils.isNotBlank(outInfo.getMsg())) {
                    outInfo.setMsg(errorString("VZBM0810001", "第" + (i + 1) + "行" + outInfo.getMsg()));
                }

                return outInfo;
            }
            int countDuplication = dao.count("XBAI16.duplication", new HashMap<String, Object>() {{
                put("customCompany", xbai16.getCustomCompany());
                put("directUserNum", xbai16.getDirectUserNum());
                put("finUserId", xbai16.getFinUserId());
            }});
            if (countDuplication > 0) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg(errorString("VZBM0810001", "第" + (i + 1) + "行记录重复！"));
                return outInfo;
            }
            xbai16.setSegNo(segNo);
            xbai16.setUnitCode(unitCode);
            rows.add(xbai16.toMap());

        }
        inInfo.getBlock(EiConstant.resultBlock).setRows(rows);

        return insert(inInfo, "XBAI16.insert");
    }

    @Nullable
    private boolean checkXbai16(XBAI16 xbai16, EiInfo outInfo) {
        try {
            String customCompany = xbai16.getCustomCompany();
            if (StringUtils.isEmpty(customCompany)) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("走访用户不能为空！");
                return false;
            }

            if (customCompany.getBytes("gbk").length > 100) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("走访用户最大为100个文字！");
                return false;
            }

            if (StringUtils.isEmpty(xbai16.getDirectUserNum()) && StringUtils.isEmpty(xbai16.getFinUserId())) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("订货用户和最终用户不能同时为空！");
                return false;
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return true;
    }


    /**
     * @param code
     * @param msg
     * @return
     */
    private String errorString(String code, String msg) {
        VZBM1300 queryMsg = new VZBM1300();
        queryMsg.setErrorNum(code);
        queryMsg.setFormEname("XBAI16");
        GlobalUtils.setCreatorProerty(queryMsg);
        return ServiceVZBM1300.getMessageTextByErrorNumAndRecord(new String[]{msg}, queryMsg);
    }

    /**
     * 修改功能
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo update(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();

        for (int i = 0; i < inInfo.getBlock(EiConstant.resultBlock).getRowCount(); i++) {
            XBAI16 xbai16 = new XBAI16();
            xbai16.fromMap(inInfo.getRow(EiConstant.resultBlock, i));
            BeanUtil.beanAttributeValueTrim(xbai16);
            FormUtils.setCreatorProertyFromXservice(xbai16);
            boolean isCheckPass = checkXbai16(xbai16, outInfo);
            if (!isCheckPass) {
                if (StringUtils.isNotBlank(outInfo.getMsg())) {
                    outInfo.setMsg(errorString("VZBM0810001", "第" + (i + 1) + "行" + outInfo.getMsg()));
                }

                return outInfo;
            }
            if (StringUtils.isEmpty(xbai16.getUuid())) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg("记录uuid为空！");
                return outInfo;
            }
            int countDuplication = dao.count("XBAI16.duplication", new HashMap<String, Object>() {{
                put("customCompany", xbai16.getCustomCompany());
                put("directUserNum", xbai16.getDirectUserNum());
                put("finUserId", xbai16.getFinUserId());
                put("notContainUuid", xbai16.getUuid());
            }});
            if (countDuplication > 0) {
                outInfo.setStatus(EiConstant.STATUS_FAILURE);
                outInfo.setMsg(errorString("VZBM0810001", "第" + (i + 1) + "行记录重复！"));
                return outInfo;
            }

        }


        return super.update(inInfo, "XBAI16.update");
    }

    /**
     * 删除功能
     *
     * @param inInfo
     * @return
     */
    @Override
    public EiInfo delete(EiInfo inInfo) {
        EiInfo outInfo = super.delete(inInfo, "XBAI16.delete");
        return outInfo;
    }


    /**
     * 查询客户信息
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryCustomer(EiInfo inInfo) {

        int limit = 20;
        Object limitObject = inInfo.getBlockInfoValue("resultDirectUser", "limit");
        if (Objects.nonNull(limitObject)) {
            limit = Integer.parseInt(limitObject.toString());
        }
        int finalLimit = limit;
        EiInfo queryInfo = new EiInfo() {{
            set(EiConstant.serviceId, "S_UN_BI_0017");
            set("cancelMark", "N");
            set("limit", finalLimit);
            set("offset", inInfo.getBlockInfoValue("resultDirectUser", "offset"));

            set("userNum", inInfo.getCellStr("inqu_custom", 0, "userNum"));
            set("chineseUserName", inInfo.getCellStr("inqu_custom", 0, "chineseUserName"));
            set("englishUserName", inInfo.getCellStr("inqu_custom", 0, "englishUserName"));

        }};
        EiInfo resultInfo = new EiInfo();

        try {
            EiInfo result = EServiceManager.call(queryInfo, ImcGlobalUtils.getToken());
            logger.info("resultInfo: {}", result);
            if (result.getStatus() >= 0) {
                resultInfo.setStatus(EiConstant.STATUS_SUCCESS);
                resultInfo.setBlocks(new HashMap<String, EiBlock>() {{
                    put("resultDirectUser", result.getBlock("result"));
                }});
            } else {

                resultInfo.setStatus(EiConstant.STATUS_SUCCESS);
                resultInfo.setMsg("调用S_UN_BI_0017失败");
                return resultInfo;
            }
        } catch (Exception e) {
            // 捕获异常，设置失败状态并返回错误信息
            e.printStackTrace();
            resultInfo.setStatus(EiConstant.STATUS_FAILURE);
            resultInfo.setMsg("查询客商服务失败，原因：" + e.getMessage());
        }

        return resultInfo;
    }

}

import asyncio
from crawl4ai import Async<PERSON>ebCrawler
from crawl4ai.extraction_strategy import <PERSON>sonCssExtractionStrategy, LLMExtractionStrategy
import json
import re

async def debug_page_structure(url):
    """Debug the page structure to understand what selectors to use"""
    print(f"正在分析页面结构: {url}")
    
    async with AsyncWebCrawler(verbose=True) as crawler:
        # First, get the basic page content
        result = await crawler.arun(url=url)
        
        print(f"页面标题: {result.metadata.get('title', 'N/A')}")
        print(f"页面状态码: {result.status_code}")
        print(f"页面大小: {len(result.html)} 字符")
        
        if result.html:
            # Look for common content containers
            html_content = result.html
            
            # Check for common article selectors
            common_selectors = [
                '.articleDetailContent',
                '.article-content',
                '.content',
                '.post-content',
                '.entry-content',
                'article',
                '.main-content',
                '#content',
                '.article-body',
                '.text-content'
            ]
            
            print("\n检查常见的内容选择器:")
            for selector in common_selectors:
                # Simple check if selector exists in HTML
                selector_class = selector.replace('.', '').replace('#', '')
                if selector_class in html_content:
                    print(f"✓ 找到可能的选择器: {selector}")
                else:
                    print(f"✗ 未找到选择器: {selector}")
            
            # Look for headings
            print("\n检查标题元素:")
            for i in range(1, 7):
                h_tag = f"<h{i}"
                count = html_content.count(h_tag)
                if count > 0:
                    print(f"✓ 找到 {count} 个 h{i} 标签")
            
            # Look for paragraphs
            p_count = html_content.count("<p")
            print(f"✓ 找到 {p_count} 个段落标签")
            
            # Look for divs with text content
            div_count = html_content.count("<div")
            print(f"✓ 找到 {div_count} 个 div 标签")
            
            # Save HTML for manual inspection
            with open("page_debug.html", "w", encoding="utf-8") as f:
                f.write(html_content)
            print(f"\n页面HTML已保存到 page_debug.html 供手动检查")
            
            return result
        else:
            print("❌ 无法获取页面内容")
            return None

async def test_different_strategies(url):
    """测试不同的提取策略"""
    print(f"\n正在测试不同的提取策略: {url}")
    
    async with AsyncWebCrawler(verbose=True) as crawler:
        
        # Strategy 1: No extraction strategy (get all text)
        print("\n=== 策略1: 获取所有文本内容 ===")
        result1 = await crawler.arun(url=url)
        if result1.cleaned_html:
            print(f"清理后的HTML长度: {len(result1.cleaned_html)}")
            print(f"前500字符: {result1.cleaned_html[:500]}...")
        
        # Strategy 2: Try different CSS selectors
        print("\n=== 策略2: 尝试不同的CSS选择器 ===")
        
        selectors_to_try = [
            {
                "name": "article_content",
                "baseSelector": "article",
                "fields": [{"name": "content", "selector": "p", "type": "text"}]
            },
            {
                "name": "main_content", 
                "baseSelector": ".content, .main-content, #content",
                "fields": [{"name": "content", "selector": "p, h1, h2, h3", "type": "text"}]
            },
            {
                "name": "any_paragraphs",
                "baseSelector": "body",
                "fields": [{"name": "paragraphs", "selector": "p", "type": "text"}]
            },
            {
                "name": "any_headings",
                "baseSelector": "body", 
                "fields": [{"name": "headings", "selector": "h1, h2, h3, h4, h5, h6", "type": "text"}]
            }
        ]
        
        for schema in selectors_to_try:
            try:
                strategy = JsonCssExtractionStrategy(schema)
                result = await crawler.arun(url=url, extraction_strategy=strategy)
                
                if result.extracted_content:
                    print(f"✓ {schema['name']} 成功提取内容:")
                    content = json.loads(result.extracted_content) if result.extracted_content.startswith('[') or result.extracted_content.startswith('{') else result.extracted_content
                    print(f"  内容: {str(content)[:200]}...")
                else:
                    print(f"✗ {schema['name']} 未提取到内容")
                    
            except Exception as e:
                print(f"✗ {schema['name']} 提取失败: {e}")

async def test_36kr_specific(url):
    """专门针对36kr网站的测试"""
    print(f"\n=== 36kr专用测试 ===")
    
    async with AsyncWebCrawler(verbose=True) as crawler:
        # 36kr specific selectors
        kr36_schemas = [
            {
                "name": "kr36_article_v1",
                "baseSelector": ".articleDetailContent",
                "fields": [
                    {"name": "title", "selector": "h1, h2, .title", "type": "text"},
                    {"name": "content", "selector": "p, .content", "type": "text"}
                ]
            },
            {
                "name": "kr36_article_v2", 
                "baseSelector": "article, .article",
                "fields": [
                    {"name": "title", "selector": "h1, h2", "type": "text"},
                    {"name": "content", "selector": "p", "type": "text"}
                ]
            },
            {
                "name": "kr36_any_content",
                "baseSelector": "body",
                "fields": [
                    {"name": "all_text", "selector": "p, h1, h2, h3, div", "type": "text"}
                ]
            }
        ]
        
        for schema in kr36_schemas:
            try:
                strategy = JsonCssExtractionStrategy(schema)
                result = await crawler.arun(url=url, extraction_strategy=strategy)
                
                if result.extracted_content:
                    print(f"✓ {schema['name']} 成功:")
                    try:
                        content = json.loads(result.extracted_content)
                        print(f"  提取的数据: {content}")
                    except:
                        print(f"  原始内容: {result.extracted_content[:300]}...")
                else:
                    print(f"✗ {schema['name']} 无内容")
                    
            except Exception as e:
                print(f"✗ {schema['name']} 错误: {e}")

async def main():
    url = "https://36kr.com/p/3311645093895936"
    
    # Step 1: Debug page structure
    page_result = await debug_page_structure(url)
    
    if page_result:
        # Step 2: Test different strategies
        await test_different_strategies(url)
        
        # Step 3: Test 36kr specific strategies
        await test_36kr_specific(url)
    
    print("\n=== 调试完成 ===")
    print("请检查生成的 page_debug.html 文件来手动分析页面结构")

if __name__ == "__main__":
    asyncio.run(main())

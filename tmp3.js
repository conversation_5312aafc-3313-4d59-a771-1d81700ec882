function execute(input) {
  const types = {
    A: { file: '', content: '' },
    B: { file: '', content: '' },
    C: { file: '', content: '' }
  };

  for (let i = 1; i <= 3; i++) {
    const type = input[`attachment_${i}_type`];
    if (types[type]) {
      types[type].file = input[`attachment_${i}`];
      types[type].content = input[`attachment_${i}_content`];
    }
  }

  const { A: businessLicense, B: accountRegistration, C: invoiceInformation } = types;

  let status = 0;
  let messages = [];

  if (!accountRegistration.content) {
    status = -5;
    messages.push("开户登记表或委托代理书");
  }

  if (!businessLicense.content) {
    status = -5;
    messages.push("营业执照");
  }

  return {
    status,
    message: messages.length ? "请上传正确的" + messages.join(",") : "",
    account_registration_file: accountRegistration.file,
    account_registration_content: accountRegistration.content,
    business_license_file: businessLicense.file,
    business_license_content: businessLicense.content,
    invoice_information_file: invoiceInformation.file,
    invoice_information_content: invoiceInformation.content
  };
}
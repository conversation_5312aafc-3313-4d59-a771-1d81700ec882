import unittest
import websocket
import json
import uuid
import threading
from unittest.mock import patch
from queue import Queue
from time import time
from datetime import datetime

class FlameAPITester(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.ws_url = "ws://10.81.7.91:30009/openapi/flames/api/v2/chat"
        cls.headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer YOUR_API_KEY"  # 根据实际需求添加
        }
        cls.max_message_queue = 10
        cls.response_queue = Queue(maxsize=cls.max_message_queue)
        
    def generate_trace_id(self):
        """生成唯一的traceId"""
        return f"TRACE-{datetime.now().strftime('%Y%m%d%H%M%S')}-{uuid.uuid4().hex[:8]}"

    def run_websocket(self, payload):
        """独立线程运行WebSocket连接"""
        ws = websocket.WebSocketApp(
            self.ws_url,
            header=self.headers,
            on_message=self.queue_message,
            on_error=self.handle_error,
            on_close=self.handle_close
        )
        
        # 启动消息接收线程
        wst = threading.Thread(
            target=ws.run_forever,
            kwargs={"ping_interval": 30, "ping_timeout": 10}
        )
        wst.daemon = True
        wst.start()
        
        return ws
    
    def queue_message(self, ws, message):
        """将接收到的消息放入队列"""
        try:
            self.response_queue.put_nowait(json.loads(message))
        except Queue.Full:
            print("消息队列已满，丢弃最新消息")

    def handle_error(self, ws, error):
        """错误处理"""
        print(f"WebSocket Error: {str(error)}")
        self.response_queue.put(("ERROR", str(error)))

    def handle_close(self, ws, close_status_code, close_msg):
        """连接关闭处理"""
        print("### WebSocket连接已关闭 ###")
        self.response_queue.put(("CLOSE", close_status_code))

    def test_connection(self):
        """测试WebSocket连接"""
        ws = self.run_websocket({})
        time.sleep(2)  # 等待连接建立
        self.assertIsNotNone(ws.sock)
        ws.close()

    def test_planning_agent(self):
        """测试规划型智能体"""
        test_message = "请解释人工智能的基本概念"
        payload = self.build_planning_payload("SESSION_001", test_message)
        
        ws = self.run_websocket(payload)
        time.sleep(2)  # 等待消息发送
        
        # 验证响应
        valid_responses = 0
        start_time = time.time()
        while time.time() - start_time < 10:
            if not self.response_queue.empty():
                response_type, data = self.response_queue.get()
                if response_type == "MESSAGE":
                    self.assertIn("content", data)
                    valid_responses += 1
                    break
        self.assertGreater(valid_responses, 0)
        ws.close()
        
    def build_header(self, app_id, body_id, mode=0):
        """构建请求头"""
        return {
            "traceId": self.generate_trace_id(),
            "bodyId": body_id,
            "appId": app_id,
            "mode": mode
        }

    def build_planning_payload(self, session_id, text_content):
        """构建规划型/知识型请求体"""
        return {
            "header": self.build_header("9BC895C708EB440D804C", "5ECAE42D56634B49B5F9E1BAC7F65632"),
            "parameter": {},
            "payload": {
                "sessionId": session_id,
                "text": [{
                    "content": text_content,
                    "content_type": "text",
                    "role": "user"
                }]
            }
        }

    def build_task_payload(self, session_id="", form_params=None):
        """构建任务型请求体"""
        return {
            "header": self.build_header("9BC895C708EB440D804C", "5ECAE42D56634B49B5F9E1BAC7F65632"),
            "payload": {
                "sessionId": session_id,
                "text": [{
                    "content_type": "form_input",
                    "role": "user",
                    "formInput": {
                        "parameters": form_params or [{
                            "key": "arg1",
                            "name": "参数1",
                            "type": "string",
                            "required": True,
                            "value": "测试值"
                        }]
                    }
                }]
            }
        }

    def on_message(self, ws, message):
        """消息处理回调"""
        try:
            data = json.loads(message)
            print(f"收到响应: {json.dumps(data, indent=2)}")
            self.response_data = data
        except json.JSONDecodeError:
            print(f"无法解析响应内容: {message}")

    def on_error(self, ws, error):
        """错误处理"""
        print(f"发生错误: {str(error)}")
        self.error_occurred = True

    def on_close(self, ws, close_status_code, close_msg):
        """连接关闭处理"""
        print("### 连接已关闭 ###")

    def on_open(self, ws):
        """连接建立后的处理"""
        print("### 连接已建立 ###")

    def test_connection(self):
        """测试WebSocket连接"""
        with websocket.WebSocketApp(
            self.ws_url,
            header=self.headers,
            on_open=self.on_open,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close
        ) as ws:
            ws.run_forever(timeout=10)

    def test_planning_agent(self):
        """测试规划型智能体"""
        test_message = "请解释人工智能的基本概念"
        payload = self.build_planning_payload("SESSION_001", test_message)
        
        with websocket.WebSocketApp(
            self.ws_url,
            header=self.headers,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close
        ) as ws:
            ws.send(json.dumps(payload))
            ws.run_forever(timeout=10)
            self.assertIsNotNone(self.response_data)

    def test_task_agent_parameters(self):
        """测试任务型参数校验"""
        invalid_params = [{
            "key": "arg1",
            "name": "参数1",
            "type": "string",
            "required": True,
            "value": ""  # 故意留空触发必填校验
        }]
        
        payload = self.build_task_payload(form_params=invalid_params)
        
        with websocket.WebSocketApp(
            self.ws_url,
            header=self.headers,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close
        ) as ws:
            ws.send(json.dumps(payload))
            ws.run_forever(timeout=10)
            self.assertIn("error", self.response_data)

if __name__ == '__main__':
    unittest.main()

    # unittest.main(testRunner=xmlrunner.XMLTestRunner(output='test_reports'))
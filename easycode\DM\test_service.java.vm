## 引入宏定义
$!{baowu.vm}

## 使用宏定义设置回调（保存位置与文件）
$!callback.setSavePath($baowuTestDir+"/service")
$!callback.setFileName("Service"+$entityName+"Test.java")

#setPackageSuffix("service")
$!{autoImport.vm}
import com.baosight.BaseTest;
import com.baosight.iplat4j.core.ei.EiInfo;
import org.junit.Before;
import org.junit.Test;

/**
 * ${tableInfo.comment} 服务测试类
 * @DateTime ${time.currTime('yyyy-MM-dd HH:mm:ss')} 
 * <AUTHOR>
 */
public class Service${entityName}Test extends BaseTest {
    Service${entityName} service${entityName};
    @Before
    public void init() {
        setSessionUser();
        service${entityName} =(Service${entityName})getServiceBean("${entityName}");
    }

    private ${entityName} get${entityName}() {
        ${entityName} ${entityInstanceName} = new ${entityName}();
        #foreach($column in $tableInfo.fullColumn)
        ${entityInstanceName}.set${tool.firstUpperCase($column.name)}(${column.obj.getDefaultValue()});
        #end
        return ${entityInstanceName};
    }

    @Test
    public void testQuery() {
        EiInfo info =service${entityName}.query(new EiInfo(){{

        }});
        System.out.println(info.toJSONString());
    }

    @Test
    public void testInsert() {
                inInfo.getBlock(EiConstant.resultBlock).setRows(rows);

        EiBlock eiBlock=new EiBlock(EiConstant.resultBlock)
                .setRows(
                    new ArrayList<${entityName}>(){{
                        add(get${entityName}());
                    }}
                );
        EiInfo info =service${entityName}.insert(new EiInfo(){{
            setBlock(eiBlock);
        }});
            
        System.out.println(info.toJSONString());
    }

    @Test
    public void testUpdate() {
        EiBlock eiBlock=new EiBlock(EiConstant.resultBlock)
                .setRows(
                    new ArrayList<${entityName}>(){{
                        add(get${entityName}());
                    }}
                );
        EiInfo info =service${entityName}.update(new EiInfo(){{
            setBlock(eiBlock);
        }});
        System.out.println(info.toJSONString());
    }

    @Test
    public void testDelete() {
        EiBlock eiBlock=new EiBlock(EiConstant.resultBlock)
                .setRows(
                    new ArrayList<${entityName}>(){{
                        add(get${entityName}());
                    }}
                );
        EiInfo info =service${entityName}.delete(new EiInfo(){{
            setBlock(eiBlock);
        }});
        System.out.println(info.toJSONString());
    }

}
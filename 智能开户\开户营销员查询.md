# 接口文档：营销员查询

## 请求地址
http://imctest.baogang.info/imc-ce/service/CPBI0104/query


## 请求参数
| 参数名 | 类型   | 是否必填 | 示例值 | 描述       |
|--------|--------|----------|--------|------------|
| userId   | string | 可选       |    | 员工号 |
| userName   | string | 可选       |    | 员工姓名 |

## 请求示例
```json
{
  "__blocks__": {
    "inqu_status": {
      "attr": {},
      "meta": {
        "desc": "",
        "attr": {},
        "columns": [
          {
            "pos": 0,
            "name": "userId"
          },
          {
            "pos": 1,
            "name": "userName"
          }
        ]
      },
      "rows": [
        [
          "YG0852",
          ""
        ]
      ]
    }
  }
}
```

## 响应结果
```json
{
    "serviceType": "",
    "__resAppEname__": "imc-ce",
    "$$remote$$": "false",
    "methodName": "queryUserId",
    "soaInvokeProtocol": "local",
    "urlAdress": "http://imctest.baogang.info:80/imc-cd/service",
    "serviceName": "UNBI0000",
    "__blocks__": {
        "inqu_status": {
            "meta": {
                "columns": [
                    {
                        "pos": 0,
                        "name": "userId",
                        "descName": " "
                    },
                    {
                        "pos": 1,
                        "name": "userName",
                        "descName": " "
                    }
                ],
                "attr": {},
                "desc": ""
            },
            "attr": {},
            "rows": [
                [
                    "YG0852",
                    ""
                ]
            ]
        },
        "result": {
            "meta": {
                "columns": [
                    {
                        "pos": 0,
                        "name": "segNo",
                        "descName": " "
                    },
                    {
                        "pos": 1,
                        "name": "segFullName",
                        "descName": " "
                    },
                    {
                        "pos": 2,
                        "name": "mobile",
                        "descName": " "
                    },
                    {
                        "pos": 3,
                        "name": "userName",
                        "descName": " "
                    },
                    {
                        "pos": 4,
                        "name": "userId",
                        "descName": " "
                    },
                    {
                        "pos": 5,
                        "name": "email",
                        "descName": " "
                    }
                ]
            },
            "attr": {
                "offset": 0,
                "count": 0,
                "limit": 10
            },
            "rows": [
                [
                    null,
                    null,
                    "15800672074",
                    "韦宾宾",
                    "YG0852",
                    "<EMAIL>"
                ]
            ]
        }
    },
    "__resProjectEname__": "crm",
    "__sys__": {
        "msg": "查询成功，本次查询返回1条记录！",
        "traceId": "144b0001f1750125288844000025d4",
        "detailMsg": "",
        "msgKey": "ep.2000",
        "status": 0
    },
    "__version__": "2.0",
    "serviceId": "S_UN_BI_4605",
    "UUID": "3fd48ebc-b6a0-4154-bc5a-d5666a2956e9"
}
```
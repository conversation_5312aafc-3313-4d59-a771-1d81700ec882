package com.baosight.imc.xb.ai.domain;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.util.NumberUtils;
import com.baosight.iplat4j.core.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Project: <br>
 * Title:Txbai16.java <br>
 * Description: 订货规则配置（走访分析用）表<br>
 *
 * Copyrigth:Baosight Software LTD.co Copyright (c) 2019. <br>
 *
 * @version 1.0
 * @history 2025-04-15 16:40:05 create
 */
public class XBAI16 extends DaoEPBase {

	private static final long serialVersionUID = 1L;

	public static final String FIELD_UUID = "uuid";    		/* ID*/
	public static final String FIELD_REC_CREATOR = "recCreator";    		/* 记录创建人*/
	public static final String FIELD_REC_CREATOR_NAME = "recCreatorName";    		/* 记录创建人姓名*/
	public static final String FIELD_REC_CREATE_TIME = "recCreateTime";    		/* 记录创建时间*/
	public static final String FIELD_REC_REVISOR = "recRevisor";    		/* 记录修改人*/
	public static final String FIELD_REC_REVISOR_NAME = "recRevisorName";    		/* 记录修改人姓名*/
	public static final String FIELD_REC_REVISE_TIME = "recReviseTime";    		/* 记录修改时间*/
	public static final String FIELD_ARCHIVE_FLAG = "archiveFlag";    		/* 归档标记*/
	public static final String FIELD_DEL_FLAG = "delFlag";    		/* 记录删除标记*/
	public static final String FIELD_TENANT_USER = "tenantUser";    		/* 租户*/
	public static final String FIELD_UNIT_CODE = "unitCode";    		/* 业务单元代码*/
	public static final String FIELD_SEG_NO = "segNo";    		/* 系统账套*/
	public static final String FIELD_DIRECT_USER_NUM = "directUserNum";    		/* 订货用户代码*/
	public static final String FIELD_DIRECT_USER_NAME = "directUserName";    		/* 订货用户名称*/
	public static final String FIELD_FIN_USER_ID = "finUserId";    		/* 最终用户代码*/
	public static final String FIELD_FIN_USER_NAME = "finUserName";    		/* 最终用户名称*/

	public static final String FIELD_CUSTOM_COMPANY = "customCompany";    		/* 走访用户*/

	public static final String COL_UUID = "UUID";    		/* ID*/
	public static final String COL_REC_CREATOR = "REC_CREATOR";    		/* 记录创建人*/
	public static final String COL_REC_CREATOR_NAME = "REC_CREATOR_NAME";    		/* 记录创建人姓名*/
	public static final String COL_REC_CREATE_TIME = "REC_CREATE_TIME";    		/* 记录创建时间*/
	public static final String COL_REC_REVISOR = "REC_REVISOR";    		/* 记录修改人*/
	public static final String COL_REC_REVISOR_NAME = "REC_REVISOR_NAME";    		/* 记录修改人姓名*/
	public static final String COL_REC_REVISE_TIME = "REC_REVISE_TIME";    		/* 记录修改时间*/
	public static final String COL_ARCHIVE_FLAG = "ARCHIVE_FLAG";    		/* 归档标记*/
	public static final String COL_DEL_FLAG = "DEL_FLAG";    		/* 记录删除标记*/
	public static final String COL_TENANT_USER = "TENANT_USER";    		/* 租户*/
	public static final String COL_UNIT_CODE = "UNIT_CODE";    		/* 业务单元代码*/
	public static final String COL_SEG_NO = "SEG_NO";    		/* 系统账套*/
	public static final String COL_DIRECT_USER_NUM = "DIRECT_USER_NUM";    		/* 订货用户代码*/
	public static final String COL_DIRECT_USER_NAME = "DIRECT_USER_NAME";    		/* 订货用户名称*/
	public static final String COL_FIN_USER_ID = "FIN_USER_ID";    		/* 最终用户代码*/
	public static final String COL_FIN_USER_NAME = "FIN_USER_NAME";    		/* 最终用户名称*/

	public static final String COL_CUSTOM_COMPANY = "CUSTOM_COMPANY";    		/* 走访用户*/


	public static final String QUERY = "txbai16.query";
	public static final String COUNT = "txbai16.count";
	public static final String INSERT = "txbai16.insert";
	public static final String UPDATE = "txbai16.update";
	public static final String DELETE = "txbai16.delete";

	private String uuid = "";		/* ID*/
	private String recCreator = "";		/* 记录创建人*/
	private String recCreatorName = "";		/* 记录创建人姓名*/
	private String recCreateTime = "";		/* 记录创建时间*/
	private String recRevisor = "";		/* 记录修改人*/
	private String recRevisorName = "";		/* 记录修改人姓名*/
	private String recReviseTime = "";		/* 记录修改时间*/
	private String archiveFlag = "";		/* 归档标记*/
	private Integer delFlag = new Integer(0);		/* 记录删除标记*/
	private String tenantUser = "";		/* 租户*/
	private String unitCode = "";		/* 业务单元代码*/
	private String segNo = "";		/* 系统账套*/
	private String directUserNum = "";		/* 订货用户代码*/
	private String directUserName = "";		/* 订货用户名称*/
	private String finUserId = "";		/* 最终用户代码*/
	private String finUserName = "";		/* 最终用户名称*/

	private String customCompany = "";		/* 走访用户*/

	/**
	 * initialize the metadata.
	 */
	public void initMetaData() {
		EiColumn eiColumn;

		eiColumn = new EiColumn(FIELD_UUID);
		eiColumn.setFieldLength(32);
		eiColumn.setDescName("ID");
		eiMetadata.addMeta(eiColumn);

		eiColumn = new EiColumn(FIELD_REC_CREATOR);
		eiColumn.setFieldLength(32);
		eiColumn.setDescName("记录创建人");
		eiMetadata.addMeta(eiColumn);

		eiColumn = new EiColumn(FIELD_REC_CREATOR_NAME);
		eiColumn.setFieldLength(100);
		eiColumn.setDescName("记录创建人姓名");
		eiMetadata.addMeta(eiColumn);

		eiColumn = new EiColumn(FIELD_REC_CREATE_TIME);
		eiColumn.setFieldLength(17);
		eiColumn.setDescName("记录创建时间");
		eiMetadata.addMeta(eiColumn);

		eiColumn = new EiColumn(FIELD_REC_REVISOR);
		eiColumn.setFieldLength(32);
		eiColumn.setDescName("记录修改人");
		eiMetadata.addMeta(eiColumn);

		eiColumn = new EiColumn(FIELD_REC_REVISOR_NAME);
		eiColumn.setFieldLength(100);
		eiColumn.setDescName("记录修改人姓名");
		eiMetadata.addMeta(eiColumn);

		eiColumn = new EiColumn(FIELD_REC_REVISE_TIME);
		eiColumn.setFieldLength(17);
		eiColumn.setDescName("记录修改时间");
		eiMetadata.addMeta(eiColumn);

		eiColumn = new EiColumn(FIELD_ARCHIVE_FLAG);
		eiColumn.setFieldLength(1);
		eiColumn.setDescName("归档标记");
		eiMetadata.addMeta(eiColumn);

		eiColumn = new EiColumn(FIELD_DEL_FLAG);
		eiColumn.setDescName("记录删除标记");
		eiMetadata.addMeta(eiColumn);

		eiColumn = new EiColumn(FIELD_TENANT_USER);
		eiColumn.setFieldLength(10);
		eiColumn.setDescName("租户");
		eiMetadata.addMeta(eiColumn);

		eiColumn = new EiColumn(FIELD_UNIT_CODE);
		eiColumn.setFieldLength(10);
		eiColumn.setDescName("业务单元代码");
		eiMetadata.addMeta(eiColumn);

		eiColumn = new EiColumn(FIELD_SEG_NO);
		eiColumn.setFieldLength(10);
		eiColumn.setDescName("系统账套");
		eiMetadata.addMeta(eiColumn);

		eiColumn = new EiColumn(FIELD_DIRECT_USER_NUM);
		eiColumn.setFieldLength(20);
		eiColumn.setDescName("订货用户代码");
		eiMetadata.addMeta(eiColumn);

		eiColumn = new EiColumn(FIELD_DIRECT_USER_NAME);
		eiColumn.setFieldLength(100);
		eiColumn.setDescName("订货用户名称");
		eiMetadata.addMeta(eiColumn);

		eiColumn = new EiColumn(FIELD_FIN_USER_ID);
		eiColumn.setFieldLength(20);
		eiColumn.setDescName("最终用户代码");
		eiMetadata.addMeta(eiColumn);

		eiColumn = new EiColumn(FIELD_FIN_USER_NAME);
		eiColumn.setFieldLength(100);
		eiColumn.setDescName("最终用户名称");
		eiMetadata.addMeta(eiColumn);

		eiColumn = new EiColumn(FIELD_CUSTOM_COMPANY);
		eiColumn.setFieldLength(100);
		eiColumn.setDescName("走访用户名称");
		eiMetadata.addMeta(eiColumn);


	}

	/**
	 * the constructor.
	 */
	public XBAI16() {
		initMetaData();
	}

	/**
	 * get the uuid - ID.
	 * @return the uuid
	 */
	public String getUuid() {
		return this.uuid;
	}

	/**
	 * set the uuid - ID.
	 *
	 * @param uuid - ID
	 */
	public void setUuid(String uuid) {
		this.uuid = uuid;
	}
	/**
	 * get the recCreator - 记录创建人.
	 * @return the recCreator
	 */
	public String getRecCreator() {
		return this.recCreator;
	}

	/**
	 * set the recCreator - 记录创建人.
	 *
	 * @param recCreator - 记录创建人
	 */
	@Override
	public void setRecCreator(String recCreator) {
		this.recCreator = recCreator;
	}
	/**
	 * get the recCreatorName - 记录创建人姓名.
	 * @return the recCreatorName
	 */
	public String getRecCreatorName() {
		return this.recCreatorName;
	}

	/**
	 * set the recCreatorName - 记录创建人姓名.
	 *
	 * @param recCreatorName - 记录创建人姓名
	 */
	public void setRecCreatorName(String recCreatorName) {
		this.recCreatorName = recCreatorName;
	}
	/**
	 * get the recCreateTime - 记录创建时间.
	 * @return the recCreateTime
	 */
	public String getRecCreateTime() {
		return this.recCreateTime;
	}

	/**
	 * set the recCreateTime - 记录创建时间.
	 *
	 * @param recCreateTime - 记录创建时间
	 */
	@Override
	public void setRecCreateTime(String recCreateTime) {
		this.recCreateTime = recCreateTime;
	}
	/**
	 * get the recRevisor - 记录修改人.
	 * @return the recRevisor
	 */
	public String getRecRevisor() {
		return this.recRevisor;
	}

	/**
	 * set the recRevisor - 记录修改人.
	 *
	 * @param recRevisor - 记录修改人
	 */
	@Override
	public void setRecRevisor(String recRevisor) {
		this.recRevisor = recRevisor;
	}
	/**
	 * get the recRevisorName - 记录修改人姓名.
	 * @return the recRevisorName
	 */
	public String getRecRevisorName() {
		return this.recRevisorName;
	}

	/**
	 * set the recRevisorName - 记录修改人姓名.
	 *
	 * @param recRevisorName - 记录修改人姓名
	 */
	public void setRecRevisorName(String recRevisorName) {
		this.recRevisorName = recRevisorName;
	}
	/**
	 * get the recReviseTime - 记录修改时间.
	 * @return the recReviseTime
	 */
	public String getRecReviseTime() {
		return this.recReviseTime;
	}

	/**
	 * set the recReviseTime - 记录修改时间.
	 *
	 * @param recReviseTime - 记录修改时间
	 */
	@Override
	public void setRecReviseTime(String recReviseTime) {
		this.recReviseTime = recReviseTime;
	}
	/**
	 * get the archiveFlag - 归档标记.
	 * @return the archiveFlag
	 */
	public String getArchiveFlag() {
		return this.archiveFlag;
	}

	/**
	 * set the archiveFlag - 归档标记.
	 *
	 * @param archiveFlag - 归档标记
	 */
	@Override
	public void setArchiveFlag(String archiveFlag) {
		this.archiveFlag = archiveFlag;
	}
	/**
	 * get the delFlag - 记录删除标记.
	 * @return the delFlag
	 */
	public Integer getDelFlag() {
		return this.delFlag;
	}

	/**
	 * set the delFlag - 记录删除标记.
	 *
	 * @param delFlag - 记录删除标记
	 */
	public void setDelFlag(Integer delFlag) {
		this.delFlag = delFlag;
	}
	/**
	 * get the tenantUser - 租户.
	 * @return the tenantUser
	 */
	public String getTenantUser() {
		return this.tenantUser;
	}

	/**
	 * set the tenantUser - 租户.
	 *
	 * @param tenantUser - 租户
	 */
	public void setTenantUser(String tenantUser) {
		this.tenantUser = tenantUser;
	}
	/**
	 * get the unitCode - 业务单元代码.
	 * @return the unitCode
	 */
	public String getUnitCode() {
		return this.unitCode;
	}

	/**
	 * set the unitCode - 业务单元代码.
	 *
	 * @param unitCode - 业务单元代码
	 */
	public void setUnitCode(String unitCode) {
		this.unitCode = unitCode;
	}
	/**
	 * get the segNo - 系统账套.
	 * @return the segNo
	 */
	public String getSegNo() {
		return this.segNo;
	}

	/**
	 * set the segNo - 系统账套.
	 *
	 * @param segNo - 系统账套
	 */
	public void setSegNo(String segNo) {
		this.segNo = segNo;
	}
	/**
	 * get the directUserNum - 订货用户代码.
	 * @return the directUserNum
	 */
	public String getDirectUserNum() {
		return this.directUserNum;
	}

	/**
	 * set the directUserNum - 订货用户代码.
	 *
	 * @param directUserNum - 订货用户代码
	 */
	public void setDirectUserNum(String directUserNum) {
		this.directUserNum = directUserNum;
	}
	/**
	 * get the directUserName - 订货用户名称.
	 * @return the directUserName
	 */
	public String getDirectUserName() {
		return this.directUserName;
	}

	/**
	 * set the directUserName - 订货用户名称.
	 *
	 * @param directUserName - 订货用户名称
	 */
	public void setDirectUserName(String directUserName) {
		this.directUserName = directUserName;
	}
	/**
	 * get the finUserId - 最终用户代码.
	 * @return the finUserId
	 */
	public String getFinUserId() {
		return this.finUserId;
	}

	/**
	 * set the finUserId - 最终用户代码.
	 *
	 * @param finUserId - 最终用户代码
	 */
	public void setFinUserId(String finUserId) {
		this.finUserId = finUserId;
	}
	/**
	 * get the finUserName - 最终用户名称.
	 * @return the finUserName
	 */
	public String getFinUserName() {
		return this.finUserName;
	}

	/**
	 * set the finUserName - 最终用户名称.
	 *
	 * @param finUserName - 最终用户名称
	 */
	public void setFinUserName(String finUserName) {
		this.finUserName = finUserName;
	}

	public String getCustomCompany() {
		return this.customCompany;
	}

	public void setCustomCompany(String customCompany) {
		this.customCompany = customCompany;
	}

	/**
	 * get the value from Map.
	 *
	 * @param map - source data map
	 */
	@Override
	public void fromMap(Map map) {

		setUuid(StringUtils.defaultIfEmpty(StringUtils.toString(map.get(FIELD_UUID)), uuid));
		setRecCreator(StringUtils.defaultIfEmpty(StringUtils.toString(map.get(FIELD_REC_CREATOR)), recCreator));
		setRecCreatorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get(FIELD_REC_CREATOR_NAME)), recCreatorName));
		setRecCreateTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get(FIELD_REC_CREATE_TIME)), recCreateTime));
		setRecRevisor(StringUtils.defaultIfEmpty(StringUtils.toString(map.get(FIELD_REC_REVISOR)), recRevisor));
		setRecRevisorName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get(FIELD_REC_REVISOR_NAME)), recRevisorName));
		setRecReviseTime(StringUtils.defaultIfEmpty(StringUtils.toString(map.get(FIELD_REC_REVISE_TIME)), recReviseTime));
		setArchiveFlag(StringUtils.defaultIfEmpty(StringUtils.toString(map.get(FIELD_ARCHIVE_FLAG)), archiveFlag));
		setDelFlag(NumberUtils.toInteger(StringUtils.toString(map.get(FIELD_DEL_FLAG)), delFlag));
		setTenantUser(StringUtils.defaultIfEmpty(StringUtils.toString(map.get(FIELD_TENANT_USER)), tenantUser));
		setUnitCode(StringUtils.defaultIfEmpty(StringUtils.toString(map.get(FIELD_UNIT_CODE)), unitCode));
		setSegNo(StringUtils.defaultIfEmpty(StringUtils.toString(map.get(FIELD_SEG_NO)), segNo));
		setDirectUserNum(StringUtils.defaultIfEmpty(StringUtils.toString(map.get(FIELD_DIRECT_USER_NUM)), directUserNum));
		setDirectUserName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get(FIELD_DIRECT_USER_NAME)), directUserName));
		setFinUserId(StringUtils.defaultIfEmpty(StringUtils.toString(map.get(FIELD_FIN_USER_ID)), finUserId));
		setFinUserName(StringUtils.defaultIfEmpty(StringUtils.toString(map.get(FIELD_FIN_USER_NAME)), finUserName));
		setCustomCompany(StringUtils.defaultIfEmpty(StringUtils.toString(map.get(FIELD_CUSTOM_COMPANY)), customCompany));
	}

	/**
	 * set the value to Map.
	 */
	@Override
	public Map toMap() {

		Map map = new HashMap();
		map.put(FIELD_UUID, StringUtils.toString(uuid, eiMetadata.getMeta(FIELD_UUID)));
		map.put(FIELD_REC_CREATOR, StringUtils.toString(recCreator, eiMetadata.getMeta(FIELD_REC_CREATOR)));
		map.put(FIELD_REC_CREATOR_NAME, StringUtils.toString(recCreatorName, eiMetadata.getMeta(FIELD_REC_CREATOR_NAME)));
		map.put(FIELD_REC_CREATE_TIME, StringUtils.toString(recCreateTime, eiMetadata.getMeta(FIELD_REC_CREATE_TIME)));
		map.put(FIELD_REC_REVISOR, StringUtils.toString(recRevisor, eiMetadata.getMeta(FIELD_REC_REVISOR)));
		map.put(FIELD_REC_REVISOR_NAME, StringUtils.toString(recRevisorName, eiMetadata.getMeta(FIELD_REC_REVISOR_NAME)));
		map.put(FIELD_REC_REVISE_TIME, StringUtils.toString(recReviseTime, eiMetadata.getMeta(FIELD_REC_REVISE_TIME)));
		map.put(FIELD_ARCHIVE_FLAG, StringUtils.toString(archiveFlag, eiMetadata.getMeta(FIELD_ARCHIVE_FLAG)));
		map.put(FIELD_DEL_FLAG, StringUtils.toString(delFlag, eiMetadata.getMeta(FIELD_DEL_FLAG)));
		map.put(FIELD_TENANT_USER, StringUtils.toString(tenantUser, eiMetadata.getMeta(FIELD_TENANT_USER)));
		map.put(FIELD_UNIT_CODE, StringUtils.toString(unitCode, eiMetadata.getMeta(FIELD_UNIT_CODE)));
		map.put(FIELD_SEG_NO, StringUtils.toString(segNo, eiMetadata.getMeta(FIELD_SEG_NO)));
		map.put(FIELD_DIRECT_USER_NUM, StringUtils.toString(directUserNum, eiMetadata.getMeta(FIELD_DIRECT_USER_NUM)));
		map.put(FIELD_DIRECT_USER_NAME, StringUtils.toString(directUserName, eiMetadata.getMeta(FIELD_DIRECT_USER_NAME)));
		map.put(FIELD_FIN_USER_ID, StringUtils.toString(finUserId, eiMetadata.getMeta(FIELD_FIN_USER_ID)));
		map.put(FIELD_FIN_USER_NAME, StringUtils.toString(finUserName, eiMetadata.getMeta(FIELD_FIN_USER_NAME)));
		map.put(FIELD_CUSTOM_COMPANY, StringUtils.toString(customCompany, eiMetadata.getMeta(FIELD_CUSTOM_COMPANY)));
		return map;
	}
}

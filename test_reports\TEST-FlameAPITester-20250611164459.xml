<?xml version="1.0" ?>
<testsuite name="FlameAPITester-20250611164459" tests="1" time="2.005" failures="0" errors="1">
	<testcase classname="FlameAPITester" name="test_planning_agent" time="2.005">
		<error type="AssertionError" message="测试过程中发生错误: [('ERROR', 'WebSocket错误: Handshake status 401 Unauthorized -+-+- {\'server\': \'nginx/1.21.5\', \'date\': \'Wed, 11 Jun 2025 08:44:59 GMT\', \'content-type\': \'application/json;charset=UTF-8\', \'content-length\': \'70\', \'connection\': \'keep-alive\', \'cache-control\': \'no-cache, no-store, max-age=0, must-revalidate\', \'pragma\': \'no-cache\', \'expires\': \'0\', \'x-content-type-options\': \'nosniff\', \'x-frame-options\': \'DENY\', \'x-xss-protection\': \'1 ; mode=block\', \'referrer-policy\': \'no-referrer\'} -+-+- b\'{&quot;code&quot;:401,&quot;data&quot;:null,&quot;message&quot;:&quot;\\xe8\\xaf\\xb7\\xe6\\xb1\\x82\\xe6\\x8e\\xa5\\xe5\\x8f\\xa3\\xe6\\x9c\\xaa\\xe6\\x8e\\x88\\xe6\\x9d\\x83\\xe6\\x88\\x96\\xe4\\xb8\\x8d\\xe5\\xad\\x98\\xe5\\x9c\\xa8&quot;}\'')]"><![CDATA[Traceback (most recent call last):
  File "c:\vs_project\test\智能开户\test\test2.py", line 293, in test_planning_agent
    self.fail(f"测试过程中发生错误: {all_responses}")
AssertionError: 测试过程中发生错误: [('ERROR', 'WebSocket错误: Handshake status 401 Unauthorized -+-+- {\'server\': \'nginx/1.21.5\', \'date\': \'Wed, 11 Jun 2025 08:44:59 GMT\', \'content-type\': \'application/json;charset=UTF-8\', \'content-length\': \'70\', \'connection\': \'keep-alive\', \'cache-control\': \'no-cache, no-store, max-age=0, must-revalidate\', \'pragma\': \'no-cache\', \'expires\': \'0\', \'x-content-type-options\': \'nosniff\', \'x-frame-options\': \'DENY\', \'x-xss-protection\': \'1 ; mode=block\', \'referrer-policy\': \'no-referrer\'} -+-+- b\'{"code":401,"data":null,"message":"\\xe8\\xaf\\xb7\\xe6\\xb1\\x82\\xe6\\x8e\\xa5\\xe5\\x8f\\xa3\\xe6\\x9c\\xaa\\xe6\\x8e\\x88\\xe6\\x9d\\x83\\xe6\\x88\\x96\\xe4\\xb8\\x8d\\xe5\\xad\\x98\\xe5\\x9c\\xa8"}\'')]
]]></error>
	</testcase>
	<system-out><![CDATA[WebSocket错误: Handshake status 401 Unauthorized -+-+- {'server': 'nginx/1.21.5', 'date': 'Wed, 11 Jun 2025 08:44:59 GMT', 'content-type': 'application/json;charset=UTF-8', 'content-length': '70', 'connection': 'keep-alive', 'cache-control': 'no-cache, no-store, max-age=0, must-revalidate', 'pragma': 'no-cache', 'expires': '0', 'x-content-type-options': 'nosniff', 'x-frame-options': 'DENY', 'x-xss-protection': '1 ; mode=block', 'referrer-policy': 'no-referrer'} -+-+- b'{"code":401,"data":null,"message":"\xe8\xaf\xb7\xe6\xb1\x82\xe6\x8e\xa5\xe5\x8f\xa3\xe6\x9c\xaa\xe6\x8e\x88\xe6\x9d\x83\xe6\x88\x96\xe4\xb8\x8d\xe5\xad\x98\xe5\x9c\xa8"}'
====================错误==================
错误详情: WebSocket错误: Handshake status 401 Unauthorized -+-+- {'server': 'nginx/1.21.5', 'date': 'Wed, 11 Jun 2025 08:44:59 GMT', 'content-type': 'application/json;charset=UTF-8', 'content-length': '70', 'connection': 'keep-alive', 'cache-control': 'no-cache, no-store, max-age=0, must-revalidate', 'pragma': 'no-cache', 'expires': '0', 'x-content-type-options': 'nosniff', 'x-frame-options': 'DENY', 'x-xss-protection': '1 ; mode=block', 'referrer-policy': 'no-referrer'} -+-+- b'{"code":401,"data":null,"message":"\xe8\xaf\xb7\xe6\xb1\x82\xe6\x8e\xa5\xe5\x8f\xa3\xe6\x9c\xaa\xe6\x8e\x88\xe6\x9d\x83\xe6\x88\x96\xe4\xb8\x8d\xe5\xad\x98\xe5\x9c\xa8"}'

测试结果:
- 总响应数: 1
- 是否有错误: True
- 是否有有效响应: False
- 测试用时: 0.00秒
- 所有响应: [('ERROR', 'WebSocket错误: Handshake status 401 Unauthorized -+-+- {\'server\': \'nginx/1.21.5\', \'date\': \'Wed, 11 Jun 2025 08:44:59 GMT\', \'content-type\': \'application/json;charset=UTF-8\', \'content-length\': \'70\', \'connection\': \'keep-alive\', \'cache-control\': \'no-cache, no-store, max-age=0, must-revalidate\', \'pragma\': \'no-cache\', \'expires\': \'0\', \'x-content-type-options\': \'nosniff\', \'x-frame-options\': \'DENY\', \'x-xss-protection\': \'1 ; mode=block\', \'referrer-policy\': \'no-referrer\'} -+-+- b\'{"code":401,"data":null,"message":"\\xe8\\xaf\\xb7\\xe6\\xb1\\x82\\xe6\\x8e\\xa5\\xe5\\x8f\\xa3\\xe6\\x9c\\xaa\\xe6\\x8e\\x88\\xe6\\x9d\\x83\\xe6\\x88\\x96\\xe4\\xb8\\x8d\\xe5\\xad\\x98\\xe5\\x9c\\xa8"}\'')]
]]></system-out>
	<system-err><![CDATA[]]></system-err>
</testsuite>

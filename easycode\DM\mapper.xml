## 引入宏定义
$!{baowu.vm}

## 使用宏定义设置回调（保存位置与文件）
$!callback.setSavePath($tableInfo.savePath+"/sql")
$!callback.setFileName($entityName+".xml")

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<!--    table information
		Generate time : ${time.currTime('yyyy-MM-dd HH:mm:ss')}
   		Version :  1.0
		table comment : ${tableInfo.comment}
		schema : ${dasUtil.getSchema($tableInfo.obj)}
		tableName : ${tableInfo.obj.getName()}
#foreach($column in $tableInfo.fullColumn)
		$!column.obj.name  ${column.obj.getDataType()}  default ${column.obj.getDefault()} #if($column.obj.isNotNull()) NOT #end NULL, 
#end
	-->
<sqlMap namespace="${entityName}">

	<sql id="condition">
#foreach($column in $tableInfo.fullColumn)
		<isNotEmpty prepend=" AND " property="${column.name}">
			$!column.obj.name = #${column.name}#
		</isNotEmpty>
#end
	</sql>

	<select id="duplication" parameterClass="java.util.HashMap"
			resultClass="${tableInfo.savePackageName}.${entityName}">
		SELECT
#foreach($column in $tableInfo.fullColumn)
			$!column.obj.name	as "${column.name}",  <!-- ${column.comment} -->
#end
		FROM ${dasUtil.getSchema($tableInfo.obj)}.${tableInfo.obj.getName()} WHERE 1=1 
#foreach($column in $tableInfo.fullColumn)
			AND $!column.obj.name = #${column.name}#
#end

		<isNotEmpty prepend=" AND " property="notContain$!pkFirstUpperCase">
			$!pk.obj.name  != #notContain$!pkFirstUpperCase#
		</isNotEmpty>

		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
    		  $orderBy$
			</isNotEmpty>
		</dynamic>

	</select>

	<select id="query" parameterClass="java.util.HashMap"
			resultClass="${tableInfo.savePackageName}.${tableInfo.name.toUpperCase()}">
		SELECT
#foreach($column in $tableInfo.fullColumn)
		$!column.obj.name	as "${column.name}",  <!-- ${column.comment} -->
#end
		FROM ${dasUtil.getSchema($tableInfo.obj)}.${tableInfo.obj.getName()} WHERE 1=1
		<include refid="condition" />
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
		</dynamic>

	</select>

	<select id="count" resultClass="int">
		SELECT COUNT(*) FROM ${dasUtil.getSchema($tableInfo.obj)}.${tableInfo.obj.getName()} WHERE 1=1
    <include refid="condition" />
	</select>

	<insert id="insert">
		INSERT INTO ${dasUtil.getSchema($tableInfo.obj)}.${tableInfo.obj.getName()} (
#foreach($column in $tableInfo.fullColumn)
			$!column.obj.name #if($foreach.hasNext),#end <!-- ${column.comment} -->
#end
			)
	    VALUES (
#foreach($column in $tableInfo.fullColumn)
			#${column.name}:$!column.ext.jdbcType# #if($foreach.hasNext),#end
#end
			)
	</insert>

	<delete id="delete">
		DELETE FROM ${dasUtil.getSchema($tableInfo.obj)}.${tableInfo.obj.getName()} WHERE $!pk.obj.name = #$!pk.name#
	</delete>

	<update id="update">
		UPDATE ${dasUtil.getSchema($tableInfo.obj)}.${tableInfo.obj.getName()}
		SET
#foreach($column in $tableInfo.otherColumn)
			$!column.obj.name	= #${column.name}# #if($foreach.hasNext),#end   <!-- ${column.comment} -->
#end
		WHERE
            $!pk.obj.name = #$!pk.name#
            
	</update>

</sqlMap>

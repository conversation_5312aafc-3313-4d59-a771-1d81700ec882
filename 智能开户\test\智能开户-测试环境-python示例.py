# coding: utf-8
import _thread as thread
import base64
import hashlib
import hmac
import json
import os
import requests
import ssl
import uuid
import websocket
from datetime import datetime
from time import mktime
from urllib.parse import urlencode
from urllib.parse import urlparse
from wsgiref.handlers import format_date_time


class FlamesChatClientV2(object):

    # 初始化
    def __init__(self, app_id, app_secret, base_url, body_id):
        self.app_id = app_id
        self.app_secret = app_secret
        self.body_id = body_id
        self.host = urlparse(base_url).hostname
        self.base_url = base_url
        # chat会话接口地址
        self.chat_endpoint = "/openapi/flames/api/v2/chat"
        # 文件上传接口地址
        self.upload_endpoint = "/openapi/flames/file/v2/upload"

    # 生成url,拼接API网关核心鉴权签名信息
    def create_url(self, method, path, wsProtocol):
        # 生成RFC1123格式的时间戳
        now = datetime.now()
        date = format_date_time(mktime(now.timetuple()))

        # 拼接字符串
        signature_origin = "host: {}\ndate: {}\n{} {} HTTP/1.1".format(self.host, date, method, path)

        # 进行hmac-sha256进行加密
        signature_sha = hmac.new(self.app_secret.encode('utf-8'), signature_origin.encode('utf-8'),
                                 digestmod=hashlib.sha256).digest()

        signature_sha_base64 = base64.b64encode(signature_sha).decode(encoding='utf-8')

        authorization_origin = f'hmac api_key="{self.app_id}", algorithm="hmac-sha256", headers="host date request-line", signature="{signature_sha_base64}"'

        authorization = base64.b64encode(authorization_origin.encode('utf-8')).decode(encoding='utf-8')

        # 将请求的鉴权参数组合为字典
        v = {
            "authorization": authorization,
            "date": date,
            "host": self.host,
            "bodyId": self.body_id
        }
        base_url = self.base_url.replace("https", "wss").replace("http", "ws") if wsProtocol else self.base_url
        # 拼接鉴权参数，生成url
        url = base_url + path + '?' + urlencode(v)
        # 此处打印出建立连接时候的url,参考本demo的时候可取消上方打印的注释，比对相同参数时生成的url与自己代码生成的url是否一致
        return url

    def upload(self, file_path):
        request_url = self.create_url("POST", self.upload_endpoint, False)
        print("### upload ### request_url:", request_url)
        _, file_name = os.path.split(file_path)
        file = open(file_path, 'rb')
        file_base64_str = base64.b64encode(file.read()).decode('utf-8')
        body = {
            "payload": {
                "fileName": file_name,
                "file": file_base64_str
            }
        }
        response = requests.post(request_url, json=body, headers={'content-type': "application/json"},
                                 verify=False)
        print('response:', response.text)
        data = json.loads(response.text)
        code = data["header"]["code"]
        if code != 0:
            print(f'请求错误: {code}, {data}')
            return
        else:
            file_id = data["payload"]["id"]
        return file_id

    # 建立连接, 生成内容
    def generate(self, params):
        request_url = self.create_url("GET", self.chat_endpoint, True)
        print("### generate ### request_url:", request_url)
        websocket.enableTrace(False)
        ws = websocket.WebSocketApp(
            request_url,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close,
            on_open=on_open
        )
        ws.app_id = self.app_id
        ws.body_id = self.body_id
        ws.params = params
        ws.run_forever(
            sslopt={
                "cert_reqs": ssl.CERT_NONE
            }
        )


# 收到websocket错误的处理
def on_error(ws, error):
    print("### on_error:", error)


# 收到websocket关闭的处理
def on_close(ws, close_status_code, close_msg):
    print("### on_close ### code:", close_status_code, " msg:", close_msg)


# 收到websocket连接建立的处理
def on_open(ws):
    print("### on_open ###")
    request_params = json.dumps(ws.params)
    print("### request:", request_params)
    ws.send(request_params)


# 收到websocket消息的处理
def on_message(ws, message):
    # print("### on_message:", message)
    data = json.loads(message)
    if data["payload"] and data["payload"]["choices"] and data["payload"]["choices"]["text"]:
        for res in data["payload"]["choices"]["text"]:
            if res["role"]=="assistant" and not "reasoning_content"  in res:
                # print(res["content"], end='')
                print(json.dumps(res, ensure_ascii=False))


# 入口函数
if __name__ == "__main__":
    TRACE_ID = str(uuid.uuid1()).replace("-", "")
    # 开发平台访问地址
    # BASE_URL = "http://xmind.baogang.info"
    BASE_URL = "http://10.81.7.91:30009"

    # 应用管理 -> 应用详情 -> AppID(复制)
    APP_ID = "2741701DAF894C02BA78"
    # 应用管理 -> 应用详情 -> App Secret Key(复制)
    APP_SECRET = "5127A65D9F874DAF86E9FAF002F8AF0C"
    # 应用管理 -> 应用详情 -> 关联数据列表 -> 编码(复制)
    BODY_ID = "xzrbgent6ue6b0btaorwxi5phhbyld3d"
    
    # test_message = "重新为我开户，附件 TOSS_VjowMixCTjpmbGFtZXMsT046Y3pNNkx5OXdiR0Z1WlhSVWFXUXZjR3hoYm1WMEx6SXdNalV3TmpBMkwrVzhnT2FJdCtlWnUraXVzT2locUM1d1pHWT0=_TOSS ，TOSS_VjowMixCTjpmbGFtZXMsT046Y3pNNkx5OXdiR0Z1WlhSVWFXUXZjR3hoYm1WMEx6SXdNalV3TmpBMkwraVFwZVM0bXVhSnArZUZweTVxY0djPQ==_TOSS ,TOSS_VjowMixCTjpmbGFtZXMsT046Y3pNNkx5OXdiR0Z1WlhSVWFXUXZjR3hoYm1WMEx6SXdNalV3TlRBNEwrVzhnT2VscU9pMWhPYVdtUzV3Ym1jPQ==_TOSS"
    test_message = "重新为我开户，附件 TOSS_VjowMixCTjpmbGFtZXMsT046Y3pNNkx5OXdiR0Z1WlhSVWFXUXZjR3hoYm1WMEx6SXdNalV3TmpBMkwrVzhnT2FJdCtlWnUraXVzT2locUM1d1pHWT0=_TOSS"

    # 初始化客户端
    client = FlamesChatClientV2(APP_ID, APP_SECRET, BASE_URL, BODY_ID)
    # 上传文件,获取文件ID, (如果不需要上传文件,忽略此方法)
    # file_id = client.upload("test.txt")
    # 通过"应用管理->应用详情->关联数据列表->协议"详情获取请求参数
    data = {
    "header": {
        "traceId": TRACE_ID,
        "bodyId": BODY_ID,
        "appId": APP_ID,
        "mode": 0
    },
    "payload": {
        "sessionId": "",
        "text": [
            {
                "content": test_message,
                "content_type": "text",
                "role": "user"
            }
        ]
    }
}
    # 生成内容
    client.generate(data)
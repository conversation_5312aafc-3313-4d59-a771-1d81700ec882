import asyncio
from crawl4ai import AsyncWebCrawler

async def simple_test():
    """简单测试爬虫是否工作"""
    print("开始简单测试...")
    
    # 测试一个简单的网站
    test_urls = [
        "https://example.com",
        "https://httpbin.org/html",
        "https://36kr.com/p/3311645093895936"
    ]
    
    async with AsyncWebCrawler(verbose=True) as crawler:
        for url in test_urls:
            print(f"\n正在测试: {url}")
            try:
                result = await crawler.arun(url=url)
                
                print(f"状态码: {result.status_code}")
                print(f"标题: {result.metadata.get('title', 'N/A')}")
                print(f"HTML长度: {len(result.html) if result.html else 0}")
                print(f"清理后内容长度: {len(result.cleaned_html) if result.cleaned_html else 0}")
                
                if result.cleaned_html:
                    print(f"内容预览: {result.cleaned_html[:200]}...")
                    print("✓ 成功获取内容")
                else:
                    print("✗ 未获取到内容")
                    
            except Exception as e:
                print(f"✗ 错误: {e}")

if __name__ == "__main__":
    asyncio.run(simple_test())

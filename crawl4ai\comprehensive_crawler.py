import asyncio
import json
import re
from crawl4ai import Async<PERSON>ebCrawler
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

class SmartCrawler:
    def __init__(self):
        self.crawler = None
    
    async def __aenter__(self):
        self.crawler = AsyncWebCrawler(verbose=True)
        await self.crawler.__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.crawler:
            await self.crawler.__aexit__(exc_type, exc_val, exc_tb)
    
    async def analyze_page(self, url):
        """分析页面结构"""
        print(f"分析页面: {url}")
        
        result = await self.crawler.arun(url=url)
        
        analysis = {
            "status_code": result.status_code,
            "title": result.metadata.get('title', 'N/A'),
            "html_length": len(result.html) if result.html else 0,
            "cleaned_length": len(result.cleaned_html) if result.cleaned_html else 0,
            "has_content": bool(result.cleaned_html),
            "selectors_found": []
        }
        
        if result.html:
            # 检查常见选择器
            selectors = [
                'article', '.article', '#article',
                '.content', '.main-content', '.post-content',
                '.articleDetailContent', '.article-content',
                '.entry-content', '.text-content'
            ]
            
            for selector in selectors:
                if selector.startswith('.'):
                    class_name = selector[1:]
                    if f'class="{class_name}"' in result.html or f"class='{class_name}'" in result.html:
                        analysis["selectors_found"].append(selector)
                elif selector.startswith('#'):
                    id_name = selector[1:]
                    if f'id="{id_name}"' in result.html or f"id='{id_name}'" in result.html:
                        analysis["selectors_found"].append(selector)
                else:
                    if f'<{selector}' in result.html:
                        analysis["selectors_found"].append(selector)
        
        return analysis, result
    
    async def extract_with_strategy(self, url, schema):
        """使用指定策略提取内容"""
        try:
            strategy = JsonCssExtractionStrategy(schema)
            result = await self.crawler.arun(url=url, extraction_strategy=strategy)
            return result.extracted_content
        except Exception as e:
            print(f"策略 {schema['name']} 失败: {e}")
            return None
    
    async def smart_extract(self, url):
        """智能提取内容"""
        print(f"开始智能提取: {url}")
        
        # 1. 分析页面
        analysis, basic_result = await self.analyze_page(url)
        
        print(f"页面分析结果:")
        print(f"  状态码: {analysis['status_code']}")
        print(f"  标题: {analysis['title']}")
        print(f"  HTML长度: {analysis['html_length']}")
        print(f"  清理后长度: {analysis['cleaned_length']}")
        print(f"  找到的选择器: {analysis['selectors_found']}")
        
        if not analysis['has_content']:
            print("❌ 页面没有内容")
            return None
        
        # 2. 根据找到的选择器构建策略
        strategies = []
        
        # 基于找到的选择器创建策略
        for selector in analysis['selectors_found']:
            if 'article' in selector.lower() or 'content' in selector.lower():
                strategies.append({
                    "name": f"strategy_{selector.replace('.', '').replace('#', '')}",
                    "baseSelector": selector,
                    "fields": [
                        {"name": "title", "selector": "h1, h2, h3, .title", "type": "text"},
                        {"name": "content", "selector": "p, div", "type": "text"}
                    ]
                })
        
        # 添加通用策略
        strategies.extend([
            {
                "name": "generic_article",
                "baseSelector": "article",
                "fields": [
                    {"name": "title", "selector": "h1, h2, h3", "type": "text"},
                    {"name": "content", "selector": "p", "type": "text"}
                ]
            },
            {
                "name": "generic_body",
                "baseSelector": "body",
                "fields": [
                    {"name": "headings", "selector": "h1, h2, h3, h4", "type": "text"},
                    {"name": "paragraphs", "selector": "p", "type": "text"}
                ]
            }
        ])
        
        # 3. 尝试各种策略
        for strategy in strategies:
            print(f"\n尝试策略: {strategy['name']}")
            content = await self.extract_with_strategy(url, strategy)
            
            if content and content.strip():
                print(f"✓ 策略 {strategy['name']} 成功")
                try:
                    parsed_content = json.loads(content)
                    if parsed_content and any(parsed_content.values()):
                        return {
                            "strategy_used": strategy['name'],
                            "extracted_data": parsed_content,
                            "raw_content": content
                        }
                except:
                    if content.strip():
                        return {
                            "strategy_used": strategy['name'],
                            "extracted_data": content,
                            "raw_content": content
                        }
        
        # 4. 如果所有策略都失败，返回清理后的文本
        print("\n所有结构化策略都失败，返回清理后的文本")
        
        if basic_result.cleaned_html:
            # 提取标题
            title = analysis['title']
            if title == 'N/A' and basic_result.html:
                title_match = re.search(r'<title[^>]*>([^<]+)</title>', basic_result.html, re.IGNORECASE)
                title = title_match.group(1) if title_match else "未找到标题"
            
            # 简单分段
            text = basic_result.cleaned_html
            paragraphs = [p.strip() for p in text.split('\n') if p.strip() and len(p.strip()) > 20]
            
            return {
                "strategy_used": "fallback_text",
                "extracted_data": {
                    "title": title,
                    "paragraphs": paragraphs[:10],  # 前10段
                    "full_text": text[:5000]  # 前5000字符
                },
                "raw_content": text
            }
        
        return None

async def main():
    url = "https://36kr.com/p/3311645093895936"
    
    async with SmartCrawler() as crawler:
        result = await crawler.smart_extract(url)
        
        if result:
            print("\n" + "="*50)
            print("提取成功!")
            print(f"使用策略: {result['strategy_used']}")
            print("\n提取的数据:")
            
            if isinstance(result['extracted_data'], dict):
                for key, value in result['extracted_data'].items():
                    if isinstance(value, list):
                        print(f"{key}: {len(value)} 项")
                        for i, item in enumerate(value[:3]):  # 只显示前3项
                            print(f"  {i+1}. {str(item)[:100]}...")
                    else:
                        print(f"{key}: {str(value)[:200]}...")
            else:
                print(result['extracted_data'][:500] + "...")
            
            # 保存结果
            with open("smart_extraction_result.json", "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"\n结果已保存到: smart_extraction_result.json")
            
        else:
            print("\n❌ 提取失败")
            print("建议:")
            print("1. 检查URL是否正确")
            print("2. 检查网络连接")
            print("3. 网站可能有反爬虫保护")
            print("4. 尝试其他URL进行测试")

if __name__ == "__main__":
    asyncio.run(main())

<?xml version="1.0" ?>
<testsuite name="FlameAPITester-20250611160132" tests="1" time="2.005" failures="0" errors="1">
	<testcase classname="FlameAPITester" name="test_planning_agent" time="2.005">
		<error type="AssertionError" message="测试过程中发生错误: [('ERROR', 'WebSocket错误: Handshake status 401 Unauthorized -+-+- {\'server\': \'nginx/1.21.5\', \'date\': \'Wed, 11 Jun 2025 08:01:32 GMT\', \'content-type\': \'application/json;charset=UTF-8\', \'content-length\': \'70\', \'connection\': \'keep-alive\', \'cache-control\': \'no-cache, no-store, max-age=0, must-revalidate\', \'pragma\': \'no-cache\', \'expires\': \'0\', \'x-content-type-options\': \'nosniff\', \'x-frame-options\': \'DENY\', \'x-xss-protection\': \'1 ; mode=block\', \'referrer-policy\': \'no-referrer\'} -+-+- b\'{&quot;code&quot;:401,&quot;data&quot;:null,&quot;message&quot;:&quot;\\xe8\\xaf\\xb7\\xe6\\xb1\\x82\\xe6\\x8e\\xa5\\xe5\\x8f\\xa3\\xe6\\x9c\\xaa\\xe6\\x8e\\x88\\xe6\\x9d\\x83\\xe6\\x88\\x96\\xe4\\xb8\\x8d\\xe5\\xad\\x98\\xe5\\x9c\\xa8&quot;}\'')]"><![CDATA[Traceback (most recent call last):
  File "c:\vs_project\test\智能开户\test\test2.py", line 293, in test_planning_agent
    self.fail(f"测试过程中发生错误: {all_responses}")
AssertionError: 测试过程中发生错误: [('ERROR', 'WebSocket错误: Handshake status 401 Unauthorized -+-+- {\'server\': \'nginx/1.21.5\', \'date\': \'Wed, 11 Jun 2025 08:01:32 GMT\', \'content-type\': \'application/json;charset=UTF-8\', \'content-length\': \'70\', \'connection\': \'keep-alive\', \'cache-control\': \'no-cache, no-store, max-age=0, must-revalidate\', \'pragma\': \'no-cache\', \'expires\': \'0\', \'x-content-type-options\': \'nosniff\', \'x-frame-options\': \'DENY\', \'x-xss-protection\': \'1 ; mode=block\', \'referrer-policy\': \'no-referrer\'} -+-+- b\'{"code":401,"data":null,"message":"\\xe8\\xaf\\xb7\\xe6\\xb1\\x82\\xe6\\x8e\\xa5\\xe5\\x8f\\xa3\\xe6\\x9c\\xaa\\xe6\\x8e\\x88\\xe6\\x9d\\x83\\xe6\\x88\\x96\\xe4\\xb8\\x8d\\xe5\\xad\\x98\\xe5\\x9c\\xa8"}\'')]
]]></error>
	</testcase>
	<system-out><![CDATA[开始测试规划型智能体...
构建的payload: {
  "header": {
    "traceId": "TRACE-1749628892-1258ac09",
    "bodyId": "xzrbcess5ht7xw5o2l36x5px9owbscou",
    "appId": "9BC895C708EB440D804C",
    "mode": 0
  },
  "parameter": {},
  "payload": {
    "sessionId": "SESSION_001",
    "text": [
      {
        "content": "开户申请",
        "content_type": "text",
        "role": "user"
      }
    ]
  }
}
signed_str=host: **********
date: Wed, 11 Jun 2025 08:01:32 GMT
GET /openapi/flames/api/v2/chat HTTP/1.1
############hmac api_key="9BC895C708EB440D804C",algorithm="hmac-sha256",headers="host date request-line",signature="8aEvhYddrHSth65erucAsMJz51Iji/ktNYGWHhmh/QE="
############aG1hYyBhcGlfa2V5PSI5QkM4OTVDNzA4RUI0NDBEODA0QyIsYWxnb3JpdGhtPSJobWFjLXNoYTI1NiIsaGVhZGVycz0iaG9zdCBkYXRlIHJlcXVlc3QtbGluZSIsc2lnbmF0dXJlPSI4YUV2aFlkZHJIU3RoNjVlcnVjQXNNSno1MUlqaS9rdE5ZR1dIaG1oL1FFPSI=
------------ws.url---ws://**********:30009/openapi/flames/api/v2/chat?host=**********&date=Wed%2C+11+Jun+2025+08%3A01%3A32+GMT&authorization=aG1hYyBhcGlfa2V5PSI5QkM4OTVDNzA4RUI0NDBEODA0QyIsYWxnb3JpdGhtPSJobWFjLXNoYTI1NiIsaGVhZGVycz0iaG9zdCBkYXRlIHJlcXVlc3QtbGluZSIsc2lnbmF0dXJlPSI4YUV2aFlkZHJIU3RoNjVlcnVjQXNNSno1MUlqaS9rdE5ZR1dIaG1oL1FFPSI%3D&bodyId=xzrbcess5ht7xw5o2l36x5px9owbscou
------------ws.head---[]
WebSocket错误: Handshake status 401 Unauthorized -+-+- {'server': 'nginx/1.21.5', 'date': 'Wed, 11 Jun 2025 08:01:32 GMT', 'content-type': 'application/json;charset=UTF-8', 'content-length': '70', 'connection': 'keep-alive', 'cache-control': 'no-cache, no-store, max-age=0, must-revalidate', 'pragma': 'no-cache', 'expires': '0', 'x-content-type-options': 'nosniff', 'x-frame-options': 'DENY', 'x-xss-protection': '1 ; mode=block', 'referrer-policy': 'no-referrer'} -+-+- b'{"code":401,"data":null,"message":"\xe8\xaf\xb7\xe6\xb1\x82\xe6\x8e\xa5\xe5\x8f\xa3\xe6\x9c\xaa\xe6\x8e\x88\xe6\x9d\x83\xe6\x88\x96\xe4\xb8\x8d\xe5\xad\x98\xe5\x9c\xa8"}'
错误类型: <class 'websocket._exceptions.WebSocketBadStatusException'>
错误状态码: {'code': 401, 'data': None, 'message': '请求接口未授权或不存在'}
### 连接已关闭 ### 状态码: None, 消息: None
等待服务器响应...
收到响应: ('ERROR', 'WebSocket错误: Handshake status 401 Unauthorized -+-+- {\'server\': \'nginx/1.21.5\', \'date\': \'Wed, 11 Jun 2025 08:01:32 GMT\', \'content-type\': \'application/json;charset=UTF-8\', \'content-length\': \'70\', \'connection\': \'keep-alive\', \'cache-control\': \'no-cache, no-store, max-age=0, must-revalidate\', \'pragma\': \'no-cache\', \'expires\': \'0\', \'x-content-type-options\': \'nosniff\', \'x-frame-options\': \'DENY\', \'x-xss-protection\': \'1 ; mode=block\', \'referrer-policy\': \'no-referrer\'} -+-+- b\'{"code":401,"data":null,"message":"\\xe8\\xaf\\xb7\\xe6\\xb1\\x82\\xe6\\x8e\\xa5\\xe5\\x8f\\xa3\\xe6\\x9c\\xaa\\xe6\\x8e\\x88\\xe6\\x9d\\x83\\xe6\\x88\\x96\\xe4\\xb8\\x8d\\xe5\\xad\\x98\\xe5\\x9c\\xa8"}\'')
====================错误==================
错误详情: WebSocket错误: Handshake status 401 Unauthorized -+-+- {'server': 'nginx/1.21.5', 'date': 'Wed, 11 Jun 2025 08:01:32 GMT', 'content-type': 'application/json;charset=UTF-8', 'content-length': '70', 'connection': 'keep-alive', 'cache-control': 'no-cache, no-store, max-age=0, must-revalidate', 'pragma': 'no-cache', 'expires': '0', 'x-content-type-options': 'nosniff', 'x-frame-options': 'DENY', 'x-xss-protection': '1 ; mode=block', 'referrer-policy': 'no-referrer'} -+-+- b'{"code":401,"data":null,"message":"\xe8\xaf\xb7\xe6\xb1\x82\xe6\x8e\xa5\xe5\x8f\xa3\xe6\x9c\xaa\xe6\x8e\x88\xe6\x9d\x83\xe6\x88\x96\xe4\xb8\x8d\xe5\xad\x98\xe5\x9c\xa8"}'

测试结果:
- 总响应数: 1
- 是否有错误: True
- 是否有有效响应: False
- 测试用时: 0.00秒
- 所有响应: [('ERROR', 'WebSocket错误: Handshake status 401 Unauthorized -+-+- {\'server\': \'nginx/1.21.5\', \'date\': \'Wed, 11 Jun 2025 08:01:32 GMT\', \'content-type\': \'application/json;charset=UTF-8\', \'content-length\': \'70\', \'connection\': \'keep-alive\', \'cache-control\': \'no-cache, no-store, max-age=0, must-revalidate\', \'pragma\': \'no-cache\', \'expires\': \'0\', \'x-content-type-options\': \'nosniff\', \'x-frame-options\': \'DENY\', \'x-xss-protection\': \'1 ; mode=block\', \'referrer-policy\': \'no-referrer\'} -+-+- b\'{"code":401,"data":null,"message":"\\xe8\\xaf\\xb7\\xe6\\xb1\\x82\\xe6\\x8e\\xa5\\xe5\\x8f\\xa3\\xe6\\x9c\\xaa\\xe6\\x8e\\x88\\xe6\\x9d\\x83\\xe6\\x88\\x96\\xe4\\xb8\\x8d\\xe5\\xad\\x98\\xe5\\x9c\\xa8"}\'')]
]]></system-out>
	<system-err><![CDATA[]]></system-err>
</testsuite>

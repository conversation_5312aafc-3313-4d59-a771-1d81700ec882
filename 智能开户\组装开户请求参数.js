
function getRegAddrProvinceCode(regAddrProvince) {
    const province_map = new Map([
        ["北京市", "110000"],
        ["天津市", "120000"],
        ["河北省", "130000"],
        ["山西省", "140000"],
        ["内蒙古自治区", "150000"],
        ["辽宁省", "210000"],
        ["吉林省", "220000"],
        ["黑龙江省", "230000"],
        ["上海市", "310000"],
        ["江苏省", "320000"],
        ["浙江省", "330000"],
        ["安徽省", "340000"],
        ["福建省", "350000"],
        ["江西省", "360000"],
        ["山东省", "370000"],
        ["河南省", "410000"],
        ["湖北省", "420000"],
        ["湖南省", "430000"],
        ["广东省", "440000"],
        ["广西壮族自治区", "450000"],
        ["海南省", "460000"],
        ["重庆市", "500000"],
        ["四川省", "510000"],
        ["贵州省", "520000"],
        ["云南省", "530000"],
        ["西藏自治区", "540000"],
        ["陕西省", "610000"],
        ["甘肃省", "620000"],
        ["青海省", "630000"],
        ["宁夏回族自治区", "640000"],
        ["新疆维吾尔自治区", "650000"],
        ["台湾省", "710000"],
        ["香港特别行政区", "810000"],
        ["澳门特别行政区", "820000"]
    ]);

    var province_code = province_map.get(regAddrProvince);
    if (province_code != null) {
        return province_code;
    }
    return "000000";
}

function getBankCode(bankBranchName) {

    const BANK_CODE_MAP = new Map([
        ["宝钢集团财务有限责任公司", "CW"],
        ["中国工商银行", "GS"],
        ["中国建设银行", "JS"],
        ["中国交通银行", "JT"],
        ["中国农业银行", "NY"],
        ["浦东发展银行", "PF"],
        ["中国银行", "ZG"],
        ["招商银行", "ZS"],
        ["澳新银行", "AN"],
        ["美国银行", "BA"],
        ["法国巴黎银行", "BP"],
        ["德国商业银行股份有限公司", "CM"],
        ["花旗银行", "CT"],
        ["德意志银行", "DB"],
        ["中国光大银行", "GD"],
        ["广东发展银行", "GF"],
        ["国家开发银行上海市分行", "GK"],
        ["汇丰银行", "HF"],
        ["华夏银行", "HX"],
        ["中国进出口银行", "JC"],
        ["JP 摩根", "JP"],
        ["中国民生银行", "MS"],
        ["瑞穗实业银行", "MZ"],
        ["南京银行", "NJ"],
        ["农村商业银行", "NS"],
        ["其它", "QT"],
        ["苏格兰皇家银行", "RB"],
        ["渣打银行", "SC"],
        ["深圳发展银行", "SF"],
        ["法国兴业银行", "SG"],
        ["上海银行", "SH"],
        ["日本三井住友银行", "SM"],
        ["三菱东京日联银行", "TM"],
        ["大华银行", "UO"],
        ["兴业银行", "XY"],
        ["星展银行", "XZ"],
        ["中央结算公司", "ZD"],
        ["中信实业银行", "ZX"]
    ]);
    if(bankBranchName!="" && bankBranchName!=undefined){
        // 遍历BANK_CODE_MAP
        for (var [key, value] of BANK_CODE_MAP) {
            if (bankBranchName.indexOf(key) != -1) {
                return value;
            }
        }
    }
    
    
    return "QT";
}

function getResult(status, message, data) {
    return {
        data: {
            status: status,
            message: message,
            data: data
        }
    }
}
function execute(input) {
    
    //A 营业执照
    var business_license_json=null;
    var business_license_result = input.business_license_result;
    if (business_license_result != null && business_license_result != "") {
        business_license_result = JSON.parse(business_license_result);
        if(business_license_result.status!=0){
            return business_license_result;
        }
        business_license_json=business_license_result.data;
    } else {
        return getResult(-10, "营业执照解析失败");

    }
    //B 开户登记表
    var account_registration_json = null;
    var account_registration_result = input.account_registration_result;
    if (account_registration_result != null && account_registration_result != "") {
        account_registration_result = JSON.parse(account_registration_result);
        if(account_registration_result.status!=0){
            return account_registration_result;
        }
        account_registration_json=account_registration_result.data;
    } else {
        return getResult(-20, "开户登记表解析失败");
    }
    //C 开票资料
    var invoice_information_json=null;
    var invoice_information_result = input.invoice_information_result;
    if (invoice_information_result != null && invoice_information_result != "") {
        invoice_information_result = JSON.parse(invoice_information_result);
        if(invoice_information_result.status!=0){
            return invoice_information_result;
        }
        invoice_information_json=invoice_information_result.data;
    }


    var params = Object();

    const date = new Date();
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();


    //###客户基本信息####

    // #####业务单元#####
    //中文全称：对应A名称
    params.chineseUserName = business_license_json["名称"];
    //中文简称：建议暂读取A名称前5个字（再人工修正）
    var shortChineseUserName = business_license_json["名称"];
    var shortChineseUserNameLength = 5;
    if (shortChineseUserName.length > shortChineseUserNameLength) {
        shortChineseUserName = shortChineseUserName.substring(0, shortChineseUserNameLength);
    }
    params.chineseUserAbbr = shortChineseUserName;
    //统一信用代码(税号)：读取A中统一社会信用代码
    params.taxNum = business_license_json["统一社会信用代码"];
    //客户属性建议默认社会企业
    params.userAttr = "S";

    //是否集团公司标志建议否
    params.tabFlag = "0";
    //有效起始日期可默认录入系统时间
    params.activeStartDate = year + month + day;
    var agentStartDate = account_registration_json["委托代理人1授权期限的开始日期"];
    if(agentStartDate
        &&agentStartDate!=""
        && /^\d{8}$/.test(agentStartDate)
    ){
        params.activeStartDate = agentStartDate;
    }
    //截止日期读取B委托代理人1授权期限栏（注：1、若为长期的就建议默认截止时间为********，2、若有多个委托代理人即默认授权期限最长的委托代理人所对应的时间）
    var activeEndDate = account_registration_json["委托代理人1授权期限的结束日期"];
    if (account_registration_json["委托代理人2授权期限的结束日期"] > account_registration_json["委托代理人1授权期限的结束日期"]) {
        activeEndDate = account_registration_json["委托代理人2授权期限的结束日期"];
    }
    params.activeEndDate = activeEndDate;
    // 申请类型: 开户
    params.openingAppFlag="N";
    //申请性质：普通
    params.openingType = "0";
    //国别地区：CHN-中国
    params.countryId = "CHN";
    //行业编码：不细分定义
    params.trade = "00"


    //###地址及银行账号####


    
    // 注册资金 币种
    params.regCurrency="CNY";
    // 注册资金
    params.regCapital = account_registration_json["注册资金"];
    //注册地址(长)：对应A住所
    params.regAddrLong = business_license_json["住所"];
    //注册地址(短)：对应A住所，若字数在系统字数限制范围内则全部对应，字数超系统字数限制，则截取
    var shortRegAddr = business_license_json["住所"];
    var shortRegAddrLength = 30;
    if (shortRegAddr.length > shortRegAddrLength) {
        shortRegAddr = shortRegAddr.substring(0, shortRegAddrLength);
    }
    params.regAddr = shortRegAddr
    //注册省份：读取A住所上省份信息
    var regAddrProvince = business_license_json["住所省份"];
    params.regAddrProvince = getRegAddrProvinceCode(regAddrProvince);
    //联系电话：读取B经营联系地址项对应电话
    params.teleNum = account_registration_json["经营联系地址电话"]
    //公司联系地址(长)：读取B经营联系地址
    params.addressReLong = account_registration_json["经营联系地址"]
    //公司联系地址(短)：读取B经营联系地址，若字数在系统字数限制范围内则全部对应，字数超系统字数限制，则截取
    var shortAddressRe = account_registration_json["经营联系地址"];
    var shortAddressReLength = 30;

    if (shortAddressRe.length > shortAddressReLength) {
        shortAddressRe = shortAddressRe.substring(0, shortAddressReLength);
    }
    params.addressRe = shortAddressRe
    //公司联系人邮编：读取B经营联系地址项对应邮编
    params.postCodeRe = account_registration_json["经营联系地址邮编"];
    //公司联系人：读取B联系人
    params.companyContact = account_registration_json["联系人"];
    //联系人电话：读取B联系人手机号码
    params.teleNumRe = account_registration_json["联系人手机号码"];
    //公司联系人传真：读取B经营联系地址项对应传真
    params.faxNumRe = account_registration_json["经营联系地址传真"];

    //开户银行：有C则读取开户行中的银行名字，若无C则读取B中开户银行中的银行名字
    var bankBranchName = "";
    if (invoice_information_json && invoice_information_json["开户银行"] && invoice_information_json["开户银行"] != "") {
        bankBranchName = invoice_information_json["开户银行"];
    }
    if (bankBranchName == "") {
        bankBranchName = account_registration_json["开户银行名称"];
    }
    params.bankBranchName = bankBranchName;

    //银行代码：有C则读取开户行中的银行名字对应的代码，若无C则读取B中开户银行中的银行名字对应的代码；如开户银行在IMC下拉值集中找不到精准匹配的银行，则默认为“其它”

    var bankCode = getBankCode(bankBranchName);
    params.bankCode = bankCode;

    //银行开户行账号：有C则读取C中账号或账户信息，若无C则读取B中账号或账户信息
    var accountNum = "";
    if (invoice_information_json && invoice_information_json["开户银行账号"] && invoice_information_json["开户银行账号"] != "") {
        accountNum = invoice_information_json["开户银行账号"];
    }
    if (accountNum == "") {
        accountNum = account_registration_json["开户银行账号"];
    }
    params.accountNum = accountNum;
    //账号名称：对应A名称
    params.accountName = business_license_json["名称"];
    //发票电话：有C则读取C中对应电话信息，若无C则读取B中开票电话信息
    var invoicePhone = "";
    if (invoice_information_json && invoice_information_json["电话"] && invoice_information_json["电话"] != "") {
        invoicePhone = invoice_information_json["电话"];
    }
    if (invoicePhone == "") {
        invoicePhone = account_registration_json["开票地址开票电话"];
    }
    params.invoicePhone = invoicePhone;
    //发票地址(长)：有C则读取C中对应地址信息，若无C则读取B中开票地址信息
    var invoiceAddrLong = "";
    if (invoice_information_json && invoice_information_json["地址"] && invoice_information_json["地址"] != "") {
        invoiceAddrLong = invoice_information_json["地址"];
    }
    if (invoiceAddrLong == "") {
        invoiceAddrLong = account_registration_json["开票地址"];
    }
    params.invoiceAddrLong = invoiceAddrLong;
    //发票地址(短)：有C则读取C地址，若字数在系统字数限制范围内则全部对应，字数超系统字数限制，则截取；无C则读取B中开票地址信息，若字数在系统字数限制范围内则全部对应，字数超系统字数限制，则截取
    var invoiceAddr = invoiceAddrLong;
    var invoiceAddrLength = 30;
    if (invoiceAddr.length > invoiceAddrLength) {
        invoiceAddr = invoiceAddr.substring(0, invoiceAddrLength);
    }
    params.invoiceAddr = invoiceAddr;
    //银行地区码：有C则对应开户行中的银行对应地区的代码，若无C则读取开户银行中的对应地区的代码

    var areaCodeName = "";
    if (invoice_information_json && invoice_information_json["开户银行所属省"] && invoice_information_json["开户银行所属省"] != "") {
        areaCodeName = invoice_information_json["开户银行所属省"];
    }
    if (areaCodeName == "") {
        areaCodeName = account_registration_json["开户银行所属省"];
    }
    //=========#####====开户银行所属省一般会和注册地址省一致===#####===
    if(areaCodeName==""){
        areaCodeName=regAddrProvince;
    }

    params.areaCode = getRegAddrProvinceCode(areaCodeName);
    //#####专属信息######
    //客户分类默认：111代码
    params.customerPropertyId = "111"
    //保险类型默认：代码号A
    params.insuranceType = "A";
    //营销员工号：暂需人工来进行确认 todo
    params.salesPersId = "";
    //发票寄送收件人：读取B中收件人信息
    params.invoiceReceiveMan = account_registration_json["发票传递方式邮寄收件人"];
    //发票寄送联系电话：若资料B中-寄往（注册地/经营地）项有电话的话则读取该电话，若无则读取B中经营联系地址（发票邮寄地址）项对应电话
    var invoiceReceiveTel = account_registration_json["发票传递方式邮寄电话"];
    if (invoiceReceiveTel == "") {
        invoiceReceiveTel = account_registration_json["经营联系地址电话"];
    }
    params.invoiceReceiveTel = invoiceReceiveTel;
    //发票寄送地址：读取B中经营联系地址（发票邮寄地址）
    params.invoiceMailAddress = account_registration_json["经营联系地址"];
    //会员管理人姓名：读取B中授权期限最长的委托代理人
    var memberName = account_registration_json["委托代理人1姓名"];
    if (account_registration_json["委托代理人2授权期限的结束日期"] > account_registration_json["委托代理人1授权期限的结束日期"]) {
        memberName = account_registration_json["委托代理人2姓名"];
    }
    params.memberName = memberName;
    //会员管理人手机号：暂定为若委托代理人1姓名与联系人姓名一致则读取该联系人手机号码
    var memberMobile = "";
    if(account_registration_json["委托代理人1姓名"] == account_registration_json["联系人"]){
        memberMobile = account_registration_json["联系人手机号码"];
    }
    params.memberMobile = memberMobile;
    // 是否前置结算：是
    params.ifSettleMent = "1"
    // 是否分户：是
    params.ifDFlag = "1"

    //#######其他信息######
    // 申请人工号
    params.applyId="";
    // 申请人姓名
    params.applyName="";
    // 申请人电话
    params.applyTel="";
    var data = Object();
    //附件解析后组装的数据
    data.parseParams=params;
    //分析的附件类型
    data.attachemnt={
        "account_registration_file": input.account_registration_file,
        "business_license_file": input.business_license_file,
        "invoice_information_file": input.invoice_information_file
    }
    

    return getResult(0,"",data)
    
}
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background-color: #f0f0f0;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.container {
    /* display: flex; */
    gap: 20px;
    background-color: #fff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.game-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 200px;
}

h1 {
    text-align: center;
    color: #333;
}

.score-container {
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 5px;
}

.next-piece-container {
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 5px;
}

.controls {
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 5px;
    font-size: 14px;
}

#tetris {
    border: 1px solid #000;
    background-color: #111;
}

#next-piece {
    background-color: #111;
    margin-top: 10px;
}

#start-button {
    padding: 10px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

#start-button:hover {
    background-color: #45a049;
}

p {
    margin: 5px 0;
}
